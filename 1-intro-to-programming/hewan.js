// Definisikan superclass Hewan
class Hewan {
  constructor(nama) {
    this.nama = nama;
  }

  bersuara() {
    console.log(`${this.nama} mengeluarkan suara.`);
  }

  bergerak() {
    console.log(`${this.nama} bergerak.`);
  }
}

// Definisikan subclass Ayam yang mewarisi Hewan
class Ayam extends Hewan {
  constructor(nama) {
    // Panggil constructor superclass
    super(nama);
  }

  // Override metode bersuara
  bersuara() {
    console.log(`${this.nama} berkokok! Kukuruyuk!`);
  }

  // Tambahkan metode khusus Ayam
  bertelur() {
    console.log(`${this.nama} sedang bertelur.`);
  }
}

// Contoh penggunaan
const hewanUmum = new Hewan("Binatang Misterius");
hewanUmum.bersuara(); // Output: Binatang Misterius mengeluarkan suara.
hewanUmum.bergerak(); // Output: Binatang Misterius bergerak.

const ayamJago = new Ayam("Ayam Jago");
ayamJago.bersuara(); // Output: Ayam Jago berkokok! Kukuruyuk!
ayamJago.bergerak(); // Output: Ayam Jago bergerak.
ayamJago.bertelur(); // Output: Ayam Jago sedang bertelur.

// Anda bisa mengekspor kelas jika ingin digunakan di modul lain
// module.exports = { Hewan, Ayam }; // Untuk lingkungan Node.js
// export { Hewan, Ayam }; // Untuk lingkungan ES Modules (browser atau Node.js modern)