<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Si<PERSON>lasi <PERSON> Belan<PERSON></title>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; }
    .barang { margin-bottom: 10px; }
    .total { margin-top: 20px; font-weight: bold; }
  </style>
</head>
<body>
  <h1>Keranjang Belanja</h1>
  <div>
    <input type="text" id="nama" placeholder="Nama Barang">
    <input type="number" id="harga" placeholder="Harga">
    <button onclick="keranjang_saya.tambah_barang(document.getElementById('nama').value, parseInt(document.getElementById('harga').value))">Tambah Barang</button>
  </div>
  <div id="daftar-barang"></div>
  <div class="total" id="total"></div>

  <script>
    class Keranjang {
      constructor() {
        this.daftar_barang = [];
      }

      buat_id_unik() {
        return '_' + Math.random().toString(36).substr(2, 9);
      }

      tambah_barang(nama, harga) {
        const barang_baru = {
          id: this.buat_id_unik(),
          nama: nama,
          harga: harga,
          jumlah: 1
        };
        this.daftar_barang.push(barang_baru);
        this.perbarui_tampilan();
      }

      tambah_jumlah(id_barang) {
        this.daftar_barang.forEach(barang => {
          if (barang.id === id_barang) {
            barang.jumlah += 1;
          }
        });
        this.perbarui_tampilan();
      }

      kurangi_jumlah(id_barang) {
        this.daftar_barang.forEach(barang => {
          if (barang.id === id_barang && barang.jumlah > 1) {
            barang.jumlah -= 1;
          }
        });
        this.perbarui_tampilan();
      }

      hapus_barang(id_barang) {
        this.daftar_barang = this.daftar_barang.filter(barang => barang.id !== id_barang);
        this.perbarui_tampilan();
      }

      hitung_total() {
        return this.daftar_barang.reduce((total, barang) => {
          return total + (barang.harga * barang.jumlah);
        }, 0);
      }

      perbarui_tampilan() {
        const container = document.getElementById("daftar-barang");
        container.innerHTML = "";
        this.daftar_barang.forEach(barang => {
          const div = document.createElement("div");
          div.className = "barang";
          div.innerHTML = `
            ${barang.nama} (Rp${barang.harga}) x ${barang.jumlah}
            <button onclick="keranjang_saya.tambah_jumlah('${barang.id}')">+</button>
            <button onclick="keranjang_saya.kurangi_jumlah('${barang.id}')">-</button>
            <button onclick="keranjang_saya.hapus_barang('${barang.id}')">Hapus</button>
          `;
          container.appendChild(div);
        });
        document.getElementById("total").innerText = "Total: Rp" + this.hitung_total();
      }
    }

    const keranjang_saya = new Keranjang();
    keranjang_saya.tambah_barang("Never Split The Difference", 95000);
    keranjang_saya.tambah_barang("Sirah Rasulullah", 125000);
  </script>
</body>
</html>
