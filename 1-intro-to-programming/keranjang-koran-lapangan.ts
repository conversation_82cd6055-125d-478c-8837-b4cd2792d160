/**
 * Class KeranjangBelanja merepresentasikan keranjang belanja sederhana.
 */
class KeranjangBelanja {
  /**
   * Membuat instance KeranjangBelanja baru.
   * @param {string} pemilik - Nama pemilik keranjang.
   */
  constructor(pemilik) {
    // Atribut 1: Menyimpan nama pemilik keranjang
    this.pemilik = pemilik;
    // Atribut 2: Menyimpan daftar produk dalam bentuk array
    this.items = [];
  }

  /**
   * Metode 1: Menambahkan produk ke dalam keranjang.
   * @param {string} produk - Nama produk yang akan ditambahkan.
   */
  tambahProduk(produk) {
    this.items.push(produk);
    console.log(`"${produk}" telah ditambahkan ke keranjang ${this.pemilik}.`);
  }

  /**
   * Metode 2: Menghapus produk dari keranjang.
   * @param {string} produk - Nama produk yang akan dihapus.
   */
  hapusProduk(produk) {
    const indexProduk = this.items.indexOf(produk);
    if (indexProduk > -1) {
      this.items.splice(indexProduk, 1); // Hapus 1 item pada index tersebut
      console.log(`"${produk}" telah dihapus dari keranjang ${this.pemilik}.`);
    } else {
      console.log(`"${produk}" tidak ditemukan di keranjang ${this.pemilik}.`);
    }
  }

  /**
   * Metode 3: Menampilkan isi keranjang saat ini.
   */
  lihatKeranjang() {
    console.log(`\n--- Isi Keranjang Milik ${this.pemilik} ---`);
    if (this.items.length === 0) {
      console.log("Keranjang masih kosong.");
    } else {
      console.log("Daftar produk:");
      this.items.forEach((item, index) => {
        console.log(`${index + 1}. ${item}`);
      });
    }
    console.log("---------------------------------\n");
  }
}

// Contoh Penggunaan Class KeranjangBelanja

// Membuat objek keranjang baru untuk Budi
const keranjangBudi = new KeranjangBelanja("Budi");

// Menambahkan beberapa produk
keranjangBudi.tambahProduk("Buku Tulis");
keranjangBudi.tambahProduk("Pensil");
keranjangBudi.tambahProduk("Penghapus");

// Melihat isi keranjang Budi
keranjangBudi.lihatKeranjang();

// Menghapus satu produk
keranjangBudi.hapusProduk("Pensil");

// Mencoba menghapus produk yang tidak ada
keranjangBudi.hapusProduk("Penggaris");

// Melihat isi keranjang Budi lagi
keranjangBudi.lihatKeranjang();

// Membuat objek keranjang lain untuk Ani
const keranjangAni = new KeranjangBelanja("Ani");
keranjangAni.tambahProduk("Apel");
keranjangAni.lihatKeranjang();