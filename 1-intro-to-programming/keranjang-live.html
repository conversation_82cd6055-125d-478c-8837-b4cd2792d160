<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON><PERSON><PERSON></title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 40px;
      background-color: #f9f9f9;
    }
    h1 {
      text-align: center;
      color: #333;
    }
    .container {
      max-width: 700px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    .barang {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #ddd;
    }
    .barang:last-child {
      border-bottom: none;
    }
    .actions button {
      margin: 0 5px;
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      background-color: #4caf50;
      color: white;
      cursor: pointer;
    }
    .actions button:hover {
      background-color: #45a049;
    }
    .hapus {
      background-color: #f44336;
    }
    .hapus:hover {
      background-color: #d32f2f;
    }
    .total {
      font-weight: bold;
      text-align: right;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <h1>Keranjang Belanja</h1>
  <div class="container">
    <div id="daftar-barang"></div>
    <div class="total" id="total"></div>
  </div>

  <script>
    let daftar_barang = [
      { id: 1, nama: "Never Split The Difference", harga: 95000, jumlah: 1 },
      { id: 2, nama: "Sirah Rasulullah", harga: 125000, jumlah: 1 }
    ];

    function hitung_total() {
      let total = 0;
      for (const barang of daftar_barang) {
        total += barang.harga * barang.jumlah;
      }
      return total;
    }

    function tambah_jumlah(id_barang) {
      for (const barang of daftar_barang) {
        if (barang.id === id_barang) {
          barang.jumlah += 1;
        }
      }
      perbarui_tampilan();
    }

    function kurangi_jumlah(id_barang) {
      for (const barang of daftar_barang) {
        if (barang.id === id_barang) {
          if (barang.jumlah > 1) {
            barang.jumlah -= 1;
          }
        }
      }
      perbarui_tampilan();
    }

    function hapus_barang(id_barang) {
      daftar_barang = daftar_barang.filter(barang => barang.id !== id_barang);
      perbarui_tampilan();
    }

    function perbarui_tampilan() {
      const daftarElem = document.getElementById("daftar-barang");
      daftarElem.innerHTML = "";

      daftar_barang.forEach(barang => {
        const div = document.createElement("div");
        div.className = "barang";
        div.innerHTML = `
          <div>
            <strong>${barang.nama}</strong><br />
            Rp${barang.harga.toLocaleString()} x ${barang.jumlah} = <strong>Rp${(barang.harga * barang.jumlah).toLocaleString()}</strong>
          </div>
          <div class="actions">
            <button onclick="tambah_jumlah(${barang.id})">+</button>
            <button onclick="kurangi_jumlah(${barang.id})">-</button>
            <button class="hapus" onclick="hapus_barang(${barang.id})">Hapus</button>
          </div>
        `;
        daftarElem.appendChild(div);
      });

      const total = hitung_total();
      document.getElementById("total").innerText = `Total: Rp${total.toLocaleString()}`;
    }

    perbarui_tampilan();
  </script>
</body>
</html>
