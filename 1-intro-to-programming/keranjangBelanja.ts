// keranjangBelanja.js

class Produk {
  constructor(nama, harga) {
    this.nama = nama;
    this.harga = harga;
  }
}

class KeranjangBelanja {
  constructor() {
    this.items = new Map(); // Menggunakan Map untuk menyimpan produk dan kuantitasnya {produk: kuantitas}
  }

  /**
   * Menambahkan produk ke keranjang atau menambah kuantitas jika sudah ada.
   * @param {Produk} produk - Objek produk yang akan ditambahkan.
   * @param {number} kuantitas - Jumlah produk yang ditambahkan.
   */
  tambahProduk(produk, kuantitas = 1) {
    if (!(produk instanceof Produk)) {
      console.error("Error: Hanya objek Produk yang bisa ditambahkan.");
      return;
    }
    if (kuantitas <= 0) {
        console.error("Error: Kuantitas harus lebih besar dari 0.");
        return;
    }

    if (this.items.has(produk.nama)) {
      this.items.set(produk.nama, { produk: produk, kuantitas: this.items.get(produk.nama).kuantitas + kuantitas });
    } else {
      this.items.set(produk.nama, { produk: produk, kuantitas: kuantitas });
    }
    console.log(`${kuantitas} ${produk.nama} ditambahkan ke keranjang.`);
  }

  /**
   * Menghapus produk sepenuhnya dari keranjang.
   * @param {string} namaProduk - Nama produk yang akan dihapus.
   */
  hapusProduk(namaProduk) {
    if (this.items.has(namaProduk)) {
      const itemDihapus = this.items.get(namaProduk);
      this.items.delete(namaProduk);
      console.log(`${itemDihapus.kuantitas} ${namaProduk} dihapus dari keranjang.`);
    } else {
      console.log(`Produk "${namaProduk}" tidak ditemukan di keranjang.`);
    }
  }

  /**
   * Mengurangi kuantitas produk di keranjang. Jika kuantitas menjadi 0, produk dihapus.
   * @param {string} namaProduk - Nama produk yang kuantitasnya akan dikurangi.
   * @param {number} kuantitas - Jumlah yang akan dikurangi.
   */
  kurangiProduk(namaProduk, kuantitas = 1) {
     if (kuantitas <= 0) {
        console.error("Error: Kuantitas untuk dikurangi harus lebih besar dari 0.");
        return;
    }
    if (this.items.has(namaProduk)) {
      const item = this.items.get(namaProduk);
      if (item.kuantitas > kuantitas) {
        item.kuantitas -= kuantitas;
        this.items.set(namaProduk, item);
        console.log(`Kuantitas ${namaProduk} dikurangi ${kuantitas}. Sisa: ${item.kuantitas}`);
      } else if (item.kuantitas === kuantitas) {
        this.hapusProduk(namaProduk); // Hapus jika kuantitas jadi 0
      } else {
          console.log(`Tidak bisa mengurangi ${kuantitas} ${namaProduk}, hanya ada ${item.kuantitas} di keranjang.`);
      }
    } else {
      console.log(`Produk "${namaProduk}" tidak ditemukan di keranjang.`);
    }
  }

  /**
   * Menghitung total harga semua item di keranjang.
   * @returns {number} - Total harga keranjang.
   */
  hitungTotalHarga() {
    let total = 0;
    for (const item of this.items.values()) {
      total += item.produk.harga * item.kuantitas;
    }
    return total;
  }

  /**
   * Menampilkan isi keranjang.
   */
  lihatKeranjang() {
    if (this.items.size === 0) {
      console.log("Keranjang belanja kosong.");
      return;
    }
    console.log("Isi Keranjang Belanja:");
    for (const [nama, item] of this.items) {
      console.log(`- ${item.produk.nama} (${item.kuantitas} x Rp${item.produk.harga})`);
    }
    console.log(`Total Harga: Rp${this.hitungTotalHarga()}`);
  }
}

// Contoh Penggunaan
const keranjang = new KeranjangBelanja();
const apel = new Produk("Apel", 5000);
const pisang = new Produk("Pisang", 3000);
const jeruk = new Produk("Jeruk", 4000);

keranjang.tambahProduk(apel, 3);
keranjang.tambahProduk(pisang, 5);
keranjang.tambahProduk(apel, 2); // Tambah apel lagi
keranjang.tambahProduk(jeruk, 1);

keranjang.lihatKeranjang();
// Output:
// Isi Keranjang Belanja:
// - Apel (5 x Rp5000)
// - Pisang (5 x Rp3000)
// - Jeruk (1 x Rp4000)
// Total Harga: Rp44000

console.log("\nMengurangi 2 pisang...");
keranjang.kurangiProduk("Pisang", 2);
keranjang.lihatKeranjang();
// Output:
// Isi Keranjang Belanja:
// - Apel (5 x Rp5000)
// - Pisang (3 x Rp3000)
// - Jeruk (1 x Rp4000)
// Total Harga: Rp38000

console.log("\nMenghapus Jeruk...");
keranjang.hapusProduk("Jeruk");
keranjang.lihatKeranjang();
// Output:
// Isi Keranjang Belanja:
// - Apel (5 x Rp5000)
// - Pisang (3 x Rp3000)
// Total Harga: Rp34000

console.log("\nMencoba mengurangi 4 pisang (hanya ada 3)...");
keranjang.kurangiProduk("Pisang", 4);
keranjang.lihatKeranjang();
// Output:
// Tidak bisa mengurangi 4 Pisang, hanya ada 3 di keranjang.
// Isi Keranjang Belanja:
// - Apel (5 x Rp5000)
// - Pisang (3 x Rp3000)
// Total Harga: Rp34000

console.log("\nMengurangi 3 pisang (menghapus pisang)...");
keranjang.kurangiProduk("Pisang", 3);
keranjang.lihatKeranjang();
// Output:
// 3 Pisang dihapus dari keranjang.
// Isi Keranjang Belanja:
// - Apel (5 x Rp5000)
// Total Harga: Rp25000