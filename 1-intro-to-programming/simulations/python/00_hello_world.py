"""
SIMULASI HELLO WORLD DAN PROGRAM PERTAMA
========================================

Hello World adalah program pertama yang biasanya dipelajari dalam
pemrograman. Program ini mendemonstrasikan konsep dasar output dan
struktur program sederhana.

Konsep yang dipelajari:
1. STRUKTUR PROGRAM: Bagaimana program Python diorganisir
2. OUTPUT: Menampilkan teks ke layar menggunakan print()
3. COMMENTS: Dokumentasi dalam kode
4. STRING LITERALS: Teks dalam tanda kutip
5. BASIC SYNTAX: Aturan penulisan kode Python

Hello World penting karena:
- Memverifikasi bahwa Python terinstall dengan benar
- Memperkenalkan syntax dasar
- Memberikan kepercayaan diri untuk pemula
- Tradisi dalam dunia pemrograman
"""

from datetime import datetime

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_hello_world():
    """Demonstrasi program Hello World"""
    print("HELLO WORLD adalah program pertama dalam pembelajaran pemrograman.")
    print("Mari kita mulai dengan yang paling sederhana!")
    
    print("\n1. HELLO WORLD SEDERHANA:")
    print("   Kode:")
    print("   print('Hello, World!')")
    print("   ")
    print("   Output:")
    print("   Hello, World!")
    
    print("\n2. VARIASI HELLO WORLD:")
    
    # Basic hello world
    print("   print('Hello, World!')")
    print("   →", end=" ")
    print('Hello, World!')
    
    # With variables
    message = "Hello, Python!"
    print("   message = 'Hello, Python!'")
    print("   print(message)")
    print("   →", end=" ")
    print(message)
    
    # With user input simulation
    name = "Alice"  # Simulasi input
    print("   name = 'Alice'  # Simulasi input")
    print("   print('Hello,', name)")
    print("   →", end=" ")
    print('Hello,', name)
    
    # With f-strings
    print("   print(f'Hello, {name}!')")
    print("   →", end=" ")
    print(f'Hello, {name}!')
    
    print("\n3. HELLO WORLD DALAM BERBAGAI BAHASA:")
    greetings = {
        "English": "Hello, World!",
        "Spanish": "¡Hola, Mundo!",
        "French": "Bonjour, le Monde!",
        "German": "Hallo, Welt!",
        "Italian": "Ciao, Mondo!",
        "Portuguese": "Olá, Mundo!",
        "Russian": "Привет, мир!",
        "Japanese": "こんにちは、世界！",
        "Chinese": "你好，世界！",
        "Arabic": "مرحبا بالعالم!",
        "Indonesian": "Halo, Dunia!"
    }
    
    for language, greeting in greetings.items():
        print(f"   {language:12}: {greeting}")

def demonstrate_print_function():
    """Demonstrasi fungsi print() dan variasinya"""
    print("FUNGSI print() adalah cara utama untuk menampilkan output di Python.")
    
    print("\n1. BASIC PRINT:")
    print("   print('Hello')")
    print("   →", end=" ")
    print('Hello')
    
    print("\n2. PRINT MULTIPLE VALUES:")
    print("   print('Hello', 'World', 123)")
    print("   →", end=" ")
    print('Hello', 'World', 123)
    
    print("\n3. PRINT DENGAN SEPARATOR:")
    print("   print('A', 'B', 'C', sep='-')")
    print("   →", end=" ")
    print('A', 'B', 'C', sep='-')
    
    print("   print('2024', '12', '25', sep='/')")
    print("   →", end=" ")
    print('2024', '12', '25', sep='/')
    
    print("\n4. PRINT DENGAN END PARAMETER:")
    print("   print('Hello', end=' ')")
    print("   print('World')")
    print("   → Hello World")
    
    print("\n5. PRINT DENGAN ESCAPE CHARACTERS:")
    print("   print('Line 1\\nLine 2')")
    print("   →")
    print('Line 1\nLine 2')
    
    print("   print('Tab\\tSeparated\\tValues')")
    print("   →")
    print('Tab\tSeparated\tValues')
    
    print("   print('Quote: \"Hello, World!\"')")
    print("   →", end=" ")
    print('Quote: "Hello, World!"')

def demonstrate_comments():
    """Demonstrasi penggunaan comments"""
    print("COMMENTS adalah teks yang diabaikan oleh Python interpreter.")
    print("Comments digunakan untuk dokumentasi dan penjelasan kode.")
    
    print("\n1. SINGLE LINE COMMENTS:")
    print("   # Ini adalah comment")
    print("   print('Hello')  # Comment di akhir baris")
    print("   ")
    # Ini adalah comment
    print('Hello')  # Comment di akhir baris
    
    print("\n2. MULTI-LINE COMMENTS:")
    print('   """')
    print('   Ini adalah multi-line comment')
    print('   yang bisa mencakup beberapa baris')
    print('   """')
    
    """
    Ini adalah multi-line comment
    yang bisa mencakup beberapa baris
    """
    
    print("\n3. DOCSTRING (DOCUMENTATION STRING):")
    print("   def my_function():")
    print('       """Ini adalah docstring untuk fungsi"""')
    print("       pass")
    
    def my_function():
        """Ini adalah docstring untuk fungsi"""
        pass
    
    print(f"   my_function.__doc__ = '{my_function.__doc__}'")
    
    print("\n4. BEST PRACTICES UNTUK COMMENTS:")
    print("   ✓ Jelaskan MENGAPA, bukan APA")
    print("   ✓ Update comments ketika kode berubah")
    print("   ✓ Gunakan bahasa yang jelas dan ringkas")
    print("   ✓ Hindari comment yang obvious")
    print("   ✗ Jangan comment kode yang sudah jelas")

def demonstrate_program_structure():
    """Demonstrasi struktur program Python"""
    print("STRUKTUR PROGRAM PYTHON memiliki komponen-komponen penting.")
    
    print("\n1. KOMPONEN PROGRAM PYTHON:")
    print("   ┌─────────────────────────────────┐")
    print("   │ 1. Shebang (opsional)          │")
    print("   │ 2. Encoding declaration        │")
    print("   │ 3. Module docstring            │")
    print("   │ 4. Import statements           │")
    print("   │ 5. Global variables/constants  │")
    print("   │ 6. Function definitions        │")
    print("   │ 7. Class definitions           │")
    print("   │ 8. Main execution block        │")
    print("   └─────────────────────────────────┘")
    
    print("\n2. CONTOH STRUKTUR LENGKAP:")
    print("   #!/usr/bin/env python3")
    print("   # -*- coding: utf-8 -*-")
    print('   """')
    print("   Module docstring: Deskripsi program")
    print('   """')
    print("   ")
    print("   import sys")
    print("   from datetime import datetime")
    print("   ")
    print("   # Global constants")
    print("   VERSION = '1.0.0'")
    print("   ")
    print("   def main():")
    print('       """Main function"""')
    print("       print('Hello, World!')")
    print("   ")
    print("   if __name__ == '__main__':")
    print("       main()")
    
    print("\n3. INDENTATION (SANGAT PENTING!):")
    print("   Python menggunakan indentation untuk menentukan blok kode")
    print("   ")
    print("   ✓ Gunakan 4 spasi per level indentation")
    print("   ✓ Konsisten dalam satu file")
    print("   ✗ Jangan campur tabs dan spaces")
    
    print("\n4. CASE SENSITIVITY:")
    print("   Python membedakan huruf besar dan kecil")
    print("   ")
    print("   variable ≠ Variable ≠ VARIABLE")
    print("   print ≠ Print ≠ PRINT")

def demonstrate_first_programs():
    """Demonstrasi program-program pertama yang berguna"""
    print("PROGRAM-PROGRAM PERTAMA yang berguna untuk dipelajari.")
    
    print("\n1. CALCULATOR SEDERHANA:")
    print("   a = 10")
    print("   b = 5")
    print("   print(f'{a} + {b} = {a + b}')")
    print("   print(f'{a} - {b} = {a - b}')")
    print("   print(f'{a} * {b} = {a * b}')")
    print("   print(f'{a} / {b} = {a / b}')")
    print("   ")
    print("   Output:")
    a = 10
    b = 5
    print(f'   {a} + {b} = {a + b}')
    print(f'   {a} - {b} = {a - b}')
    print(f'   {a} * {b} = {a * b}')
    print(f'   {a} / {b} = {a / b}')
    
    print("\n2. PERSONAL INFORMATION:")
    print("   name = 'Alice'")
    print("   age = 25")
    print("   city = 'Jakarta'")
    print("   print(f'Nama: {name}')")
    print("   print(f'Umur: {age} tahun')")
    print("   print(f'Kota: {city}')")
    print("   ")
    print("   Output:")
    name = 'Alice'
    age = 25
    city = 'Jakarta'
    print(f'   Nama: {name}')
    print(f'   Umur: {age} tahun')
    print(f'   Kota: {city}')
    
    print("\n3. CURRENT DATE AND TIME:")
    print("   from datetime import datetime")
    print("   now = datetime.now()")
    print("   print(f'Sekarang: {now}')")
    print("   print(f'Tanggal: {now.strftime(\"%Y-%m-%d\")}')")
    print("   print(f'Waktu: {now.strftime(\"%H:%M:%S\")}')")
    print("   ")
    print("   Output:")
    now = datetime.now()
    print(f'   Sekarang: {now}')
    print(f'   Tanggal: {now.strftime("%Y-%m-%d")}')
    print(f'   Waktu: {now.strftime("%H:%M:%S")}')

def interactive_hello_world_lab():
    """Lab interaktif untuk Hello World"""
    print("\nLAB INTERAKTIF HELLO WORLD")
    print("-" * 30)
    
    while True:
        print("\nPilih aktivitas:")
        print("1. Buat pesan personal")
        print("2. Calculator sederhana")
        print("3. Informasi sistem")
        print("4. ASCII Art")
        print("5. Keluar")
        
        choice = input("Pilihan (1-5): ").strip()
        
        if choice == "1":
            print("\n=== PESAN PERSONAL ===")
            name = input("Nama Anda: ").strip() or "Anonymous"
            hobby = input("Hobi Anda: ").strip() or "Programming"
            
            print(f"\n🎉 Hello, {name}!")
            print(f"🎯 Hobi Anda: {hobby}")
            print(f"🐍 Selamat belajar Python!")
            print(f"📅 Tanggal: {datetime.now().strftime('%Y-%m-%d')}")
        
        elif choice == "2":
            print("\n=== CALCULATOR SEDERHANA ===")
            try:
                num1 = float(input("Angka pertama: "))
                num2 = float(input("Angka kedua: "))
                
                print(f"\nHasil perhitungan:")
                print(f"{num1} + {num2} = {num1 + num2}")
                print(f"{num1} - {num2} = {num1 - num2}")
                print(f"{num1} * {num2} = {num1 * num2}")
                if num2 != 0:
                    print(f"{num1} / {num2} = {num1 / num2}")
                else:
                    print(f"{num1} / {num2} = Error (division by zero)")
            except ValueError:
                print("Error: Masukkan angka yang valid!")
        
        elif choice == "3":
            print("\n=== INFORMASI SISTEM ===")
            import sys
            import platform
            
            print(f"Python version: {sys.version}")
            print(f"Platform: {platform.system()} {platform.release()}")
            print(f"Architecture: {platform.architecture()[0]}")
            print(f"Processor: {platform.processor()}")
            print(f"Current time: {datetime.now()}")
        
        elif choice == "4":
            print("\n=== ASCII ART ===")
            print("Pilih ASCII art:")
            print("1. Python logo")
            print("2. Hello World")
            print("3. Smiley face")
            
            art_choice = input("Pilihan (1-3): ").strip()
            
            if art_choice == "1":
                print("""
    🐍 PYTHON
   ╔══════════╗
   ║  ●    ●  ║
   ║     ◡    ║
   ╚══════════╝
                """)
            elif art_choice == "2":
                print("""
 ██   ██ ███████ ██      ██       ██████  
 ██   ██ ██      ██      ██      ██    ██ 
 ███████ █████   ██      ██      ██    ██ 
 ██   ██ ██      ██      ██      ██    ██ 
 ██   ██ ███████ ███████ ███████  ██████  
                """)
            elif art_choice == "3":
                print("""
      😊 HAPPY CODING! 😊
    ╔═══════════════════╗
    ║   ●           ●   ║
    ║                   ║
    ║   \\             /   ║
    ║    \\___________/    ║
    ╚═══════════════════╝
                """)
        
        elif choice == "5":
            print("Terima kasih! Selamat belajar Python! 🐍")
            break
        
        else:
            print("Pilihan tidak valid!")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI HELLO WORLD DAN PROGRAM PERTAMA")
    print("=========================================")
    print("Selamat datang di dunia pemrograman Python!")
    print("Mari mulai dengan program pertama Anda.")
    
    print_separator("HELLO WORLD")
    demonstrate_hello_world()
    
    print_separator("FUNGSI PRINT")
    demonstrate_print_function()
    
    print_separator("COMMENTS")
    demonstrate_comments()
    
    print_separator("STRUKTUR PROGRAM")
    demonstrate_program_structure()
    
    print_separator("PROGRAM PERTAMA")
    demonstrate_first_programs()
    
    print_separator("LAB INTERAKTIF")
    interactive_hello_world_lab()
    
    print("\n" + "="*60)
    print("RINGKASAN:")
    print("- Hello World: Program pertama untuk memulai pemrograman")
    print("- print(): Fungsi untuk menampilkan output")
    print("- Comments: Dokumentasi dalam kode (#, \"\"\", ''')")
    print("- Struktur: Organisasi kode Python yang baik")
    print("- Indentation: Sangat penting dalam Python!")
    print("="*60)

if __name__ == "__main__":
    main()
