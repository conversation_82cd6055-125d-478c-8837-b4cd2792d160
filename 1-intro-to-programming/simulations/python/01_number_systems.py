"""
SIMULASI SISTEM BILANGAN
========================

Sistem bilangan adalah cara untuk merepresentasikan angka menggunakan simbol-simbol tertentu.
<PERSON><PERSON> pemrograman, kita sering bekerja dengan berbagai sistem bilangan:
- Biner (basis 2): menggunakan digit 0 dan 1
- Desimal (basis 10): menggunakan digit 0-9 (yang biasa kita gunakan)
- Heksadesimal (basis 16): menggunakan digit 0-9 dan huruf A-F

Simulasi ini menunjukkan konversi antara sistem bilangan yang berbeda.
"""

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*50)
    print(f" {title} ")
    print("="*50)

def hexa_to_binary_manual(hex_string):
    """
    Konversi manual dari heksadesimal ke biner
    Setiap digit hex dikonversi ke 4 bit biner
    """
    print(f"\nKonversi manual: {hex_string} (hex) ke biner")
    
    # Mapping setiap digit hex ke 4 bit biner
    hex_to_bin_map = {
        '0': '0000', '1': '0001', '2': '0010', '3': '0011',
        '4': '0100', '5': '0101', '6': '0110', '7': '0111',
        '8': '1000', '9': '1001', 'A': '1010', 'B': '1011',
        'C': '1100', 'D': '1101', 'E': '1110', 'F': '1111'
    }
    
    binary_result = ""
    for digit in hex_string.upper():
        if digit in hex_to_bin_map:
            binary_part = hex_to_bin_map[digit]
            binary_result += binary_part
            print(f"  {digit} (hex) = {binary_part} (bin)")
        else:
            print(f"  Error: '{digit}' bukan digit heksadesimal yang valid")
            return None
    
    # Hapus leading zeros
    binary_result = binary_result.lstrip('0') or '0'
    print(f"Hasil: {hex_string} (hex) = {binary_result} (bin)")
    return binary_result

def binary_to_hexa_manual(binary_string):
    """
    Konversi manual dari biner ke heksadesimal
    Setiap 4 bit biner dikonversi ke 1 digit hex
    """
    print(f"\nKonversi manual: {binary_string} (bin) ke heksadesimal")
    
    # Mapping setiap 4 bit biner ke digit hex
    bin_to_hex_map = {
        '0000': '0', '0001': '1', '0010': '2', '0011': '3',
        '0100': '4', '0101': '5', '0110': '6', '0111': '7',
        '1000': '8', '1001': '9', '1010': 'A', '1011': 'B',
        '1100': 'C', '1101': 'D', '1110': 'E', '1111': 'F'
    }
    
    # Pastikan panjang binary adalah kelipatan 4
    while len(binary_string) % 4 != 0:
        binary_string = '0' + binary_string
    
    hex_result = ""
    print("Proses konversi (setiap 4 bit):")
    
    for i in range(0, len(binary_string), 4):
        four_bits = binary_string[i:i+4]
        hex_digit = bin_to_hex_map[four_bits]
        hex_result += hex_digit
        print(f"  {four_bits} (bin) = {hex_digit} (hex)")
    
    # Hapus leading zeros
    hex_result = hex_result.lstrip('0') or '0'
    print(f"Hasil: {binary_string} (bin) = {hex_result} (hex)")
    return hex_result

def demonstrate_builtin_functions():
    """Demonstrasi fungsi built-in Python untuk konversi sistem bilangan"""
    print("\nMenggunakan fungsi built-in Python:")
    
    # Contoh angka
    decimal_num = 255
    print(f"Angka desimal: {decimal_num}")
    
    # Konversi ke berbagai sistem bilangan
    binary = bin(decimal_num)      # Menghasilkan '0b11111111'
    hexadecimal = hex(decimal_num) # Menghasilkan '0xff'
    octal = oct(decimal_num)       # Menghasilkan '0o377'
    
    print(f"Biner: {binary} (dengan prefix '0b')")
    print(f"Heksadesimal: {hexadecimal} (dengan prefix '0x')")
    print(f"Oktal: {octal} (dengan prefix '0o')")
    
    # Tanpa prefix
    print(f"Biner tanpa prefix: {binary[2:]}")
    print(f"Heksadesimal tanpa prefix: {hexadecimal[2:].upper()}")
    
    # Konversi dari string ke desimal
    print("\nKonversi dari string ke desimal:")
    bin_string = "11111111"
    hex_string = "FF"
    
    from_binary = int(bin_string, 2)    # basis 2
    from_hex = int(hex_string, 16)      # basis 16
    
    print(f"'{bin_string}' (biner) = {from_binary} (desimal)")
    print(f"'{hex_string}' (hex) = {from_hex} (desimal)")

def interactive_converter():
    """Konverter interaktif untuk sistem bilangan"""
    print("\n" + "-"*30)
    print("KONVERTER INTERAKTIF")
    print("-"*30)
    
    while True:
        print("\nPilihan konversi:")
        print("1. Heksadesimal ke Biner")
        print("2. Biner ke Heksadesimal")
        print("3. Desimal ke Biner dan Hex")
        print("4. Keluar")
        
        choice = input("Pilih opsi (1-4): ").strip()
        
        if choice == '1':
            hex_input = input("Masukkan angka heksadesimal (tanpa 0x): ").strip()
            try:
                # Validasi input
                int(hex_input, 16)
                hexa_to_binary_manual(hex_input)
                
                # Bandingkan dengan built-in function
                builtin_result = bin(int(hex_input, 16))[2:]
                print(f"Verifikasi dengan built-in: {builtin_result}")
                
            except ValueError:
                print("Error: Input bukan heksadesimal yang valid!")
                
        elif choice == '2':
            bin_input = input("Masukkan angka biner: ").strip()
            try:
                # Validasi input (hanya boleh 0 dan 1)
                if not all(bit in '01' for bit in bin_input):
                    raise ValueError("Hanya boleh mengandung 0 dan 1")
                
                binary_to_hexa_manual(bin_input)
                
                # Bandingkan dengan built-in function
                builtin_result = hex(int(bin_input, 2))[2:].upper()
                print(f"Verifikasi dengan built-in: {builtin_result}")
                
            except ValueError as e:
                print(f"Error: {e}")
                
        elif choice == '3':
            try:
                dec_input = int(input("Masukkan angka desimal: ").strip())
                print(f"Desimal: {dec_input}")
                print(f"Biner: {bin(dec_input)[2:]}")
                print(f"Heksadesimal: {hex(dec_input)[2:].upper()}")
            except ValueError:
                print("Error: Input bukan angka desimal yang valid!")
                
        elif choice == '4':
            print("Terima kasih!")
            break
        else:
            print("Pilihan tidak valid!")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI SISTEM BILANGAN DALAM PYTHON")
    print("=====================================")
    print("Program ini mendemonstrasikan konversi antara sistem bilangan")
    print("yang berbeda: biner, desimal, dan heksadesimal.")
    
    # Demonstrasi konversi manual
    print_separator("KONVERSI HEKSADESIMAL KE BINER")
    hexa_to_binary_manual("A3")
    hexa_to_binary_manual("FF")
    hexa_to_binary_manual("1B4")
    
    print_separator("KONVERSI BINER KE HEKSADESIMAL")
    binary_to_hexa_manual("10100011")
    binary_to_hexa_manual("11111111")
    binary_to_hexa_manual("110110100")
    
    print_separator("FUNGSI BUILT-IN PYTHON")
    demonstrate_builtin_functions()
    
    print_separator("KONVERTER INTERAKTIF")
    interactive_converter()

if __name__ == "__main__":
    main()
