"""
SIMULASI TIPE DATA
==================

Tipe data menentukan jenis nilai yang dapat disimpan dalam variabel
dan operasi apa yang dapat dilakukan pada nilai tersebut.

Python memiliki dua kategori utama tipe data:
1. PRIMITIF (Sederhana): int, float, bool, str
2. COMPOSITE (Kompleks): list, tuple, dict, set, dan lainnya

Tipe data penting karena:
- Menentukan berapa memori yang dibutuhkan
- Menentukan operasi yang bisa dilakukan
- Membantu mencegah error dalam program
"""

import sys
from decimal import Decimal
from fractions import Fraction

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_primitive_types():
    """Demonstrasi tipe data primitif"""
    print("TIPE DATA PRIMITIF adalah tipe data dasar yang built-in dalam Python.")
    print("Setiap variabel primitif menyimpan satu nilai tunggal.")
    
    print("\n1. INTEGER (int) - Bilangan Bulat:")
    # Integer examples
    positif = 42
    negatif = -17
    nol = 0
    besar = 123456789012345678901234567890  # Python mendukung integer tak terbatas
    
    print(f"   Positif: {positif} (tipe: {type(positif).__name__})")
    print(f"   Negatif: {negatif} (tipe: {type(negatif).__name__})")
    print(f"   Nol: {nol} (tipe: {type(nol).__name__})")
    print(f"   Besar: {besar}")
    print(f"   Ukuran integer besar: {sys.getsizeof(besar)} bytes")
    
    # Integer operations
    print("   Operasi integer:")
    print(f"     {positif} + {negatif} = {positif + negatif}")
    print(f"     {positif} * 2 = {positif * 2}")
    print(f"     {positif} // 5 = {positif // 5} (pembagian bulat)")
    print(f"     {positif} % 5 = {positif % 5} (sisa bagi)")
    
    print("\n2. FLOAT - Bilangan Desimal:")
    # Float examples
    pi = 3.14159
    scientific = 1.23e-4  # Scientific notation
    infinity = float('inf')
    not_a_number = float('nan')
    
    print(f"   Pi: {pi} (tipe: {type(pi).__name__})")
    print(f"   Scientific: {scientific} (tipe: {type(scientific).__name__})")
    print(f"   Infinity: {infinity}")
    print(f"   NaN: {not_a_number}")
    
    # Float precision
    print("   Presisi float (hati-hati dengan perhitungan desimal):")
    print(f"     0.1 + 0.2 = {0.1 + 0.2}")
    print(f"     0.1 + 0.2 == 0.3? {0.1 + 0.2 == 0.3}")
    
    # Solusi untuk presisi
    print("   Solusi menggunakan Decimal:")
    d1 = Decimal('0.1')
    d2 = Decimal('0.2')
    print(f"     Decimal('0.1') + Decimal('0.2') = {d1 + d2}")
    
    print("\n3. BOOLEAN (bool) - True/False:")
    benar = True
    salah = False
    
    print(f"   Benar: {benar} (tipe: {type(benar).__name__})")
    print(f"   Salah: {salah} (tipe: {type(salah).__name__})")
    
    # Boolean operations
    print("   Operasi boolean:")
    print(f"     True and False = {True and False}")
    print(f"     True or False = {True or False}")
    print(f"     not True = {not True}")
    
    # Truthiness
    print("   Nilai yang dianggap False:")
    false_values = [False, 0, 0.0, '', [], {}, None]
    for val in false_values:
        print(f"     bool({repr(val)}) = {bool(val)}")
    
    print("\n4. STRING (str) - Teks:")
    nama = "Alice"
    kalimat = 'Python adalah bahasa pemrograman yang mudah'
    multiline = """Ini adalah
string multiline
yang panjang"""
    
    print(f"   Nama: '{nama}' (tipe: {type(nama).__name__})")
    print(f"   Panjang: {len(nama)} karakter")
    print(f"   Kalimat: '{kalimat}'")
    print(f"   Multiline: {repr(multiline)}")
    
    # String operations
    print("   Operasi string:")
    print(f"     '{nama}' + ' Smith' = '{nama + ' Smith'}'")
    print(f"     '{nama}' * 3 = '{nama * 3}'")
    print(f"     '{nama}'.upper() = '{nama.upper()}'")
    print(f"     '{kalimat}'.split() = {kalimat.split()[:3]}... (3 kata pertama)")

def demonstrate_composite_types():
    """Demonstrasi tipe data composite"""
    print("TIPE DATA COMPOSITE dapat menyimpan multiple nilai atau")
    print("memiliki struktur yang lebih kompleks.")
    
    print("\n1. LIST - Koleksi Terurut yang Dapat Diubah:")
    # List examples
    angka = [1, 2, 3, 4, 5]
    campuran = [1, "hello", 3.14, True, [1, 2]]
    kosong = []
    
    print(f"   Angka: {angka} (tipe: {type(angka).__name__})")
    print(f"   Campuran: {campuran}")
    print(f"   Kosong: {kosong}")
    print(f"   Panjang list angka: {len(angka)}")
    
    # List operations
    print("   Operasi list:")
    angka.append(6)
    print(f"     Setelah append(6): {angka}")
    angka.insert(0, 0)
    print(f"     Setelah insert(0, 0): {angka}")
    print(f"     Element ke-2: {angka[2]}")
    print(f"     Slice [1:4]: {angka[1:4]}")
    
    print("\n2. TUPLE - Koleksi Terurut yang Tidak Dapat Diubah:")
    koordinat = (10, 20)
    warna_rgb = (255, 128, 0)
    satu_element = (42,)  # Koma penting untuk tuple dengan satu element
    
    print(f"   Koordinat: {koordinat} (tipe: {type(koordinat).__name__})")
    print(f"   RGB: {warna_rgb}")
    print(f"   Satu element: {satu_element}")
    
    # Tuple unpacking
    x, y = koordinat
    print(f"   Unpacking: x={x}, y={y}")
    
    print("\n3. DICTIONARY (dict) - Koleksi Key-Value:")
    mahasiswa = {
        "nama": "Alice",
        "umur": 20,
        "jurusan": "Informatika",
        "nilai": [85, 90, 78]
    }
    
    print(f"   Mahasiswa: {mahasiswa}")
    print(f"   Nama: {mahasiswa['nama']}")
    print(f"   Keys: {list(mahasiswa.keys())}")
    print(f"   Values: {list(mahasiswa.values())}")
    
    # Dictionary operations
    mahasiswa["semester"] = 3
    print(f"   Setelah tambah semester: {mahasiswa}")
    
    print("\n4. SET - Koleksi Unik Tidak Terurut:")
    angka_unik = {1, 2, 3, 4, 5, 5, 5}  # Duplikat akan dihilangkan
    huruf = set("hello")  # Konversi string ke set
    
    print(f"   Angka unik: {angka_unik}")
    print(f"   Huruf dari 'hello': {huruf}")
    
    # Set operations
    set1 = {1, 2, 3, 4}
    set2 = {3, 4, 5, 6}
    print(f"   Set1: {set1}")
    print(f"   Set2: {set2}")
    print(f"   Union: {set1 | set2}")
    print(f"   Intersection: {set1 & set2}")
    print(f"   Difference: {set1 - set2}")

def demonstrate_type_conversion():
    """Demonstrasi konversi tipe data"""
    print("KONVERSI TIPE DATA memungkinkan kita mengubah satu tipe ke tipe lain.")
    
    print("\n1. KONVERSI KE INTEGER:")
    print(f"   int('123') = {int('123')}")
    print(f"   int(3.14) = {int(3.14)}")  # Memotong bagian desimal
    print(f"   int(True) = {int(True)}")
    print(f"   int(False) = {int(False)}")
    
    try:
        print(f"   int('hello') = {int('hello')}")
    except ValueError as e:
        print(f"   int('hello') = Error: {e}")
    
    print("\n2. KONVERSI KE FLOAT:")
    print(f"   float('3.14') = {float('3.14')}")
    print(f"   float(42) = {float(42)}")
    print(f"   float(True) = {float(True)}")
    
    print("\n3. KONVERSI KE STRING:")
    print(f"   str(123) = '{str(123)}'")
    print(f"   str(3.14) = '{str(3.14)}'")
    print(f"   str([1, 2, 3]) = '{str([1, 2, 3])}'")
    print(f"   str(True) = '{str(True)}'")
    
    print("\n4. KONVERSI KE BOOLEAN:")
    values = [0, 1, '', 'hello', [], [1, 2], {}, {'a': 1}]
    for val in values:
        print(f"   bool({repr(val)}) = {bool(val)}")
    
    print("\n5. KONVERSI KOLEKSI:")
    original_list = [1, 2, 3, 2, 1]
    print(f"   List: {original_list}")
    print(f"   ke Tuple: {tuple(original_list)}")
    print(f"   ke Set: {set(original_list)}")  # Duplikat dihilangkan
    
    original_string = "hello"
    print(f"   String: '{original_string}'")
    print(f"   ke List: {list(original_string)}")
    print(f"   ke Set: {set(original_string)}")

def demonstrate_type_checking():
    """Demonstrasi pengecekan tipe data"""
    print("PENGECEKAN TIPE DATA membantu kita mengetahui tipe suatu variabel.")
    
    # Berbagai variabel dengan tipe berbeda
    variables = [
        42,
        3.14,
        "hello",
        True,
        [1, 2, 3],
        (1, 2),
        {"a": 1},
        {1, 2, 3},
        None
    ]
    
    print("\nMenggunakan type() dan isinstance():")
    for var in variables:
        print(f"   {repr(var):15} -> type: {type(var).__name__:10} -> isinstance(int): {isinstance(var, int)}")
    
    print("\nPengecekan multiple tipe:")
    for var in variables:
        if isinstance(var, (int, float)):
            print(f"   {repr(var)} adalah angka")
        elif isinstance(var, str):
            print(f"   {repr(var)} adalah string")
        elif isinstance(var, (list, tuple)):
            print(f"   {repr(var)} adalah sequence")
        elif isinstance(var, (dict, set)):
            print(f"   {repr(var)} adalah collection")
        else:
            print(f"   {repr(var)} adalah tipe lain")

def demonstrate_memory_usage():
    """Demonstrasi penggunaan memori berbagai tipe data"""
    print("PENGGUNAAN MEMORI berbeda untuk setiap tipe data.")
    
    # Contoh variabel
    samples = [
        (42, "integer"),
        (3.14, "float"),
        ("hello", "string pendek"),
        ("hello" * 100, "string panjang"),
        ([1, 2, 3], "list kecil"),
        (list(range(1000)), "list besar"),
        ({"a": 1, "b": 2}, "dict kecil"),
        ({i: i**2 for i in range(100)}, "dict besar")
    ]
    
    print("\nUkuran memori (dalam bytes):")
    for obj, desc in samples:
        size = sys.getsizeof(obj)
        print(f"   {desc:20}: {size:6} bytes")
    
    print("\nCatatan:")
    print("- Integer kecil (-5 sampai 256) di-cache oleh Python")
    print("- String pendek juga di-cache")
    print("- List dan dict memiliki overhead untuk fleksibilitas")
    print("- Tuple lebih efisien memori daripada list")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI TIPE DATA PYTHON")
    print("=========================")
    print("Program ini mendemonstrasikan berbagai tipe data dalam Python,")
    print("baik primitif maupun composite, beserta operasi-operasinya.")
    
    print_separator("TIPE DATA PRIMITIF")
    demonstrate_primitive_types()
    
    print_separator("TIPE DATA COMPOSITE")
    demonstrate_composite_types()
    
    print_separator("KONVERSI TIPE DATA")
    demonstrate_type_conversion()
    
    print_separator("PENGECEKAN TIPE DATA")
    demonstrate_type_checking()
    
    print_separator("PENGGUNAAN MEMORI")
    demonstrate_memory_usage()
    
    print("\n" + "="*60)
    print("RINGKASAN TIPE DATA:")
    print("PRIMITIF:")
    print("  - int: Bilangan bulat")
    print("  - float: Bilangan desimal")
    print("  - bool: True/False")
    print("  - str: Teks/string")
    print("COMPOSITE:")
    print("  - list: Koleksi terurut, dapat diubah")
    print("  - tuple: Koleksi terurut, tidak dapat diubah")
    print("  - dict: Koleksi key-value")
    print("  - set: Koleksi unik, tidak terurut")
    print("="*60)

if __name__ == "__main__":
    main()
