"""
SIMULASI SINTAKS DAN SEMANTIK
=============================

Sintaks adalah aturan penulisan kode yang benar dalam bahasa pemrograman.
Semantik adalah makna dari kode yang ditulis.

Komponen utama dalam sintaks Python:
1. Reserved Words (Kata Kunci): Kata yang sudah memiliki makna khusus
2. Identifier: Nama yang kita berikan untuk variabel, fungsi, class, dll
3. Literal: <PERSON><PERSON> tetap yang ditulis langsung dalam kode
4. Ekspresi: Kombinasi nilai dan operator yang menghasilkan nilai
5. Pernyataan: Instruksi yang dapat dieksekusi
6. Direktif: Instruksi khusus untuk interpreter
7. Annotation: Petunjuk tipe data (opsional di Python)
"""

import keyword
import sys

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_reserved_words():
    """Demonstrasi kata kunci (reserved words) dalam Python"""
    print("Reserved Words adalah kata-kata yang sudah memiliki makna khusus")
    print("dalam Python dan tidak boleh digunakan sebagai nama variabel.")
    print("\nDaftar semua reserved words dalam Python:")
    
    # Mendapatkan semua keyword Python
    keywords = keyword.kwlist
    
    # Tampilkan dalam format yang rapi
    for i, kw in enumerate(keywords, 1):
        print(f"{i:2d}. {kw:<12}", end="")
        if i % 4 == 0:  # Baris baru setiap 4 kata
            print()
    print()
    
    print(f"\nTotal: {len(keywords)} reserved words")
    
    # Contoh penggunaan beberapa reserved words
    print("\nContoh penggunaan reserved words:")
    print("1. if, else, elif - untuk kondisional")
    print("2. for, while - untuk perulangan")
    print("3. def - untuk mendefinisikan fungsi")
    print("4. class - untuk mendefinisikan class")
    print("5. import, from - untuk mengimpor modul")
    print("6. try, except, finally - untuk error handling")
    
    # Demonstrasi error jika menggunakan reserved word sebagai variabel
    print("\nApa yang terjadi jika kita gunakan reserved word sebagai nama variabel?")
    print("Contoh: if = 5  # Ini akan menyebabkan SyntaxError!")

def demonstrate_identifiers():
    """Demonstrasi identifier (nama variabel, fungsi, dll)"""
    print("Identifier adalah nama yang kita berikan untuk:")
    print("- Variabel")
    print("- Fungsi")
    print("- Class")
    print("- Modul")
    print("- Dan objek lainnya")
    
    print("\nAturan penamaan identifier dalam Python:")
    print("1. Harus dimulai dengan huruf (a-z, A-Z) atau underscore (_)")
    print("2. Karakter selanjutnya boleh huruf, angka, atau underscore")
    print("3. Case-sensitive (huruf besar dan kecil dibedakan)")
    print("4. Tidak boleh menggunakan reserved words")
    print("5. Tidak boleh mengandung spasi atau karakter khusus")
    
    # Contoh identifier yang valid
    print("\nContoh identifier yang VALID:")
    valid_identifiers = [
        "nama", "nama_lengkap", "umur", "nilai1", "nilai_2",
        "_private", "__special__", "NamaClass", "KONSTANTA",
        "camelCase", "snake_case", "x", "y1", "temp_var"
    ]
    
    for identifier in valid_identifiers:
        print(f"  ✓ {identifier}")
    
    # Contoh identifier yang tidak valid
    print("\nContoh identifier yang TIDAK VALID:")
    invalid_examples = [
        ("2nama", "Dimulai dengan angka"),
        ("nama-lengkap", "Mengandung tanda minus"),
        ("nama lengkap", "Mengandung spasi"),
        ("if", "Reserved word"),
        ("nama@email", "Mengandung karakter khusus"),
        ("class", "Reserved word")
    ]
    
    for identifier, reason in invalid_examples:
        print(f"  ✗ {identifier} - {reason}")
    
    # Demonstrasi case-sensitive
    print("\nDemonstrasi case-sensitive:")
    nama = "John"
    Nama = "Jane"
    NAMA = "Bob"
    
    print(f"nama = '{nama}'")
    print(f"Nama = '{Nama}'")
    print(f"NAMA = '{NAMA}'")
    print("Ketiga variabel di atas adalah variabel yang berbeda!")

def demonstrate_literals():
    """Demonstrasi literal (nilai tetap)"""
    print("Literal adalah nilai yang ditulis langsung dalam kode.")
    print("Python memiliki berbagai jenis literal:")
    
    print("\n1. LITERAL NUMERIK:")
    # Integer literals
    print("   Integer (bilangan bulat):")
    print(f"     Desimal: 42, -17, 0")
    print(f"     Biner: 0b1010 = {0b1010}")
    print(f"     Oktal: 0o755 = {0o755}")
    print(f"     Heksadesimal: 0xFF = {0xFF}")
    
    # Float literals
    print("   Float (bilangan desimal):")
    print(f"     3.14, -2.5, 1.0")
    print(f"     Scientific notation: 1e6 = {1e6}, 2.5e-3 = {2.5e-3}")
    
    # Complex literals
    print("   Complex (bilangan kompleks):")
    print(f"     3+4j = {3+4j}")
    print(f"     -2j = {-2j}")
    
    print("\n2. LITERAL STRING:")
    print("   Single quotes: 'Hello World'")
    print("   Double quotes: \"Python Programming\"")
    print("   Triple quotes untuk multiline:")
    multiline = """Ini adalah
string yang
terdiri dari beberapa baris"""
    print(f"   {repr(multiline)}")
    
    # Raw strings
    print("   Raw string (r-string): r'C:\\Users\\<USER>\Users\nama'
    print(f"   Hasil: {raw_path}")
    
    # f-strings
    nama = "Alice"
    umur = 25
    print(f"   f-string: f'Nama: {{nama}}, Umur: {{umur}}'")
    print(f"   Hasil: {f'Nama: {nama}, Umur: {umur}'}")
    
    print("\n3. LITERAL BOOLEAN:")
    print(f"   True = {True}")
    print(f"   False = {False}")
    
    print("\n4. LITERAL KHUSUS:")
    print(f"   None = {None} (mewakili 'tidak ada nilai')")

def demonstrate_expressions():
    """Demonstrasi ekspresi"""
    print("Ekspresi adalah kombinasi nilai dan operator yang menghasilkan nilai.")
    print("Ekspresi dapat dievaluasi untuk menghasilkan hasil.")
    
    print("\nContoh-contoh ekspresi:")
    
    # Ekspresi aritmatika
    print("\n1. EKSPRESI ARITMATIKA:")
    expressions = [
        "5 + 3",
        "10 - 4",
        "6 * 7",
        "15 / 3",
        "17 // 5",  # Floor division
        "17 % 5",   # Modulus
        "2 ** 3"    # Pangkat
    ]
    
    for expr in expressions:
        result = eval(expr)
        print(f"   {expr} = {result}")
    
    # Ekspresi perbandingan
    print("\n2. EKSPRESI PERBANDINGAN:")
    comparisons = [
        "5 > 3",
        "10 <= 10",
        "7 == 7",
        "8 != 9",
        "'abc' < 'def'"
    ]
    
    for expr in comparisons:
        result = eval(expr)
        print(f"   {expr} = {result}")
    
    # Ekspresi logika
    print("\n3. EKSPRESI LOGIKA:")
    x, y = 5, 10
    print(f"   Dengan x = {x}, y = {y}:")
    logic_expressions = [
        "x > 0 and y > 0",
        "x > 10 or y > 5",
        "not (x > y)"
    ]
    
    for expr in logic_expressions:
        result = eval(expr)
        print(f"   {expr} = {result}")
    
    # Ekspresi kompleks
    print("\n4. EKSPRESI KOMPLEKS:")
    complex_expr = "(5 + 3) * 2 - 1"
    result = eval(complex_expr)
    print(f"   {complex_expr} = {result}")
    print("   Urutan evaluasi: (5 + 3) → 8, 8 * 2 → 16, 16 - 1 → 15")

def demonstrate_statements():
    """Demonstrasi pernyataan (statements)"""
    print("Pernyataan adalah instruksi yang dapat dieksekusi oleh Python.")
    print("Berbeda dengan ekspresi, pernyataan melakukan aksi.")
    
    print("\nJenis-jenis pernyataan:")
    
    print("\n1. ASSIGNMENT STATEMENT (Pernyataan Penugasan):")
    print("   x = 10")
    print("   nama = 'Alice'")
    print("   hasil = x + 5")
    
    print("\n2. EXPRESSION STATEMENT:")
    print("   print('Hello')  # Memanggil fungsi")
    print("   len([1, 2, 3])  # Memanggil fungsi (meski hasilnya tidak disimpan)")
    
    print("\n3. COMPOUND STATEMENT (Pernyataan Majemuk):")
    print("   if kondisi:")
    print("       aksi1")
    print("   else:")
    print("       aksi2")
    
    print("\n4. IMPORT STATEMENT:")
    print("   import math")
    print("   from datetime import date")
    
    print("\n5. FUNCTION DEFINITION:")
    print("   def nama_fungsi(parameter):")
    print("       return hasil")
    
    print("\n6. CLASS DEFINITION:")
    print("   class NamaClass:")
    print("       pass")
    
    # Demonstrasi eksekusi pernyataan
    print("\nDemonstrasi eksekusi pernyataan:")
    print("Eksekusi: x = 5")
    x = 5
    print(f"Hasil: x sekarang bernilai {x}")
    
    print("Eksekusi: print('Hello from statement!')")
    print('Hello from statement!')

def demonstrate_directives():
    """Demonstrasi direktif"""
    print("Direktif adalah instruksi khusus untuk interpreter Python.")
    print("Biasanya berupa komentar khusus atau import statement.")
    
    print("\n1. ENCODING DIRECTIVE:")
    print("   # -*- coding: utf-8 -*-")
    print("   Memberitahu Python encoding file ini")
    
    print("\n2. SHEBANG (untuk Unix/Linux):")
    print("   #!/usr/bin/env python3")
    print("   Memberitahu sistem operasi interpreter yang digunakan")
    
    print("\n3. FUTURE IMPORTS:")
    print("   from __future__ import annotations")
    print("   Mengaktifkan fitur dari versi Python yang lebih baru")
    
    print("\n4. DOCSTRING:")
    print('   """Ini adalah docstring"""')
    print("   Dokumentasi untuk modul, class, atau fungsi")
    
    print(f"\nInformasi interpreter saat ini:")
    print(f"   Versi Python: {sys.version}")
    print(f"   Encoding default: {sys.getdefaultencoding()}")

def demonstrate_annotations():
    """Demonstrasi type annotations"""
    print("Type Annotations adalah petunjuk tipe data (opsional di Python).")
    print("Membantu dokumentasi dan tools seperti IDE untuk type checking.")
    
    print("\nContoh function dengan type annotations:")
    
    def tambah(a: int, b: int) -> int:
        """Fungsi untuk menambahkan dua bilangan integer"""
        return a + b
    
    def sapa(nama: str, umur: int = 0) -> str:
        """Fungsi untuk menyapa dengan nama dan umur"""
        if umur > 0:
            return f"Halo {nama}, umur {umur} tahun!"
        return f"Halo {nama}!"
    
    print("def tambah(a: int, b: int) -> int:")
    print("    return a + b")
    print()
    print("def sapa(nama: str, umur: int = 0) -> str:")
    print("    if umur > 0:")
    print("        return f'Halo {nama}, umur {umur} tahun!'")
    print("    return f'Halo {nama}!'")
    
    # Demonstrasi penggunaan
    print("\nPenggunaan:")
    hasil = tambah(5, 3)
    print(f"tambah(5, 3) = {hasil}")
    
    pesan1 = sapa("Alice")
    pesan2 = sapa("Bob", 25)
    print(f"sapa('Alice') = '{pesan1}'")
    print(f"sapa('Bob', 25) = '{pesan2}'")
    
    print("\nType annotations untuk variabel:")
    print("nama: str = 'Alice'")
    print("umur: int = 25")
    print("nilai: list[float] = [85.5, 90.0, 78.5]")
    
    # Contoh dengan variabel
    nama: str = 'Alice'
    umur: int = 25
    nilai: list[float] = [85.5, 90.0, 78.5]
    
    print(f"\nHasil: nama='{nama}', umur={umur}, nilai={nilai}")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI SINTAKS DAN SEMANTIK PYTHON")
    print("====================================")
    print("Program ini mendemonstrasikan komponen-komponen dasar")
    print("dalam sintaks dan semantik bahasa Python.")
    
    print_separator("RESERVED WORDS (KATA KUNCI)")
    demonstrate_reserved_words()
    
    print_separator("IDENTIFIERS (PENAMAAN)")
    demonstrate_identifiers()
    
    print_separator("LITERALS (NILAI TETAP)")
    demonstrate_literals()
    
    print_separator("EXPRESSIONS (EKSPRESI)")
    demonstrate_expressions()
    
    print_separator("STATEMENTS (PERNYATAAN)")
    demonstrate_statements()
    
    print_separator("DIRECTIVES (DIREKTIF)")
    demonstrate_directives()
    
    print_separator("TYPE ANNOTATIONS")
    demonstrate_annotations()
    
    print("\n" + "="*60)
    print("RINGKASAN:")
    print("- Reserved words: Kata kunci yang sudah memiliki makna khusus")
    print("- Identifiers: Nama yang kita berikan untuk variabel, fungsi, dll")
    print("- Literals: Nilai tetap yang ditulis langsung dalam kode")
    print("- Expressions: Kombinasi nilai dan operator yang menghasilkan nilai")
    print("- Statements: Instruksi yang dapat dieksekusi")
    print("- Directives: Instruksi khusus untuk interpreter")
    print("- Annotations: Petunjuk tipe data (opsional)")
    print("="*60)

if __name__ == "__main__":
    main()
