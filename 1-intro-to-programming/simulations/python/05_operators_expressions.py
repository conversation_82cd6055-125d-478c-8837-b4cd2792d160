"""
SIMULASI OPERATOR DAN EKSPRESI
==============================

Operator adalah simbol yang melakukan operasi pada operand (nilai/variabel).
Ekspresi adalah kombinasi operator dan operand yang menghasilkan nilai.

Jenis operator dalam Python:
1. ARITMATIKA: +, -, *, /, //, %, **
2. PERBANDINGAN: ==, !=, <, >, <=, >=
3. LOGIKA: and, or, not
4. ASSIGNMENT: =, +=, -=, *=, /=, dll
5. BITWISE: &, |, ^, ~, <<, >>
6. MEMBERSHIP: in, not in
7. IDENTITY: is, is not
"""

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_arithmetic_operators():
    """Demonstrasi operator aritmatika"""
    print("OPERATOR ARITMATIKA digunakan untuk operasi matematika.")
    
    a, b = 17, 5
    print(f"Dengan a = {a} dan b = {b}:")
    
    print("\n1. OPERASI DASAR:")
    print(f"   a + b = {a} + {b} = {a + b}  (penjumlahan)")
    print(f"   a - b = {a} - {b} = {a - b}  (pengurangan)")
    print(f"   a * b = {a} * {b} = {a * b}  (perkalian)")
    print(f"   a / b = {a} / {b} = {a / b}  (pembagian float)")
    
    print("\n2. OPERASI KHUSUS:")
    print(f"   a // b = {a} // {b} = {a // b}  (floor division - pembagian bulat)")
    print(f"   a % b = {a} % {b} = {a % b}   (modulus - sisa bagi)")
    print(f"   a ** b = {a} ** {b} = {a ** b}  (pangkat)")
    
    print("\n3. OPERATOR UNARY:")
    print(f"   +a = +{a} = {+a}   (unary plus)")
    print(f"   -a = -{a} = {-a}  (unary minus)")
    
    print("\n4. CONTOH PRAKTIS:")
    # Menghitung luas lingkaran
    radius = 7
    pi = 3.14159
    luas = pi * radius ** 2
    print(f"   Luas lingkaran (r={radius}): π × r² = {pi} × {radius}² = {luas}")
    
    # Konversi suhu
    celsius = 25
    fahrenheit = (celsius * 9/5) + 32
    print(f"   Konversi suhu: {celsius}°C = {fahrenheit}°F")
    
    # Menghitung sisa waktu
    total_detik = 3725
    jam = total_detik // 3600
    menit = (total_detik % 3600) // 60
    detik = total_detik % 60
    print(f"   {total_detik} detik = {jam} jam, {menit} menit, {detik} detik")

def demonstrate_comparison_operators():
    """Demonstrasi operator perbandingan"""
    print("OPERATOR PERBANDINGAN membandingkan dua nilai dan menghasilkan boolean.")
    
    x, y = 10, 15
    print(f"Dengan x = {x} dan y = {y}:")
    
    print("\n1. PERBANDINGAN NUMERIK:")
    print(f"   x == y  →  {x} == {y}  →  {x == y}   (sama dengan)")
    print(f"   x != y  →  {x} != {y}  →  {x != y}   (tidak sama dengan)")
    print(f"   x < y   →  {x} < {y}   →  {x < y}    (kurang dari)")
    print(f"   x > y   →  {x} > {y}   →  {x > y}   (lebih dari)")
    print(f"   x <= y  →  {x} <= {y}  →  {x <= y}   (kurang dari atau sama dengan)")
    print(f"   x >= y  →  {x} >= {y}  →  {x >= y}  (lebih dari atau sama dengan)")
    
    print("\n2. PERBANDINGAN STRING:")
    str1, str2 = "apple", "banana"
    print(f"   '{str1}' < '{str2}' = {str1 < str2}  (urutan alfabetis)")
    print(f"   '{str1}' == '{str1}' = {str1 == str1}")
    
    # Case sensitivity
    print(f"   'Apple' == 'apple' = {'Apple' == 'apple'}  (case sensitive)")
    print(f"   'Apple'.lower() == 'apple' = {'Apple'.lower() == 'apple'}")
    
    print("\n3. PERBANDINGAN KOLEKSI:")
    list1, list2 = [1, 2, 3], [1, 2, 4]
    print(f"   {list1} < {list2} = {list1 < list2}  (lexicographic)")
    print(f"   {list1} == {list1} = {list1 == list1}")
    
    print("\n4. CHAINED COMPARISON:")
    nilai = 75
    print(f"   Dengan nilai = {nilai}:")
    print(f"   0 <= nilai <= 100 = {0 <= nilai <= 100}")
    print(f"   60 <= nilai < 80 = {60 <= nilai < 80}")
    print(f"   nilai < 50 or nilai > 90 = {nilai < 50 or nilai > 90}")

def demonstrate_logical_operators():
    """Demonstrasi operator logika"""
    print("OPERATOR LOGIKA menggabungkan atau memodifikasi nilai boolean.")
    
    print("\n1. OPERATOR DASAR:")
    print("   and: True jika kedua operand True")
    print("   or:  True jika salah satu operand True")
    print("   not: Membalik nilai boolean")
    
    print("\n2. TRUTH TABLE:")
    print("   A     | B     | A and B | A or B | not A")
    print("   ------|-------|---------|--------|-------")
    for a in [True, False]:
        for b in [True, False]:
            print(f"   {str(a):5} | {str(b):5} | {str(a and b):7} | {str(a or b):6} | {str(not a):5}")
    
    print("\n3. CONTOH PRAKTIS:")
    umur = 20
    punya_sim = True
    punya_mobil = False
    
    print(f"   umur = {umur}, punya_sim = {punya_sim}, punya_mobil = {punya_mobil}")
    
    bisa_nyetir = umur >= 17 and punya_sim
    print(f"   bisa_nyetir = umur >= 17 and punya_sim = {bisa_nyetir}")
    
    bisa_pergi = bisa_nyetir and punya_mobil
    print(f"   bisa_pergi = bisa_nyetir and punya_mobil = {bisa_pergi}")
    
    perlu_transportasi = not punya_mobil
    print(f"   perlu_transportasi = not punya_mobil = {perlu_transportasi}")
    
    print("\n4. SHORT-CIRCUIT EVALUATION:")
    print("   Python menggunakan short-circuit evaluation:")
    
    def fungsi_a():
        print("     Fungsi A dipanggil")
        return True
    
    def fungsi_b():
        print("     Fungsi B dipanggil")
        return False
    
    print("   False and fungsi_a():")
    result = False and fungsi_a()  # fungsi_a() tidak dipanggil
    print(f"     Hasil: {result}")
    
    print("   True or fungsi_b():")
    result = True or fungsi_b()  # fungsi_b() tidak dipanggil
    print(f"     Hasil: {result}")

def demonstrate_assignment_operators():
    """Demonstrasi operator assignment"""
    print("OPERATOR ASSIGNMENT memberikan nilai ke variabel.")
    
    print("\n1. ASSIGNMENT SEDERHANA:")
    x = 10
    print(f"   x = 10  →  x = {x}")
    
    print("\n2. COMPOUND ASSIGNMENT:")
    print("   Operator ini menggabungkan operasi dengan assignment:")
    
    # Aritmatika
    x += 5  # sama dengan x = x + 5
    print(f"   x += 5   →  x = {x}")
    
    x -= 3  # sama dengan x = x - 3
    print(f"   x -= 3   →  x = {x}")
    
    x *= 2  # sama dengan x = x * 2
    print(f"   x *= 2   →  x = {x}")
    
    x /= 4  # sama dengan x = x / 4
    print(f"   x /= 4   →  x = {x}")
    
    x //= 2  # sama dengan x = x // 2
    print(f"   x //= 2  →  x = {x}")
    
    x %= 3  # sama dengan x = x % 3
    print(f"   x %= 3   →  x = {x}")
    
    x **= 3  # sama dengan x = x ** 3
    print(f"   x **= 3  →  x = {x}")
    
    print("\n3. ASSIGNMENT DENGAN KOLEKSI:")
    # List
    numbers = [1, 2, 3]
    print(f"   numbers = {numbers}")
    
    numbers += [4, 5]  # extend list
    print(f"   numbers += [4, 5]  →  numbers = {numbers}")
    
    numbers *= 2  # repeat list
    print(f"   numbers *= 2  →  numbers = {numbers}")
    
    # String
    greeting = "Hello"
    greeting += " World"
    print(f"   greeting += ' World'  →  greeting = '{greeting}'")
    
    print("\n4. MULTIPLE ASSIGNMENT:")
    a = b = c = 0
    print(f"   a = b = c = 0  →  a={a}, b={b}, c={c}")
    
    # Tuple unpacking
    a, b, c = 1, 2, 3
    print(f"   a, b, c = 1, 2, 3  →  a={a}, b={b}, c={c}")
    
    # Swap variables
    a, b = b, a
    print(f"   a, b = b, a  →  a={a}, b={b} (swap)")

def demonstrate_membership_operators():
    """Demonstrasi operator membership"""
    print("OPERATOR MEMBERSHIP memeriksa apakah nilai ada dalam koleksi.")
    
    print("\n1. OPERATOR 'in' dan 'not in':")
    
    # List
    fruits = ["apple", "banana", "orange"]
    print(f"   fruits = {fruits}")
    print(f"   'apple' in fruits = {'apple' in fruits}")
    print(f"   'grape' in fruits = {'grape' in fruits}")
    print(f"   'grape' not in fruits = {'grape' not in fruits}")
    
    # String
    text = "Hello World"
    print(f"\n   text = '{text}'")
    print(f"   'Hello' in text = {'Hello' in text}")
    print(f"   'hello' in text = {'hello' in text}  (case sensitive)")
    print(f"   'xyz' not in text = {'xyz' not in text}")
    
    # Dictionary (memeriksa key)
    person = {"name": "Alice", "age": 25, "city": "Jakarta"}
    print(f"\n   person = {person}")
    print(f"   'name' in person = {'name' in person}")
    print(f"   'Alice' in person = {'Alice' in person}  (value tidak dicek)")
    print(f"   'Alice' in person.values() = {'Alice' in person.values()}")
    
    # Set
    numbers = {1, 2, 3, 4, 5}
    print(f"\n   numbers = {numbers}")
    print(f"   3 in numbers = {3 in numbers}")
    print(f"   6 in numbers = {6 in numbers}")
    
    print("\n2. CONTOH PRAKTIS:")
    # Validasi input
    valid_choices = ['y', 'yes', 'n', 'no']
    user_input = 'yes'
    print(f"   valid_choices = {valid_choices}")
    print(f"   user_input = '{user_input}'")
    print(f"   user_input in valid_choices = {user_input in valid_choices}")
    
    # Cek karakter dalam string
    password = "mypassword123"
    has_digit = any(char.isdigit() for char in password)
    print(f"   password = '{password}'")
    print(f"   has_digit = {has_digit}")

def demonstrate_identity_operators():
    """Demonstrasi operator identity"""
    print("OPERATOR IDENTITY memeriksa apakah dua variabel merujuk objek yang sama.")
    
    print("\n1. OPERATOR 'is' dan 'is not':")
    
    # Integer kecil (cached)
    a = 100
    b = 100
    print(f"   a = 100, b = 100")
    print(f"   a is b = {a is b}  (integer kecil di-cache)")
    print(f"   id(a) = {id(a)}, id(b) = {id(b)}")
    
    # Integer besar (tidak cached)
    c = 1000
    d = 1000
    print(f"\n   c = 1000, d = 1000")
    print(f"   c is d = {c is d}  (integer besar tidak di-cache)")
    print(f"   c == d = {c == d}  (nilai sama)")
    print(f"   id(c) = {id(c)}, id(d) = {id(d)}")
    
    # List
    list1 = [1, 2, 3]
    list2 = [1, 2, 3]
    list3 = list1
    
    print(f"\n   list1 = {list1}")
    print(f"   list2 = {list2}")
    print(f"   list3 = list1")
    print(f"   list1 is list2 = {list1 is list2}  (objek berbeda)")
    print(f"   list1 == list2 = {list1 == list2}  (nilai sama)")
    print(f"   list1 is list3 = {list1 is list3}  (referensi sama)")
    
    # None
    value = None
    print(f"\n   value = None")
    print(f"   value is None = {value is None}  (cara yang benar)")
    print(f"   value == None = {value == None}  (tidak direkomendasikan)")
    
    print("\n2. PERBEDAAN '==' vs 'is':")
    print("   '==' membandingkan nilai")
    print("   'is' membandingkan identitas objek")
    
    # Contoh dengan string
    str1 = "hello"
    str2 = "hello"
    str3 = "hel" + "lo"
    
    print(f"\n   str1 = 'hello'")
    print(f"   str2 = 'hello'")
    print(f"   str3 = 'hel' + 'lo'")
    print(f"   str1 == str2 = {str1 == str2}")
    print(f"   str1 is str2 = {str1 is str2}")
    print(f"   str1 == str3 = {str1 == str3}")
    print(f"   str1 is str3 = {str1 is str3}")

def demonstrate_operator_precedence():
    """Demonstrasi prioritas operator"""
    print("PRIORITAS OPERATOR menentukan urutan evaluasi dalam ekspresi kompleks.")
    
    print("\n1. URUTAN PRIORITAS (dari tertinggi ke terendah):")
    precedence_table = [
        "1. () - Parentheses",
        "2. ** - Exponentiation",
        "3. +x, -x, ~x - Unary plus, minus, bitwise NOT",
        "4. *, /, //, % - Multiplication, division, floor division, modulus",
        "5. +, - - Addition, subtraction",
        "6. <<, >> - Bitwise shifts",
        "7. & - Bitwise AND",
        "8. ^ - Bitwise XOR",
        "9. | - Bitwise OR",
        "10. ==, !=, <, <=, >, >=, is, is not, in, not in - Comparisons",
        "11. not - Boolean NOT",
        "12. and - Boolean AND",
        "13. or - Boolean OR"
    ]
    
    for item in precedence_table:
        print(f"   {item}")
    
    print("\n2. CONTOH TANPA TANDA KURUNG:")
    expr1 = "2 + 3 * 4"
    result1 = 2 + 3 * 4
    print(f"   {expr1} = {result1}  (perkalian dulu)")
    
    expr2 = "10 - 4 / 2"
    result2 = 10 - 4 / 2
    print(f"   {expr2} = {result2}  (pembagian dulu)")
    
    expr3 = "2 ** 3 ** 2"
    result3 = 2 ** 3 ** 2
    print(f"   {expr3} = {result3}  (pangkat dari kanan ke kiri)")
    
    expr4 = "True or False and False"
    result4 = True or False and False
    print(f"   {expr4} = {result4}  (and dulu)")
    
    print("\n3. DENGAN TANDA KURUNG:")
    expr5 = "(2 + 3) * 4"
    result5 = (2 + 3) * 4
    print(f"   {expr5} = {result5}")
    
    expr6 = "(10 - 4) / 2"
    result6 = (10 - 4) / 2
    print(f"   {expr6} = {result6}")
    
    expr7 = "(2 ** 3) ** 2"
    result7 = (2 ** 3) ** 2
    print(f"   {expr7} = {result7}")
    
    expr8 = "(True or False) and False"
    result8 = (True or False) and False
    print(f"   {expr8} = {result8}")
    
    print("\n4. EKSPRESI KOMPLEKS:")
    # Contoh ekspresi yang kompleks
    x, y, z = 5, 10, 15
    complex_expr = x + y * z // 3 - 2 ** 2
    print(f"   Dengan x={x}, y={y}, z={z}:")
    print(f"   x + y * z // 3 - 2 ** 2")
    print(f"   = {x} + {y} * {z} // 3 - 2 ** 2")
    print(f"   = {x} + {y * z} // 3 - {2 ** 2}")
    print(f"   = {x} + {y * z // 3} - {2 ** 2}")
    print(f"   = {x + y * z // 3} - {2 ** 2}")
    print(f"   = {complex_expr}")

def interactive_expression_calculator():
    """Kalkulator ekspresi interaktif"""
    print("\nKALKULATOR EKSPRESI INTERAKTIF")
    print("-" * 35)
    print("Masukkan ekspresi matematika untuk dievaluasi.")
    print("Contoh: 2 + 3 * 4, (5 + 3) ** 2, 10 > 5 and 3 < 7")
    print("Ketik 'quit' untuk keluar.")
    
    while True:
        try:
            expression = input("\nEkspresi: ").strip()
            
            if expression.lower() == 'quit':
                print("Terima kasih!")
                break
            
            if not expression:
                continue
            
            # Evaluasi ekspresi
            result = eval(expression)
            print(f"Hasil: {result}")
            print(f"Tipe: {type(result).__name__}")
            
        except SyntaxError:
            print("Error: Sintaks tidak valid!")
        except NameError as e:
            print(f"Error: {e}")
        except ZeroDivisionError:
            print("Error: Pembagian dengan nol!")
        except Exception as e:
            print(f"Error: {e}")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI OPERATOR DAN EKSPRESI PYTHON")
    print("=====================================")
    print("Program ini mendemonstrasikan berbagai jenis operator")
    print("dan cara membentuk ekspresi dalam Python.")
    
    print_separator("OPERATOR ARITMATIKA")
    demonstrate_arithmetic_operators()
    
    print_separator("OPERATOR PERBANDINGAN")
    demonstrate_comparison_operators()
    
    print_separator("OPERATOR LOGIKA")
    demonstrate_logical_operators()
    
    print_separator("OPERATOR ASSIGNMENT")
    demonstrate_assignment_operators()
    
    print_separator("OPERATOR MEMBERSHIP")
    demonstrate_membership_operators()
    
    print_separator("OPERATOR IDENTITY")
    demonstrate_identity_operators()
    
    print_separator("PRIORITAS OPERATOR")
    demonstrate_operator_precedence()
    
    print_separator("KALKULATOR INTERAKTIF")
    interactive_expression_calculator()

if __name__ == "__main__":
    main()
