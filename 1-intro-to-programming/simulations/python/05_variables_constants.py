"""
SIMULASI VARIABEL DAN KONSTANTA
===============================

Variabel adalah tempat penyimpanan data yang nilainya dapat berubah.
Konstanta adalah nilai yang tidak berubah selama program berjalan.

Konsep penting:
1. DEKLARASI: Membuat variabel baru
2. INISIALISASI: Memberikan nilai awal
3. SCOPE: <PERSON><PERSON> lingkup di mana variabel dapat diakses
4. KONSTANTA: <PERSON><PERSON> yang tidak berubah (konvensi dalam Python)

Python menggunakan dynamic typing - tipe variabel ditentukan saat runtime.
"""

import sys

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

# Konstanta global (konvensi: huruf besar semua)
PI = 3.14159
GRAVITASI = 9.8
NAMA_APLIKASI = "Simulasi Python"
VERSI = "1.0.0"

def demonstrate_variable_declaration():
    """Demonstrasi deklarasi dan inisialisasi variabel"""
    print("DEKLARASI dan INISIALISASI VARIABEL")
    print("Di Python, deklarasi dan inisialisasi dilakukan bersamaan.")
    
    print("\n1. DEKLARASI SEDERHANA:")
    # Deklarasi dengan inisialisasi
    nama = "Alice"
    umur = 25
    tinggi = 165.5
    mahasiswa = True
    
    print(f"   nama = '{nama}' (tipe: {type(nama).__name__})")
    print(f"   umur = {umur} (tipe: {type(umur).__name__})")
    print(f"   tinggi = {tinggi} (tipe: {type(tinggi).__name__})")
    print(f"   mahasiswa = {mahasiswa} (tipe: {type(mahasiswa).__name__})")
    
    print("\n2. MULTIPLE ASSIGNMENT:")
    # Mendeklarasikan beberapa variabel sekaligus
    x, y, z = 10, 20, 30
    print(f"   x, y, z = 10, 20, 30")
    print(f"   Hasil: x={x}, y={y}, z={z}")
    
    # Assignment dengan nilai yang sama
    a = b = c = 100
    print(f"   a = b = c = 100")
    print(f"   Hasil: a={a}, b={b}, c={c}")
    
    print("\n3. DYNAMIC TYPING:")
    print("   Python memungkinkan variabel mengubah tipe:")
    variabel_dinamis = 42
    print(f"   variabel_dinamis = {variabel_dinamis} (tipe: {type(variabel_dinamis).__name__})")
    
    variabel_dinamis = "Sekarang string"
    print(f"   variabel_dinamis = '{variabel_dinamis}' (tipe: {type(variabel_dinamis).__name__})")
    
    variabel_dinamis = [1, 2, 3]
    print(f"   variabel_dinamis = {variabel_dinamis} (tipe: {type(variabel_dinamis).__name__})")
    
    print("\n4. UNPACKING:")
    # Unpacking dari list/tuple
    koordinat = (100, 200)
    x_pos, y_pos = koordinat
    print(f"   koordinat = {koordinat}")
    print(f"   x_pos, y_pos = koordinat")
    print(f"   Hasil: x_pos={x_pos}, y_pos={y_pos}")
    
    # Unpacking dengan *
    angka = [1, 2, 3, 4, 5]
    pertama, *tengah, terakhir = angka
    print(f"   angka = {angka}")
    print(f"   pertama, *tengah, terakhir = angka")
    print(f"   Hasil: pertama={pertama}, tengah={tengah}, terakhir={terakhir}")

def demonstrate_variable_scope():
    """Demonstrasi scope variabel"""
    print("SCOPE VARIABEL menentukan di mana variabel dapat diakses.")
    
    # Variabel global
    global_var = "Saya variabel global"
    
    print("\n1. GLOBAL SCOPE:")
    print(f"   global_var = '{global_var}'")
    print("   Dapat diakses dari mana saja dalam file ini")
    
    def fungsi_luar():
        """Fungsi untuk demonstrasi local scope"""
        # Variabel lokal
        local_var = "Saya variabel lokal"
        
        print(f"\n2. LOCAL SCOPE (dalam fungsi_luar):")
        print(f"   local_var = '{local_var}'")
        print(f"   global_var = '{global_var}' (akses global dari lokal)")
        
        def fungsi_dalam():
            """Fungsi nested untuk demonstrasi enclosing scope"""
            # Deklarasi nonlocal harus di awal sebelum menggunakan variabel
            nonlocal local_var

            # Variabel dalam fungsi nested
            nested_var = "Saya variabel nested"

            print(f"\n3. NESTED SCOPE (dalam fungsi_dalam):")
            print(f"   nested_var = '{nested_var}'")
            print(f"   local_var = '{local_var}' (akses enclosing scope)")
            print(f"   global_var = '{global_var}' (akses global)")

            # Modifikasi variabel dengan nonlocal
            local_var = "Diubah dari nested function"
            print(f"   local_var diubah menjadi: '{local_var}'")
        
        fungsi_dalam()
        print(f"\n   Setelah fungsi_dalam: local_var = '{local_var}'")
    
    fungsi_luar()
    
    print(f"\n4. SETELAH FUNGSI SELESAI:")
    print(f"   global_var = '{global_var}' (masih ada)")
    print("   local_var dan nested_var sudah tidak dapat diakses")
    
    # Demonstrasi global keyword
    def ubah_global():
        global global_var
        global_var = "Global diubah dari fungsi"
    
    print(f"\n5. MENGUBAH GLOBAL DARI FUNGSI:")
    print(f"   Sebelum: global_var = '{global_var}'")
    ubah_global()
    print(f"   Setelah: global_var = '{global_var}'")

def demonstrate_constants():
    """Demonstrasi konstanta"""
    print("KONSTANTA adalah nilai yang tidak berubah.")
    print("Python tidak memiliki konstanta sejati, tapi menggunakan konvensi.")
    
    print("\n1. KONVENSI KONSTANTA:")
    print("   Gunakan HURUF_BESAR untuk nama konstanta")
    print(f"   PI = {PI}")
    print(f"   GRAVITASI = {GRAVITASI}")
    print(f"   NAMA_APLIKASI = '{NAMA_APLIKASI}'")
    print(f"   VERSI = '{VERSI}'")
    
    print("\n2. KONSTANTA LOKAL:")
    # Konstanta dalam fungsi
    KECEPATAN_CAHAYA = 299792458  # m/s
    KONSTANTA_PLANCK = 6.62607015e-34  # J⋅s
    
    print(f"   KECEPATAN_CAHAYA = {KECEPATAN_CAHAYA} m/s")
    print(f"   KONSTANTA_PLANCK = {KONSTANTA_PLANCK} J⋅s")
    
    print("\n3. KONSTANTA BUILT-IN PYTHON:")
    import math
    print(f"   math.pi = {math.pi}")
    print(f"   math.e = {math.e}")
    print(f"   math.inf = {math.inf}")
    print(f"   math.nan = {math.nan}")
    
    print("\n4. MENGGUNAKAN ENUM UNTUK KONSTANTA:")
    from enum import Enum
    
    class Status(Enum):
        PENDING = "pending"
        APPROVED = "approved"
        REJECTED = "rejected"
    
    class HttpStatus(Enum):
        OK = 200
        NOT_FOUND = 404
        SERVER_ERROR = 500
    
    print("   class Status(Enum):")
    print("       PENDING = 'pending'")
    print("       APPROVED = 'approved'")
    print("       REJECTED = 'rejected'")
    print(f"   Status.PENDING = {Status.PENDING}")
    print(f"   Status.PENDING.value = {Status.PENDING.value}")
    
    print(f"   HttpStatus.OK = {HttpStatus.OK}")
    print(f"   HttpStatus.OK.value = {HttpStatus.OK.value}")

def demonstrate_variable_lifecycle():
    """Demonstrasi siklus hidup variabel"""
    print("SIKLUS HIDUP VARIABEL: Pembuatan → Penggunaan → Penghapusan")
    
    print("\n1. PEMBUATAN VARIABEL:")
    data = [1, 2, 3, 4, 5]
    print(f"   data = {data}")
    print(f"   ID objek: {id(data)}")
    print(f"   Referensi count: {sys.getrefcount(data) - 1}")  # -1 karena getrefcount menambah 1
    
    print("\n2. MULTIPLE REFERENSI:")
    data_copy = data  # Tidak membuat copy, hanya referensi baru
    print(f"   data_copy = data")
    print(f"   data ID: {id(data)}")
    print(f"   data_copy ID: {id(data_copy)}")
    print(f"   Sama? {data is data_copy}")
    print(f"   Referensi count: {sys.getrefcount(data) - 1}")
    
    print("\n3. MODIFIKASI MELALUI REFERENSI:")
    data_copy.append(6)
    print(f"   data_copy.append(6)")
    print(f"   data = {data}")
    print(f"   data_copy = {data_copy}")
    print("   Kedua variabel berubah karena merujuk objek yang sama!")
    
    print("\n4. MEMBUAT COPY SEJATI:")
    data_real_copy = data.copy()  # atau data[:]
    print(f"   data_real_copy = data.copy()")
    print(f"   data ID: {id(data)}")
    print(f"   data_real_copy ID: {id(data_real_copy)}")
    print(f"   Sama? {data is data_real_copy}")
    
    data_real_copy.append(7)
    print(f"   data_real_copy.append(7)")
    print(f"   data = {data}")
    print(f"   data_real_copy = {data_real_copy}")
    
    print("\n5. PENGHAPUSAN VARIABEL:")
    print(f"   Sebelum del: referensi count data = {sys.getrefcount(data) - 1}")
    del data_copy
    print(f"   Setelah del data_copy: referensi count data = {sys.getrefcount(data) - 1}")
    
    # Variabel akan dihapus otomatis saat keluar dari scope

def demonstrate_variable_naming():
    """Demonstrasi konvensi penamaan variabel"""
    print("KONVENSI PENAMAAN VARIABEL membantu kode lebih mudah dibaca.")
    
    print("\n1. SNAKE_CASE (Direkomendasikan untuk Python):")
    nama_lengkap = "Alice Johnson"
    tanggal_lahir = "1998-05-15"
    nilai_rata_rata = 85.5
    is_mahasiswa_aktif = True
    
    print(f"   nama_lengkap = '{nama_lengkap}'")
    print(f"   tanggal_lahir = '{tanggal_lahir}'")
    print(f"   nilai_rata_rata = {nilai_rata_rata}")
    print(f"   is_mahasiswa_aktif = {is_mahasiswa_aktif}")
    
    print("\n2. CAMELCASE (Tidak umum untuk variabel Python):")
    namaLengkap = "Bob Smith"  # Tidak direkomendasikan
    tanggalLahir = "1999-03-20"
    
    print(f"   namaLengkap = '{namaLengkap}' (tidak direkomendasikan)")
    print(f"   tanggalLahir = '{tanggalLahir}' (tidak direkomendasikan)")
    
    print("\n3. KONSTANTA (UPPER_CASE):")
    print(f"   PI = {PI}")
    print(f"   GRAVITASI = {GRAVITASI}")
    
    print("\n4. PRIVATE VARIABLES (dengan underscore):")
    _internal_var = "Variabel internal"
    __private_var = "Variabel sangat private"
    
    print(f"   _internal_var = '{_internal_var}' (konvensi: internal)")
    print(f"   __private_var = '{__private_var}' (name mangling)")
    
    print("\n5. SPECIAL VARIABLES (dunder):")
    print(f"   __name__ = '{__name__}' (nama modul)")
    print(f"   __file__ = '{__file__}' (path file)")

def interactive_variable_demo():
    """Demo interaktif untuk eksplorasi variabel"""
    print("\nDEMO INTERAKTIF VARIABEL")
    print("-" * 30)
    
    variables = {}
    
    while True:
        print("\nPilihan:")
        print("1. Buat variabel baru")
        print("2. Lihat semua variabel")
        print("3. Ubah nilai variabel")
        print("4. Hapus variabel")
        print("5. Keluar")
        
        choice = input("Pilih opsi (1-5): ").strip()
        
        if choice == '1':
            name = input("Nama variabel: ").strip()
            if not name.isidentifier():
                print("Error: Nama variabel tidak valid!")
                continue
            
            value = input("Nilai variabel: ").strip()
            try:
                # Coba konversi ke tipe yang sesuai
                if value.isdigit():
                    value = int(value)
                elif value.replace('.', '').isdigit():
                    value = float(value)
                elif value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                # Sisanya tetap string
                
                variables[name] = value
                print(f"Variabel '{name}' dibuat dengan nilai {repr(value)}")
            except Exception as e:
                print(f"Error: {e}")
        
        elif choice == '2':
            if not variables:
                print("Belum ada variabel yang dibuat.")
            else:
                print("Daftar variabel:")
                for name, value in variables.items():
                    print(f"   {name} = {repr(value)} (tipe: {type(value).__name__})")
        
        elif choice == '3':
            if not variables:
                print("Belum ada variabel yang dibuat.")
                continue
            
            name = input("Nama variabel yang akan diubah: ").strip()
            if name not in variables:
                print(f"Variabel '{name}' tidak ditemukan.")
                continue
            
            new_value = input(f"Nilai baru untuk '{name}': ").strip()
            try:
                # Konversi tipe seperti sebelumnya
                if new_value.isdigit():
                    new_value = int(new_value)
                elif new_value.replace('.', '').isdigit():
                    new_value = float(new_value)
                elif new_value.lower() in ['true', 'false']:
                    new_value = new_value.lower() == 'true'
                
                old_value = variables[name]
                variables[name] = new_value
                print(f"'{name}' diubah dari {repr(old_value)} ke {repr(new_value)}")
            except Exception as e:
                print(f"Error: {e}")
        
        elif choice == '4':
            if not variables:
                print("Belum ada variabel yang dibuat.")
                continue
            
            name = input("Nama variabel yang akan dihapus: ").strip()
            if name in variables:
                del variables[name]
                print(f"Variabel '{name}' telah dihapus.")
            else:
                print(f"Variabel '{name}' tidak ditemukan.")
        
        elif choice == '5':
            print("Terima kasih!")
            break
        else:
            print("Pilihan tidak valid!")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI VARIABEL DAN KONSTANTA PYTHON")
    print("======================================")
    print("Program ini mendemonstrasikan konsep variabel dan konstanta")
    print("dalam Python, termasuk deklarasi, scope, dan lifecycle.")
    
    print_separator("DEKLARASI DAN INISIALISASI")
    demonstrate_variable_declaration()
    
    print_separator("SCOPE VARIABEL")
    demonstrate_variable_scope()
    
    print_separator("KONSTANTA")
    demonstrate_constants()
    
    print_separator("SIKLUS HIDUP VARIABEL")
    demonstrate_variable_lifecycle()
    
    print_separator("KONVENSI PENAMAAN")
    demonstrate_variable_naming()
    
    print_separator("DEMO INTERAKTIF")
    interactive_variable_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN:")
    print("- Variabel: Tempat penyimpanan data yang dapat berubah")
    print("- Konstanta: Nilai yang tidak berubah (konvensi UPPER_CASE)")
    print("- Scope: Global, Local, Enclosing, Built-in (LEGB rule)")
    print("- Dynamic typing: Tipe ditentukan saat runtime")
    print("- Naming: snake_case untuk variabel, UPPER_CASE untuk konstanta")
    print("="*60)

if __name__ == "__main__":
    main()
