"""
SIMULASI STRUKTUR KONTROL
=========================

Struktur kontrol mengatur alur eksekusi program.
Tanpa struktur kontrol, program hanya berjalan dari atas ke bawah.

<PERSON><PERSON> struktur kontrol:
1. KONDISIONAL: if, elif, else
2. PERCABANGAN: match-case (Python 3.10+)
3. PERULANGAN: for, while
4. JUMP: break, continue, pass, return

Struktur kontrol memungkinkan program membuat keputusan,
mengulang tindakan, dan mengubah alur eksekusi.
"""

import random
import time

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_conditional_statements():
    """Demonstrasi pernyataan kondisional"""
    print("PERNYATAAN KONDISIONAL memungkinkan program membuat keputusan.")
    
    print("\n1. IF STATEMENT SEDERHANA:")
    umur = 18
    print(f"   umur = {umur}")
    print("   if umur >= 18:")
    print("       print('Anda sudah dewasa')")
    
    if umur >= 18:
        print("   Output: Anda sudah dewasa")
    
    print("\n2. IF-ELSE STATEMENT:")
    nilai = 75
    print(f"   nilai = {nilai}")
    print("   if nilai >= 70:")
    print("       print('Lulus')")
    print("   else:")
    print("       print('Tidak Lulus')")
    
    if nilai >= 70:
        print("   Output: Lulus")
    else:
        print("   Output: Tidak Lulus")
    
    print("\n3. IF-ELIF-ELSE STATEMENT:")
    skor = 85
    print(f"   skor = {skor}")
    print("   if skor >= 90:")
    print("       grade = 'A'")
    print("   elif skor >= 80:")
    print("       grade = 'B'")
    print("   elif skor >= 70:")
    print("       grade = 'C'")
    print("   elif skor >= 60:")
    print("       grade = 'D'")
    print("   else:")
    print("       grade = 'F'")
    
    if skor >= 90:
        grade = 'A'
    elif skor >= 80:
        grade = 'B'
    elif skor >= 70:
        grade = 'C'
    elif skor >= 60:
        grade = 'D'
    else:
        grade = 'F'
    
    print(f"   Output: Grade = {grade}")
    
    print("\n4. NESTED IF (IF BERSARANG):")
    cuaca = "cerah"
    suhu = 25
    print(f"   cuaca = '{cuaca}', suhu = {suhu}")
    print("   if cuaca == 'cerah':")
    print("       if suhu > 30:")
    print("           print('Panas sekali!')")
    print("       elif suhu > 20:")
    print("           print('Cuaca bagus untuk jalan-jalan')")
    print("       else:")
    print("           print('Agak dingin')")
    print("   else:")
    print("       print('Cuaca tidak mendukung')")
    
    if cuaca == "cerah":
        if suhu > 30:
            print("   Output: Panas sekali!")
        elif suhu > 20:
            print("   Output: Cuaca bagus untuk jalan-jalan")
        else:
            print("   Output: Agak dingin")
    else:
        print("   Output: Cuaca tidak mendukung")
    
    print("\n5. CONDITIONAL EXPRESSION (TERNARY):")
    x = 10
    print(f"   x = {x}")
    print("   status = 'positif' if x > 0 else 'negatif atau nol'")
    status = 'positif' if x > 0 else 'negatif atau nol'
    print(f"   Output: status = '{status}'")

def demonstrate_match_case():
    """Demonstrasi match-case statement (Python 3.10+)"""
    print("MATCH-CASE STATEMENT (Python 3.10+) seperti switch-case di bahasa lain.")
    
    try:
        print("\n1. MATCH SEDERHANA:")
        hari = "senin"
        print(f"   hari = '{hari}'")
        print("   match hari:")
        print("       case 'senin':")
        print("           aktivitas = 'Kerja'")
        print("       case 'sabtu' | 'minggu':")
        print("           aktivitas = 'Libur'")
        print("       case _:")
        print("           aktivitas = 'Hari kerja'")
        
        # Note: match-case memerlukan Python 3.10+
        # Simulasi dengan if-elif untuk kompatibilitas
        if hari == 'senin':
            aktivitas = 'Kerja'
        elif hari in ['sabtu', 'minggu']:
            aktivitas = 'Libur'
        else:
            aktivitas = 'Hari kerja'
        
        print(f"   Output: aktivitas = '{aktivitas}'")
        
        print("\n2. MATCH DENGAN GUARD:")
        nilai = 85
        print(f"   nilai = {nilai}")
        print("   match nilai:")
        print("       case x if x >= 90:")
        print("           grade = 'A'")
        print("       case x if x >= 80:")
        print("           grade = 'B'")
        print("       case _:")
        print("           grade = 'C atau lebih rendah'")
        
        # Simulasi dengan if-elif
        if nilai >= 90:
            grade = 'A'
        elif nilai >= 80:
            grade = 'B'
        else:
            grade = 'C atau lebih rendah'
        
        print(f"   Output: grade = '{grade}'")
        
    except SyntaxError:
        print("   Note: match-case memerlukan Python 3.10 atau lebih baru")
        print("   Menggunakan if-elif sebagai alternatif")

def demonstrate_for_loops():
    """Demonstrasi perulangan for"""
    print("PERULANGAN FOR digunakan untuk iterasi pada sequence.")
    
    print("\n1. FOR DENGAN RANGE:")
    print("   for i in range(5):")
    print("       print(f'Iterasi {i}')")
    print("   Output:")
    for i in range(5):
        print(f"     Iterasi {i}")
    
    print("\n2. FOR DENGAN RANGE(START, STOP, STEP):")
    print("   for i in range(2, 10, 2):")
    print("       print(i, end=' ')")
    print("   Output: ", end="")
    for i in range(2, 10, 2):
        print(i, end=' ')
    print()
    
    print("\n3. FOR DENGAN LIST:")
    fruits = ["apel", "pisang", "jeruk"]
    print(f"   fruits = {fruits}")
    print("   for fruit in fruits:")
    print("       print(f'Saya suka {fruit}')")
    print("   Output:")
    for fruit in fruits:
        print(f"     Saya suka {fruit}")
    
    print("\n4. FOR DENGAN ENUMERATE:")
    print("   for index, fruit in enumerate(fruits):")
    print("       print(f'{index}: {fruit}')")
    print("   Output:")
    for index, fruit in enumerate(fruits):
        print(f"     {index}: {fruit}")
    
    print("\n5. FOR DENGAN DICTIONARY:")
    person = {"nama": "Alice", "umur": 25, "kota": "Jakarta"}
    print(f"   person = {person}")
    print("   for key, value in person.items():")
    print("       print(f'{key}: {value}')")
    print("   Output:")
    for key, value in person.items():
        print(f"     {key}: {value}")
    
    print("\n6. NESTED FOR LOOPS:")
    print("   for i in range(3):")
    print("       for j in range(3):")
    print("           print(f'({i},{j})', end=' ')")
    print("       print()  # new line")
    print("   Output:")
    for i in range(3):
        print("     ", end="")
        for j in range(3):
            print(f'({i},{j})', end=' ')
        print()  # new line
    
    print("\n7. LIST COMPREHENSION:")
    print("   squares = [x**2 for x in range(5)]")
    squares = [x**2 for x in range(5)]
    print(f"   Output: squares = {squares}")
    
    print("   even_squares = [x**2 for x in range(10) if x % 2 == 0]")
    even_squares = [x**2 for x in range(10) if x % 2 == 0]
    print(f"   Output: even_squares = {even_squares}")

def demonstrate_while_loops():
    """Demonstrasi perulangan while"""
    print("PERULANGAN WHILE berjalan selama kondisi True.")
    
    print("\n1. WHILE SEDERHANA:")
    print("   count = 0")
    print("   while count < 5:")
    print("       print(f'Count: {count}')")
    print("       count += 1")
    print("   Output:")
    count = 0
    while count < 5:
        print(f"     Count: {count}")
        count += 1
    
    print("\n2. WHILE DENGAN INPUT (SIMULASI):")
    print("   # Simulasi input validation")
    print("   attempts = 0")
    print("   max_attempts = 3")
    print("   while attempts < max_attempts:")
    print("       password = input('Password: ')")
    print("       if password == 'secret':")
    print("           print('Login berhasil!')")
    print("           break")
    print("       attempts += 1")
    print("   else:")
    print("       print('Terlalu banyak percobaan!')")
    
    # Simulasi tanpa input
    attempts = 0
    max_attempts = 3
    passwords = ["wrong1", "wrong2", "secret"]  # Simulasi input
    
    print("   Simulasi dengan passwords = ['wrong1', 'wrong2', 'secret']:")
    while attempts < max_attempts:
        password = passwords[attempts] if attempts < len(passwords) else "wrong"
        print(f"     Attempt {attempts + 1}: password = '{password}'")
        if password == 'secret':
            print("     Login berhasil!")
            break
        attempts += 1
    else:
        if attempts >= max_attempts:
            print("     Terlalu banyak percobaan!")
    
    print("\n3. INFINITE LOOP DENGAN BREAK:")
    print("   count = 0")
    print("   while True:")
    print("       if count >= 3:")
    print("           break")
    print("       print(f'Loop {count}')")
    print("       count += 1")
    print("   Output:")
    count = 0
    while True:
        if count >= 3:
            break
        print(f"     Loop {count}")
        count += 1

def demonstrate_jump_statements():
    """Demonstrasi pernyataan jump"""
    print("PERNYATAAN JUMP mengubah alur normal eksekusi program.")
    
    print("\n1. BREAK - Keluar dari loop:")
    print("   for i in range(10):")
    print("       if i == 5:")
    print("           break")
    print("       print(i, end=' ')")
    print("   Output: ", end="")
    for i in range(10):
        if i == 5:
            break
        print(i, end=' ')
    print()
    
    print("\n2. CONTINUE - Skip iterasi saat ini:")
    print("   for i in range(10):")
    print("       if i % 2 == 0:")
    print("           continue")
    print("       print(i, end=' ')")
    print("   Output: ", end="")
    for i in range(10):
        if i % 2 == 0:
            continue
        print(i, end=' ')
    print()
    
    print("\n3. PASS - Placeholder (tidak melakukan apa-apa):")
    print("   for i in range(3):")
    print("       if i == 1:")
    print("           pass  # TODO: implement later")
    print("       else:")
    print("           print(f'Processing {i}')")
    print("   Output:")
    for i in range(3):
        if i == 1:
            pass  # TODO: implement later
        else:
            print(f"     Processing {i}")
    
    print("\n4. BREAK dan CONTINUE dalam NESTED LOOPS:")
    print("   for i in range(3):")
    print("       for j in range(3):")
    print("           if j == 1:")
    print("               continue")
    print("           if i == 1 and j == 2:")
    print("               break")
    print("           print(f'({i},{j})', end=' ')")
    print("       print()  # new line")
    print("   Output:")
    for i in range(3):
        print("     ", end="")
        for j in range(3):
            if j == 1:
                continue
            if i == 1 and j == 2:
                break
            print(f'({i},{j})', end=' ')
        print()  # new line
    
    print("\n5. ELSE CLAUSE dengan LOOPS:")
    print("   # Else dijalankan jika loop selesai normal (tanpa break)")
    print("   for i in range(5):")
    print("       if i == 10:  # kondisi tidak pernah True")
    print("           break")
    print("       print(i, end=' ')")
    print("   else:")
    print("       print('Loop selesai normal')")
    print("   Output: ", end="")
    for i in range(5):
        if i == 10:  # kondisi tidak pernah True
            break
        print(i, end=' ')
    else:
        print("Loop selesai normal")

def demonstrate_practical_examples():
    """Contoh praktis penggunaan struktur kontrol"""
    print("CONTOH PRAKTIS penggunaan struktur kontrol dalam program nyata.")
    
    print("\n1. VALIDASI INPUT:")
    def validate_age(age_str):
        """Validasi input umur"""
        try:
            age = int(age_str)
            if age < 0:
                return False, "Umur tidak boleh negatif"
            elif age > 150:
                return False, "Umur terlalu besar"
            else:
                return True, f"Umur {age} tahun valid"
        except ValueError:
            return False, "Input harus berupa angka"
    
    test_ages = ["25", "-5", "200", "abc", "30"]
    print("   def validate_age(age_str): ...")
    print(f"   test_ages = {test_ages}")
    for age_str in test_ages:
        valid, message = validate_age(age_str)
        print(f"     '{age_str}' -> {message}")
    
    print("\n2. PENCARIAN DALAM LIST:")
    def find_item(items, target):
        """Mencari item dalam list"""
        for index, item in enumerate(items):
            if item == target:
                return index, f"Item '{target}' ditemukan di index {index}"
        return -1, f"Item '{target}' tidak ditemukan"
    
    fruits = ["apel", "pisang", "jeruk", "mangga"]
    search_items = ["pisang", "anggur"]
    print(f"   fruits = {fruits}")
    print(f"   search_items = {search_items}")
    for item in search_items:
        index, message = find_item(fruits, item)
        print(f"     {message}")
    
    print("\n3. MENU SEDERHANA:")
    def simple_menu():
        """Simulasi menu sederhana"""
        menu_options = {
            "1": "Lihat Data",
            "2": "Tambah Data", 
            "3": "Hapus Data",
            "4": "Keluar"
        }
        
        # Simulasi pilihan user
        user_choices = ["1", "2", "5", "4"]
        
        print("   Menu:")
        for key, value in menu_options.items():
            print(f"     {key}. {value}")
        
        print(f"   Simulasi pilihan: {user_choices}")
        
        for choice in user_choices:
            print(f"   User memilih: {choice}")
            if choice == "1":
                print("     -> Menampilkan data...")
            elif choice == "2":
                print("     -> Menambah data...")
            elif choice == "3":
                print("     -> Menghapus data...")
            elif choice == "4":
                print("     -> Keluar dari program")
                break
            else:
                print("     -> Pilihan tidak valid!")
    
    simple_menu()
    
    print("\n4. ALGORITMA SEDERHANA - BUBBLE SORT:")
    def bubble_sort_demo(arr):
        """Demonstrasi bubble sort dengan output"""
        n = len(arr)
        arr = arr.copy()  # Jangan ubah array asli
        
        print(f"   Array awal: {arr}")
        
        for i in range(n):
            swapped = False
            for j in range(0, n - i - 1):
                if arr[j] > arr[j + 1]:
                    arr[j], arr[j + 1] = arr[j + 1], arr[j]
                    swapped = True
            
            print(f"   Setelah pass {i + 1}: {arr}")
            
            if not swapped:
                print("   Array sudah terurut, berhenti lebih awal")
                break
        
        return arr
    
    numbers = [64, 34, 25, 12, 22, 11, 90]
    print("   Bubble Sort:")
    sorted_numbers = bubble_sort_demo(numbers)
    print(f"   Hasil akhir: {sorted_numbers}")

def interactive_control_demo():
    """Demo interaktif struktur kontrol"""
    print("\nDEMO INTERAKTIF STRUKTUR KONTROL")
    print("-" * 40)
    
    while True:
        print("\nPilih demo:")
        print("1. Kalkulator sederhana")
        print("2. Tebak angka")
        print("3. Tabel perkalian")
        print("4. Keluar")
        
        choice = input("Pilihan (1-4): ").strip()
        
        if choice == "1":
            print("\nKalkulator Sederhana:")
            try:
                a = float(input("Angka pertama: "))
                op = input("Operator (+, -, *, /): ").strip()
                b = float(input("Angka kedua: "))
                
                if op == "+":
                    result = a + b
                elif op == "-":
                    result = a - b
                elif op == "*":
                    result = a * b
                elif op == "/":
                    if b != 0:
                        result = a / b
                    else:
                        print("Error: Pembagian dengan nol!")
                        continue
                else:
                    print("Operator tidak valid!")
                    continue
                
                print(f"Hasil: {a} {op} {b} = {result}")
                
            except ValueError:
                print("Error: Input harus berupa angka!")
        
        elif choice == "2":
            print("\nTebak Angka (1-10):")
            target = random.randint(1, 10)
            attempts = 0
            max_attempts = 3
            
            while attempts < max_attempts:
                try:
                    guess = int(input(f"Tebakan ke-{attempts + 1}: "))
                    attempts += 1
                    
                    if guess == target:
                        print(f"Benar! Angka adalah {target}")
                        break
                    elif guess < target:
                        print("Terlalu kecil!")
                    else:
                        print("Terlalu besar!")
                        
                except ValueError:
                    print("Input harus berupa angka!")
                    attempts += 1
            else:
                print(f"Game over! Angka yang benar adalah {target}")
        
        elif choice == "3":
            print("\nTabel Perkalian:")
            try:
                n = int(input("Masukkan angka (1-12): "))
                if 1 <= n <= 12:
                    print(f"\nTabel perkalian {n}:")
                    for i in range(1, 11):
                        print(f"{n} x {i} = {n * i}")
                else:
                    print("Angka harus antara 1-12!")
            except ValueError:
                print("Input harus berupa angka!")
        
        elif choice == "4":
            print("Terima kasih!")
            break
        else:
            print("Pilihan tidak valid!")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI STRUKTUR KONTROL PYTHON")
    print("=================================")
    print("Program ini mendemonstrasikan berbagai struktur kontrol")
    print("yang mengatur alur eksekusi program.")
    
    print_separator("PERNYATAAN KONDISIONAL")
    demonstrate_conditional_statements()
    
    print_separator("MATCH-CASE STATEMENT")
    demonstrate_match_case()
    
    print_separator("PERULANGAN FOR")
    demonstrate_for_loops()
    
    print_separator("PERULANGAN WHILE")
    demonstrate_while_loops()
    
    print_separator("PERNYATAAN JUMP")
    demonstrate_jump_statements()
    
    print_separator("CONTOH PRAKTIS")
    demonstrate_practical_examples()
    
    print_separator("DEMO INTERAKTIF")
    interactive_control_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN STRUKTUR KONTROL:")
    print("- if/elif/else: Membuat keputusan berdasarkan kondisi")
    print("- for: Iterasi pada sequence (list, range, string, dll)")
    print("- while: Perulangan selama kondisi True")
    print("- break: Keluar dari loop")
    print("- continue: Skip ke iterasi berikutnya")
    print("- pass: Placeholder yang tidak melakukan apa-apa")
    print("="*60)

if __name__ == "__main__":
    main()
