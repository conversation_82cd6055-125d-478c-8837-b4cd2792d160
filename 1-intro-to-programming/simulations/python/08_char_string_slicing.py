"""
SIMULASI CHARACTER, STRING, DAN SLICING
=======================================

String adalah sequence karakter yang immutable (tidak dapat diubah).
Character dalam Python adalah string dengan panjang 1.
Slicing adalah cara mengambil bagian dari string atau sequence.

Konsep penting:
1. CHARACTER: Karakter tunggal
2. STRING: Kumpulan karakter
3. SLICING: Mengambil bagian dari string
4. STRING METHODS: Fungsi built-in untuk manipulasi string
5. STRING FORMATTING: Cara memformat string

Python menggunakan Unicode untuk mendukung karakter internasional.
"""

import string
import re

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_characters():
    """Demonstrasi karakter dalam Python"""
    print("CHARACTER dalam Python adalah string dengan panjang 1.")
    print("Python tidak memiliki tipe data char terpisah seperti C/Java.")
    
    print("\n1. KARAKTER DASAR:")
    char_a = 'a'
    char_num = '5'
    char_space = ' '
    char_newline = '\n'
    
    print(f"   char_a = '{char_a}' (tipe: {type(char_a).__name__})")
    print(f"   char_num = '{char_num}' (tipe: {type(char_num).__name__})")
    print(f"   char_space = '{char_space}' (tipe: {type(char_space).__name__})")
    print(f"   char_newline = '\\n' (tipe: {type(char_newline).__name__})")
    
    print("\n2. ASCII DAN UNICODE:")
    print("   ASCII values:")
    chars = ['A', 'a', '0', ' ', '!']
    for char in chars:
        print(f"     '{char}' -> ASCII: {ord(char)}")
    
    print("   Unicode characters:")
    unicode_chars = ['α', '中', '🐍', '€']
    for char in unicode_chars:
        print(f"     '{char}' -> Unicode: {ord(char)}")
    
    print("\n3. KONVERSI ASCII/UNICODE:")
    print("   chr(65) =", repr(chr(65)))
    print("   chr(97) =", repr(chr(97)))
    print("   chr(8364) =", repr(chr(8364)))  # Euro symbol
    print("   chr(128013) =", repr(chr(128013)))  # Snake emoji
    
    print("\n4. ESCAPE CHARACTERS:")
    escape_chars = {
        '\\n': 'newline',
        '\\t': 'tab',
        '\\r': 'carriage return',
        '\\\\': 'backslash',
        '\\"': 'double quote',
        "\\'": 'single quote'
    }
    
    for escape, desc in escape_chars.items():
        print(f"   {repr(escape):6} -> {desc}")
    
    print("\n5. KARAKTER PROPERTIES:")
    test_chars = ['A', 'a', '5', ' ', '!', 'α']
    print("   Char | isalpha | isdigit | isalnum | isspace | isprintable")
    print("   -----|---------|---------|---------|---------|------------")
    for char in test_chars:
        print(f"   '{char}':4 | {str(char.isalpha()):7} | {str(char.isdigit()):7} | {str(char.isalnum()):7} | {str(char.isspace()):7} | {str(char.isprintable()):11}")

def demonstrate_strings():
    """Demonstrasi string dalam Python"""
    print("STRING adalah sequence karakter yang immutable.")
    
    print("\n1. PEMBUATAN STRING:")
    str1 = "Hello World"
    str2 = 'Python Programming'
    str3 = """Multi-line
string dengan
beberapa baris"""
    str4 = r"Raw string: C:\Users\<USER>\n2. PROPERTIES STRING:")
    text = "Hello Python"
    print(f"   text = '{text}'")
    print(f"   len(text) = {len(text)}")
    print(f"   text[0] = '{text[0]}' (karakter pertama)")
    print(f"   text[-1] = '{text[-1]}' (karakter terakhir)")
    
    print("\n3. STRING IMMUTABILITY:")
    print("   String tidak dapat diubah setelah dibuat:")
    print(f"   text = '{text}'")
    print("   # text[0] = 'h'  # Ini akan error!")
    print("   Untuk 'mengubah', buat string baru:")
    new_text = 'h' + text[1:]
    print(f"   new_text = 'h' + text[1:] = '{new_text}'")
    
    print("\n4. STRING CONCATENATION:")
    first = "Hello"
    second = "World"
    print(f"   first = '{first}', second = '{second}'")
    print(f"   first + ' ' + second = '{first + ' ' + second}'")
    print(f"   first * 3 = '{first * 3}'")
    
    print("\n5. STRING COMPARISON:")
    strings = ["apple", "banana", "Apple", "APPLE"]
    print(f"   strings = {strings}")
    print("   Perbandingan (lexicographic order):")
    for i in range(len(strings)):
        for j in range(i+1, len(strings)):
            result = strings[i] < strings[j]
            print(f"     '{strings[i]}' < '{strings[j]}' = {result}")

def demonstrate_slicing():
    """Demonstrasi slicing string"""
    print("SLICING mengambil bagian dari string menggunakan [start:stop:step].")
    
    text = "Python Programming"
    print(f"String: '{text}'")
    print("Index:   0123456789012345678")
    print("        -18-17-16-15-14-13-12-11-10-9-8-7-6-5-4-3-2-1")
    
    print("\n1. BASIC SLICING:")
    slicing_examples = [
        ("text[0:6]", text[0:6], "karakter 0 sampai 5"),
        ("text[7:]", text[7:], "karakter 7 sampai akhir"),
        ("text[:6]", text[:6], "awal sampai karakter 5"),
        ("text[:]", text[:], "seluruh string"),
        ("text[-11:]", text[-11:], "11 karakter terakhir"),
        ("text[:-12]", text[:-12], "kecuali 12 karakter terakhir")
    ]
    
    for code, result, desc in slicing_examples:
        print(f"   {code:12} = '{result}' ({desc})")
    
    print("\n2. SLICING DENGAN STEP:")
    step_examples = [
        ("text[::2]", text[::2], "setiap 2 karakter"),
        ("text[1::2]", text[1::2], "mulai index 1, setiap 2 karakter"),
        ("text[::-1]", text[::-1], "reverse string"),
        ("text[::3]", text[::3], "setiap 3 karakter"),
        ("text[2:10:2]", text[2:10:2], "index 2-9, setiap 2 karakter")
    ]
    
    for code, result, desc in step_examples:
        print(f"   {code:15} = '{result}' ({desc})")
    
    print("\n3. SLICING DENGAN NEGATIVE INDEX:")
    negative_examples = [
        ("text[-5:]", text[-5:], "5 karakter terakhir"),
        ("text[:-5]", text[:-5], "kecuali 5 karakter terakhir"),
        ("text[-10:-5]", text[-10:-5], "dari index -10 sampai -6"),
        ("text[::-2]", text[::-2], "reverse dengan step 2")
    ]
    
    for code, result, desc in negative_examples:
        print(f"   {code:15} = '{result}' ({desc})")
    
    print("\n4. SLICING LISTS (BONUS):")
    numbers = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    print(f"   numbers = {numbers}")
    print(f"   numbers[2:7] = {numbers[2:7]}")
    print(f"   numbers[::2] = {numbers[::2]}")
    print(f"   numbers[::-1] = {numbers[::-1]}")

def demonstrate_string_methods():
    """Demonstrasi method-method string"""
    print("STRING METHODS adalah fungsi built-in untuk manipulasi string.")
    
    text = "  Hello Python World  "
    print(f"Original: {repr(text)}")
    
    print("\n1. CASE METHODS:")
    case_methods = [
        ("upper()", text.upper()),
        ("lower()", text.lower()),
        ("title()", text.title()),
        ("capitalize()", text.capitalize()),
        ("swapcase()", text.swapcase())
    ]
    
    for method, result in case_methods:
        print(f"   text.{method:12} = {repr(result)}")
    
    print("\n2. WHITESPACE METHODS:")
    whitespace_methods = [
        ("strip()", text.strip()),
        ("lstrip()", text.lstrip()),
        ("rstrip()", text.rstrip()),
        ("center(30)", text.center(30)),
        ("ljust(25)", text.ljust(25)),
        ("rjust(25)", text.rjust(25))
    ]
    
    for method, result in whitespace_methods:
        print(f"   text.{method:12} = {repr(result)}")
    
    print("\n3. SEARCH METHODS:")
    search_text = "Python is awesome. Python is powerful."
    print(f"search_text = '{search_text}'")
    
    search_methods = [
        ("find('Python')", search_text.find('Python')),
        ("rfind('Python')", search_text.rfind('Python')),
        ("index('is')", search_text.index('is')),
        ("count('Python')", search_text.count('Python')),
        ("startswith('Python')", search_text.startswith('Python')),
        ("endswith('powerful.')", search_text.endswith('powerful.'))
    ]
    
    for method, result in search_methods:
        print(f"   search_text.{method:20} = {result}")
    
    print("\n4. SPLIT AND JOIN:")
    sentence = "apple,banana,orange,grape"
    print(f"sentence = '{sentence}'")
    
    words = sentence.split(',')
    print(f"   sentence.split(',') = {words}")
    
    joined = ' | '.join(words)
    print(f"   ' | '.join(words) = '{joined}'")
    
    print("\n5. REPLACE METHODS:")
    text_replace = "Hello World Hello Universe"
    print(f"text = '{text_replace}'")
    
    replace_methods = [
        ("replace('Hello', 'Hi')", text_replace.replace('Hello', 'Hi')),
        ("replace('Hello', 'Hi', 1)", text_replace.replace('Hello', 'Hi', 1)),
    ]
    
    for method, result in replace_methods:
        print(f"   text.{method:25} = '{result}'")
    
    print("\n6. VALIDATION METHODS:")
    test_strings = ["123", "abc", "abc123", "ABC", "   ", ""]
    validation_methods = ['isdigit', 'isalpha', 'isalnum', 'isupper', 'islower', 'isspace']
    
    print("   String  | " + " | ".join(f"{method:8}" for method in validation_methods))
    print("   --------|" + "|".join("-" * 9 for _ in validation_methods))
    
    for test_str in test_strings:
        results = [str(getattr(test_str, method)()) for method in validation_methods]
        print(f"   {repr(test_str):7} | " + " | ".join(f"{result:8}" for result in results))

def demonstrate_string_formatting():
    """Demonstrasi formatting string"""
    print("STRING FORMATTING memungkinkan kita menyisipkan nilai ke dalam string.")
    
    name = "Alice"
    age = 25
    score = 87.5
    
    print(f"Data: name='{name}', age={age}, score={score}")
    
    print("\n1. OLD STYLE FORMATTING (%):")
    old_formats = [
        ("'Hello %s' % name", "Hello %s" % name),
        ("'Age: %d' % age", "Age: %d" % age),
        ("'Score: %.1f' % score", "Score: %.1f" % score),
        ("'%s is %d years old' % (name, age)", "%s is %d years old" % (name, age))
    ]
    
    for code, result in old_formats:
        print(f"   {code:35} = '{result}'")
    
    print("\n2. NEW STYLE FORMATTING (.format()):")
    new_formats = [
        ("'Hello {}'.format(name)", "Hello {}".format(name)),
        ("'Age: {}'.format(age)", "Age: {}".format(age)),
        ("'Score: {:.1f}'.format(score)", "Score: {:.1f}".format(score)),
        ("'{0} is {1} years old'.format(name, age)", "{0} is {1} years old".format(name, age)),
        ("'{name} scored {score:.1f}'.format(name=name, score=score)", "{name} scored {score:.1f}".format(name=name, score=score))
    ]
    
    for code, result in new_formats:
        print(f"   {code:50} = '{result}'")
    
    print("\n3. F-STRINGS (Python 3.6+):")
    f_formats = [
        ("f'Hello {name}'", f'Hello {name}'),
        ("f'Age: {age}'", f'Age: {age}'),
        ("f'Score: {score:.1f}'", f'Score: {score:.1f}'),
        ("f'{name} is {age} years old'", f'{name} is {age} years old'),
        ("f'{name.upper()} scored {score:.2f}%'", f'{name.upper()} scored {score:.2f}%')
    ]
    
    for code, result in f_formats:
        print(f"   {code:35} = '{result}'")
    
    print("\n4. ADVANCED F-STRING FORMATTING:")
    import datetime
    now = datetime.datetime.now()
    
    advanced_formats = [
        ("f'{score:10.2f}'", f'{score:10.2f}', "width 10, 2 decimal"),
        ("f'{name:>10}'", f'{name:>10}', "right align, width 10"),
        ("f'{name:<10}'", f'{name:<10}', "left align, width 10"),
        ("f'{name:^10}'", f'{name:^10}', "center align, width 10"),
        ("f'{age:04d}'", f'{age:04d}', "zero padding"),
        ("f'{now:%Y-%m-%d}'", f'{now:%Y-%m-%d}', "date formatting")
    ]
    
    for code, result, desc in advanced_formats:
        print(f"   {code:20} = '{result}' ({desc})")

def demonstrate_regular_expressions():
    """Demonstrasi dasar regular expressions"""
    print("REGULAR EXPRESSIONS (REGEX) untuk pattern matching dalam string.")
    
    text = "Contact: <EMAIL> or call +62-************"
    print(f"Text: '{text}'")
    
    print("\n1. BASIC PATTERN MATCHING:")
    patterns = [
        (r'\w+@\w+\.\w+', "email pattern"),
        (r'\+\d{2}-\d{3}-\d{3}-\d{4}', "phone pattern"),
        (r'\b[A-Z][a-z]+\b', "capitalized words"),
        (r'\d+', "numbers")
    ]
    
    for pattern, desc in patterns:
        matches = re.findall(pattern, text)
        print(f"   Pattern: {pattern:20} ({desc})")
        print(f"   Matches: {matches}")
    
    print("\n2. STRING VALIDATION:")
    validation_patterns = [
        (r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', "email"),
        (r'^\+\d{1,3}-\d{3}-\d{3}-\d{4}$', "phone"),
        (r'^[A-Z][a-z]+ [A-Z][a-z]+$', "full name"),
        (r'^\d{4}-\d{2}-\d{2}$', "date YYYY-MM-DD")
    ]
    
    test_strings = [
        "<EMAIL>",
        "+62-************", 
        "John Doe",
        "2023-12-25",
        "invalid-email",
        "123-456"
    ]
    
    print("   Validation results:")
    for test_str in test_strings:
        print(f"   '{test_str}':")
        for pattern, desc in validation_patterns:
            is_valid = bool(re.match(pattern, test_str))
            print(f"     {desc:15}: {is_valid}")

def interactive_string_demo():
    """Demo interaktif untuk string operations"""
    print("\nDEMO INTERAKTIF STRING OPERATIONS")
    print("-" * 40)
    
    while True:
        print("\nPilih operasi:")
        print("1. Analisis string")
        print("2. String transformation")
        print("3. String search")
        print("4. Keluar")
        
        choice = input("Pilihan (1-4): ").strip()
        
        if choice == "1":
            text = input("Masukkan string: ")
            print(f"\nAnalisis untuk: '{text}'")
            print(f"Panjang: {len(text)}")
            print(f"Huruf: {sum(1 for c in text if c.isalpha())}")
            print(f"Angka: {sum(1 for c in text if c.isdigit())}")
            print(f"Spasi: {sum(1 for c in text if c.isspace())}")
            print(f"Karakter khusus: {sum(1 for c in text if not c.isalnum() and not c.isspace())}")
            print(f"Uppercase: {sum(1 for c in text if c.isupper())}")
            print(f"Lowercase: {sum(1 for c in text if c.islower())}")
            
        elif choice == "2":
            text = input("Masukkan string: ")
            print(f"\nTransformasi untuk: '{text}'")
            print(f"Upper: '{text.upper()}'")
            print(f"Lower: '{text.lower()}'")
            print(f"Title: '{text.title()}'")
            print(f"Reverse: '{text[::-1]}'")
            print(f"Remove spaces: '{text.replace(' ', '')}'")
            print(f"First 5 chars: '{text[:5]}'")
            print(f"Last 5 chars: '{text[-5:]}'")
            
        elif choice == "3":
            text = input("Masukkan string: ")
            search = input("Cari kata/karakter: ")
            
            print(f"\nPencarian '{search}' dalam '{text}':")
            print(f"Ditemukan: {search in text}")
            print(f"Posisi pertama: {text.find(search)}")
            print(f"Posisi terakhir: {text.rfind(search)}")
            print(f"Jumlah kemunculan: {text.count(search)}")
            print(f"Dimulai dengan '{search}': {text.startswith(search)}")
            print(f"Diakhiri dengan '{search}': {text.endswith(search)}")
            
        elif choice == "4":
            print("Terima kasih!")
            break
        else:
            print("Pilihan tidak valid!")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI CHARACTER, STRING, DAN SLICING PYTHON")
    print("==============================================")
    print("Program ini mendemonstrasikan manipulasi karakter dan string,")
    print("termasuk slicing, methods, dan formatting.")
    
    print_separator("CHARACTER")
    demonstrate_characters()
    
    print_separator("STRING BASICS")
    demonstrate_strings()
    
    print_separator("STRING SLICING")
    demonstrate_slicing()
    
    print_separator("STRING METHODS")
    demonstrate_string_methods()
    
    print_separator("STRING FORMATTING")
    demonstrate_string_formatting()
    
    print_separator("REGULAR EXPRESSIONS")
    demonstrate_regular_expressions()
    
    print_separator("DEMO INTERAKTIF")
    interactive_string_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN:")
    print("- Character: String dengan panjang 1, mendukung Unicode")
    print("- String: Immutable sequence of characters")
    print("- Slicing: [start:stop:step] untuk mengambil bagian string")
    print("- Methods: upper(), lower(), strip(), split(), join(), dll")
    print("- Formatting: %, .format(), f-strings")
    print("- Regex: Pattern matching untuk validasi dan pencarian")
    print("="*60)

if __name__ == "__main__":
    main()
