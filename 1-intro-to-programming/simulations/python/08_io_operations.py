"""
SIMULASI INPUT/OUTPUT OPERATIONS
===============================

I/O (Input/Output) adalah cara program berinteraksi dengan dunia luar.
Python menyediakan berbagai cara untuk melakukan I/O:

1. CONSOLE I/O: input(), print()
2. FILE I/O: open(), read(), write()
3. NETWORK/HTTP I/O: requests, urllib
4. SERIALIZATION: JSON, pickle, CSV

I/O penting karena memungkinkan program:
- Menerima input dari user
- Menyimpan dan membaca data
- Berkomunikasi dengan sistem lain
- Memproses data dalam berbagai format
"""

import json
import csv
import pickle
import os
import tempfile
from datetime import datetime
import urllib.request
import urllib.parse

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_console_io():
    """Demonstrasi Console I/O"""
    print("CONSOLE I/O adalah interaksi dengan user melalui terminal/command line.")
    
    print("\n1. OUTPUT DENGAN print():")
    print("   print('Hello World')")
    print("   Output: Hello World")
    
    print("\n   print() dengan berbagai parameter:")
    print("   print('A', 'B', 'C')")
    print("   Output:", end=" ")
    print('A', 'B', 'C')
    
    print("   print('A', 'B', 'C', sep='-')")
    print("   Output:", end=" ")
    print('A', 'B', 'C', sep='-')
    
    print("   print('Hello', end=' ')")
    print("   print('World')")
    print("   Output:", end=" ")
    print('Hello', end=' ')
    print('World')
    
    print("\n2. FORMATTED OUTPUT:")
    name = "Alice"
    age = 25
    score = 87.5
    
    print(f"   Data: name='{name}', age={age}, score={score}")
    print("   print(f'Name: {name}, Age: {age}, Score: {score:.1f}')")
    print(f"   Output: Name: {name}, Age: {age}, Score: {score:.1f}")
    
    print("\n3. INPUT DENGAN input():")
    print("   # Simulasi input karena ini adalah demo")
    print("   name = input('Masukkan nama: ')")
    print("   age = int(input('Masukkan umur: '))")
    
    # Simulasi input
    simulated_inputs = ["John Doe", "30"]
    print(f"   Simulasi input: {simulated_inputs}")
    
    sim_name = simulated_inputs[0]
    sim_age = int(simulated_inputs[1])
    print(f"   Hasil: name='{sim_name}', age={sim_age}")
    
    print("\n4. INPUT VALIDATION:")
    def get_valid_age():
        """Simulasi validasi input umur"""
        test_inputs = ["abc", "-5", "150", "25"]
        print("   def get_valid_age():")
        print("       while True:")
        print("           try:")
        print("               age = int(input('Umur: '))")
        print("               if 0 <= age <= 120:")
        print("                   return age")
        print("               else:")
        print("                   print('Umur harus 0-120')")
        print("           except ValueError:")
        print("               print('Input harus angka')")
        
        print(f"   Test inputs: {test_inputs}")
        for inp in test_inputs:
            print(f"   Input: '{inp}'")
            try:
                age = int(inp)
                if 0 <= age <= 120:
                    print(f"   Valid: {age}")
                    return age
                else:
                    print("   Error: Umur harus 0-120")
            except ValueError:
                print("   Error: Input harus angka")
    
    valid_age = get_valid_age()
    print(f"   Final result: {valid_age}")

def demonstrate_file_io():
    """Demonstrasi File I/O"""
    print("FILE I/O memungkinkan program menyimpan dan membaca data dari file.")
    
    # Buat temporary directory untuk demo
    temp_dir = tempfile.mkdtemp()
    print(f"Demo menggunakan temporary directory: {temp_dir}")
    
    print("\n1. MENULIS FILE:")
    file_path = os.path.join(temp_dir, "demo.txt")
    
    print("   # Menulis file dengan 'w' mode")
    print("   with open('demo.txt', 'w') as file:")
    print("       file.write('Hello World\\n')")
    print("       file.write('Python Programming\\n')")
    
    with open(file_path, 'w') as file:
        file.write('Hello World\n')
        file.write('Python Programming\n')
    
    print("   File berhasil ditulis!")
    
    print("\n2. MEMBACA FILE:")
    print("   # Membaca seluruh file")
    print("   with open('demo.txt', 'r') as file:")
    print("       content = file.read()")
    print("       print(content)")
    
    with open(file_path, 'r') as file:
        content = file.read()
        print("   Output:")
        for line in content.splitlines():
            print(f"     {line}")
    
    print("\n3. MEMBACA BARIS PER BARIS:")
    print("   with open('demo.txt', 'r') as file:")
    print("       for line_num, line in enumerate(file, 1):")
    print("           print(f'Line {line_num}: {line.strip()}')")
    
    with open(file_path, 'r') as file:
        print("   Output:")
        for line_num, line in enumerate(file, 1):
            print(f"     Line {line_num}: {line.strip()}")
    
    print("\n4. APPEND MODE:")
    print("   with open('demo.txt', 'a') as file:")
    print("       file.write('Appended line\\n')")
    
    with open(file_path, 'a') as file:
        file.write('Appended line\n')
    
    print("   File setelah append:")
    with open(file_path, 'r') as file:
        for line_num, line in enumerate(file, 1):
            print(f"     Line {line_num}: {line.strip()}")
    
    print("\n5. BINARY FILE:")
    binary_path = os.path.join(temp_dir, "demo.bin")
    
    print("   # Menulis binary data")
    print("   with open('demo.bin', 'wb') as file:")
    print("       file.write(b'Binary data')")
    
    with open(binary_path, 'wb') as file:
        file.write(b'Binary data')
    
    print("   # Membaca binary data")
    print("   with open('demo.bin', 'rb') as file:")
    print("       data = file.read()")
    print("       print(data)")
    
    with open(binary_path, 'rb') as file:
        data = file.read()
        print(f"   Output: {data}")
    
    print("\n6. FILE OPERATIONS:")
    print(f"   os.path.exists('{file_path}') = {os.path.exists(file_path)}")
    print(f"   os.path.getsize('{file_path}') = {os.path.getsize(file_path)} bytes")
    
    stat = os.stat(file_path)
    mod_time = datetime.fromtimestamp(stat.st_mtime)
    print(f"   Last modified: {mod_time}")
    
    # Cleanup
    os.remove(file_path)
    os.remove(binary_path)
    os.rmdir(temp_dir)
    print("   Temporary files cleaned up")

def demonstrate_json_serialization():
    """Demonstrasi JSON serialization"""
    print("JSON adalah format pertukaran data yang populer dan human-readable.")
    
    print("\n1. PYTHON OBJECT TO JSON:")
    data = {
        "name": "Alice",
        "age": 25,
        "skills": ["Python", "JavaScript", "SQL"],
        "active": True,
        "score": 87.5,
        "address": {
            "city": "Jakarta",
            "country": "Indonesia"
        }
    }
    
    print("   data =", data)
    print("   json_string = json.dumps(data, indent=2)")
    
    json_string = json.dumps(data, indent=2)
    print("   Output:")
    for line in json_string.splitlines():
        print(f"     {line}")
    
    print("\n2. JSON TO PYTHON OBJECT:")
    print("   parsed_data = json.loads(json_string)")
    parsed_data = json.loads(json_string)
    print(f"   parsed_data = {parsed_data}")
    print(f"   Type: {type(parsed_data)}")
    print(f"   parsed_data['name'] = '{parsed_data['name']}'")
    
    print("\n3. JSON FILE I/O:")
    temp_dir = tempfile.mkdtemp()
    json_path = os.path.join(temp_dir, "data.json")
    
    print("   # Menulis ke JSON file")
    print("   with open('data.json', 'w') as file:")
    print("       json.dump(data, file, indent=2)")
    
    with open(json_path, 'w') as file:
        json.dump(data, file, indent=2)
    
    print("   # Membaca dari JSON file")
    print("   with open('data.json', 'r') as file:")
    print("       loaded_data = json.load(file)")
    
    with open(json_path, 'r') as file:
        loaded_data = json.load(file)
    
    print(f"   loaded_data = {loaded_data}")
    print(f"   Data sama? {data == loaded_data}")
    
    # Cleanup
    os.remove(json_path)
    os.rmdir(temp_dir)

def demonstrate_csv_operations():
    """Demonstrasi CSV operations"""
    print("CSV (Comma-Separated Values) adalah format data tabular yang sederhana.")
    
    print("\n1. MENULIS CSV:")
    temp_dir = tempfile.mkdtemp()
    csv_path = os.path.join(temp_dir, "students.csv")
    
    students_data = [
        ["Name", "Age", "Grade", "Score"],
        ["Alice", 20, "A", 87.5],
        ["Bob", 19, "B", 78.0],
        ["Charlie", 21, "A", 92.5],
        ["Diana", 20, "B", 81.0]
    ]
    
    print("   students_data =")
    for row in students_data:
        print(f"     {row}")
    
    print("   with open('students.csv', 'w', newline='') as file:")
    print("       writer = csv.writer(file)")
    print("       writer.writerows(students_data)")
    
    with open(csv_path, 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerows(students_data)
    
    print("\n2. MEMBACA CSV:")
    print("   with open('students.csv', 'r') as file:")
    print("       reader = csv.reader(file)")
    print("       for row in reader:")
    print("           print(row)")
    
    with open(csv_path, 'r') as file:
        reader = csv.reader(file)
        print("   Output:")
        for row in reader:
            print(f"     {row}")
    
    print("\n3. CSV DENGAN DICTREADER:")
    print("   with open('students.csv', 'r') as file:")
    print("       reader = csv.DictReader(file)")
    print("       for row in reader:")
    print("           print(row)")
    
    with open(csv_path, 'r') as file:
        reader = csv.DictReader(file)
        print("   Output:")
        for row in reader:
            print(f"     {dict(row)}")
    
    print("\n4. FILTERING CSV DATA:")
    print("   # Siswa dengan score > 80")
    with open(csv_path, 'r') as file:
        reader = csv.DictReader(file)
        high_scorers = [row for row in reader if float(row['Score']) > 80]
    
    print("   high_scorers:")
    for student in high_scorers:
        print(f"     {student['Name']}: {student['Score']}")
    
    # Cleanup
    os.remove(csv_path)
    os.rmdir(temp_dir)

def demonstrate_pickle_serialization():
    """Demonstrasi Pickle serialization"""
    print("PICKLE adalah format serialization Python yang dapat menyimpan objek Python apa pun.")
    
    print("\n1. PICKLE BASIC OBJECTS:")
    data = {
        "string": "Hello",
        "number": 42,
        "list": [1, 2, 3],
        "tuple": (4, 5, 6),
        "set": {7, 8, 9},
        "dict": {"nested": "value"}
    }
    
    print("   data =", data)
    print("   pickled = pickle.dumps(data)")
    
    pickled = pickle.dumps(data)
    print(f"   Pickled data (first 50 bytes): {pickled[:50]}...")
    print(f"   Size: {len(pickled)} bytes")
    
    print("   unpickled = pickle.loads(pickled)")
    unpickled = pickle.loads(pickled)
    print(f"   unpickled = {unpickled}")
    print(f"   Data sama? {data == unpickled}")
    
    print("\n2. PICKLE CUSTOM OBJECTS:")
    class Person:
        def __init__(self, name, age):
            self.name = name
            self.age = age
        
        def __repr__(self):
            return f"Person('{self.name}', {self.age})"
    
    person = Person("Alice", 25)
    print(f"   person = {person}")
    
    pickled_person = pickle.dumps(person)
    unpickled_person = pickle.loads(pickled_person)
    
    print(f"   Setelah pickle/unpickle: {unpickled_person}")
    print(f"   Name: {unpickled_person.name}, Age: {unpickled_person.age}")
    
    print("\n3. PICKLE FILE I/O:")
    temp_dir = tempfile.mkdtemp()
    pickle_path = os.path.join(temp_dir, "data.pkl")
    
    complex_data = {
        "persons": [Person("Alice", 25), Person("Bob", 30)],
        "metadata": {"created": datetime.now(), "version": 1.0}
    }
    
    print("   # Menulis ke pickle file")
    print("   with open('data.pkl', 'wb') as file:")
    print("       pickle.dump(complex_data, file)")
    
    with open(pickle_path, 'wb') as file:
        pickle.dump(complex_data, file)
    
    print("   # Membaca dari pickle file")
    print("   with open('data.pkl', 'rb') as file:")
    print("       loaded_data = pickle.load(file)")
    
    with open(pickle_path, 'rb') as file:
        loaded_data = pickle.load(file)
    
    print(f"   Loaded persons: {loaded_data['persons']}")
    print(f"   Loaded metadata: {loaded_data['metadata']}")
    
    # Cleanup
    os.remove(pickle_path)
    os.rmdir(temp_dir)

def demonstrate_network_io():
    """Demonstrasi Network I/O (HTTP requests)"""
    print("NETWORK I/O memungkinkan program berkomunikasi dengan server web.")
    
    print("\n1. HTTP GET REQUEST (Simulasi):")
    print("   # Contoh dengan urllib (built-in)")
    print("   import urllib.request")
    print("   response = urllib.request.urlopen('https://httpbin.org/json')")
    print("   data = response.read()")
    print("   json_data = json.loads(data)")
    
    # Simulasi response karena mungkin tidak ada koneksi internet
    simulated_response = {
        "slideshow": {
            "author": "Yours Truly",
            "date": "date of publication",
            "slides": [
                {"title": "Wake up to WonderWidgets!", "type": "all"},
                {"title": "Overview", "type": "all"}
            ],
            "title": "Sample Slide Show"
        }
    }
    
    print("   Simulasi response:")
    print(f"   {json.dumps(simulated_response, indent=2)}")
    
    print("\n2. HTTP POST REQUEST (Simulasi):")
    print("   # Mengirim data ke server")
    print("   import urllib.parse")
    print("   data = urllib.parse.urlencode({'name': 'Alice', 'age': 25})")
    print("   request = urllib.request.Request('https://httpbin.org/post',")
    print("                                    data=data.encode('utf-8'))")
    print("   response = urllib.request.urlopen(request)")
    
    # Simulasi POST data
    post_data = {"name": "Alice", "age": 25}
    encoded_data = urllib.parse.urlencode(post_data)
    print(f"   Encoded data: {encoded_data}")
    
    print("\n3. ERROR HANDLING:")
    print("   try:")
    print("       response = urllib.request.urlopen('https://invalid-url.com')")
    print("   except urllib.error.URLError as e:")
    print("       print(f'Error: {e}')")
    print("   except urllib.error.HTTPError as e:")
    print("       print(f'HTTP Error {e.code}: {e.reason}')")
    
    print("   Simulasi error: URLError('Name or service not known')")
    
    print("\n4. WORKING WITH APIs:")
    print("   # Contoh struktur untuk API call")
    print("   def get_weather(city):")
    print("       url = f'https://api.weather.com/v1/current?q={city}'")
    print("       headers = {'API-Key': 'your-api-key'}")
    print("       request = urllib.request.Request(url, headers=headers)")
    print("       response = urllib.request.urlopen(request)")
    print("       return json.loads(response.read())")
    
    # Simulasi weather data
    simulated_weather = {
        "location": "Jakarta",
        "temperature": 28,
        "humidity": 75,
        "description": "Partly cloudy"
    }
    print(f"   Simulasi weather data: {simulated_weather}")

def interactive_io_demo():
    """Demo interaktif I/O operations"""
    print("\nDEMO INTERAKTIF I/O OPERATIONS")
    print("-" * 35)
    
    temp_dir = tempfile.mkdtemp()
    print(f"Working directory: {temp_dir}")
    
    while True:
        print("\nPilih operasi:")
        print("1. File operations")
        print("2. JSON operations")
        print("3. CSV operations")
        print("4. Data analysis")
        print("5. Keluar")
        
        choice = input("Pilihan (1-5): ").strip()
        
        if choice == "1":
            print("\nFile Operations:")
            filename = input("Nama file: ").strip()
            if not filename:
                filename = "demo.txt"
            
            filepath = os.path.join(temp_dir, filename)
            
            content = input("Isi file: ").strip()
            if not content:
                content = "Hello World from Python!"
            
            # Write file
            with open(filepath, 'w') as f:
                f.write(content)
            print(f"File '{filename}' berhasil ditulis")
            
            # Read file
            with open(filepath, 'r') as f:
                read_content = f.read()
            print(f"Isi file: '{read_content}'")
            print(f"Ukuran file: {os.path.getsize(filepath)} bytes")
            
        elif choice == "2":
            print("\nJSON Operations:")
            name = input("Nama: ").strip() or "Anonymous"
            try:
                age = int(input("Umur: ").strip() or "0")
            except ValueError:
                age = 0
            
            hobbies = input("Hobi (pisahkan dengan koma): ").strip()
            hobbies_list = [h.strip() for h in hobbies.split(",")] if hobbies else []
            
            data = {
                "name": name,
                "age": age,
                "hobbies": hobbies_list,
                "timestamp": datetime.now().isoformat()
            }
            
            json_file = os.path.join(temp_dir, "person.json")
            with open(json_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            print("Data JSON:")
            print(json.dumps(data, indent=2))
            
        elif choice == "3":
            print("\nCSV Operations:")
            csv_file = os.path.join(temp_dir, "data.csv")
            
            # Create sample CSV
            sample_data = [
                ["Name", "Age", "City"],
                ["Alice", "25", "Jakarta"],
                ["Bob", "30", "Bandung"],
                ["Charlie", "28", "Surabaya"]
            ]
            
            with open(csv_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerows(sample_data)
            
            print("Sample CSV created:")
            with open(csv_file, 'r') as f:
                reader = csv.reader(f)
                for i, row in enumerate(reader):
                    print(f"  Row {i}: {row}")
            
            # Filter data
            min_age = input("Filter umur minimum (enter untuk skip): ").strip()
            if min_age:
                try:
                    min_age = int(min_age)
                    with open(csv_file, 'r') as f:
                        reader = csv.DictReader(f)
                        filtered = [row for row in reader if int(row['Age']) >= min_age]
                    
                    print(f"Data dengan umur >= {min_age}:")
                    for row in filtered:
                        print(f"  {row}")
                except ValueError:
                    print("Umur harus berupa angka")
            
        elif choice == "4":
            print("\nData Analysis:")
            # Create sample data file
            data_file = os.path.join(temp_dir, "numbers.txt")
            numbers = [1, 5, 3, 9, 2, 8, 4, 7, 6]
            
            with open(data_file, 'w') as f:
                for num in numbers:
                    f.write(f"{num}\n")
            
            # Read and analyze
            with open(data_file, 'r') as f:
                data = [int(line.strip()) for line in f if line.strip()]
            
            print(f"Data: {data}")
            print(f"Jumlah: {len(data)}")
            print(f"Total: {sum(data)}")
            print(f"Rata-rata: {sum(data)/len(data):.2f}")
            print(f"Minimum: {min(data)}")
            print(f"Maksimum: {max(data)}")
            print(f"Terurut: {sorted(data)}")
            
        elif choice == "5":
            print("Membersihkan file temporary...")
            for file in os.listdir(temp_dir):
                os.remove(os.path.join(temp_dir, file))
            os.rmdir(temp_dir)
            print("Terima kasih!")
            break
        else:
            print("Pilihan tidak valid!")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI INPUT/OUTPUT OPERATIONS PYTHON")
    print("=======================================")
    print("Program ini mendemonstrasikan berbagai cara melakukan I/O")
    print("dalam Python: console, file, network, dan serialization.")
    
    print_separator("CONSOLE I/O")
    demonstrate_console_io()
    
    print_separator("FILE I/O")
    demonstrate_file_io()
    
    print_separator("JSON SERIALIZATION")
    demonstrate_json_serialization()
    
    print_separator("CSV OPERATIONS")
    demonstrate_csv_operations()
    
    print_separator("PICKLE SERIALIZATION")
    demonstrate_pickle_serialization()
    
    print_separator("NETWORK I/O")
    demonstrate_network_io()
    
    print_separator("DEMO INTERAKTIF")
    interactive_io_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN I/O OPERATIONS:")
    print("- Console: input(), print() untuk interaksi user")
    print("- File: open(), read(), write() untuk file operations")
    print("- JSON: json.dumps(), json.loads() untuk data exchange")
    print("- CSV: csv.reader(), csv.writer() untuk data tabular")
    print("- Pickle: pickle.dumps(), pickle.loads() untuk Python objects")
    print("- Network: urllib untuk HTTP requests")
    print("="*60)

if __name__ == "__main__":
    main()
