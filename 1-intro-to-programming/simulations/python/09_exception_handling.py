"""
SIMULASI EXCEPTION/ERROR HANDLING
=================================

Exception handling adalah mekanisme untuk menangani error yang terjadi
saat program berjalan. Tanpa exception handling, program akan crash
ketika terjadi error.

Konsep penting:
1. EXCEPTION: Error yang terjadi saat runtime
2. TRY-CATCH: Menangkap dan menangani exception
3. FINALLY: Kode yang selalu dijalankan
4. RAISE: Membuat exception sendiri
5. ASSERTIONS: Validasi kondisi program
6. LOGGING: Mencatat informasi dan error

Exception handling membuat program lebih robust dan user-friendly.
"""

import logging
import traceback
import sys
import os
import tempfile
from datetime import datetime

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_basic_exceptions():
    """Demonstrasi exception dasar"""
    print("EXCEPTION adalah error yang terjadi saat program berjalan.")
    print("Python memiliki banyak built-in exception types.")
    
    print("\n1. COMMON EXCEPTIONS:")
    
    # ZeroDivisionError
    print("   ZeroDivisionError:")
    print("   try:")
    print("       result = 10 / 0")
    print("   except ZeroDivisionError:")
    print("       print('Tidak bisa membagi dengan nol!')")
    
    try:
        result = 10 / 0
    except ZeroDivisionError:
        print("   Output: Tidak bisa membagi dengan nol!")
    
    # ValueError
    print("\n   ValueError:")
    print("   try:")
    print("       number = int('abc')")
    print("   except ValueError:")
    print("       print('Input bukan angka valid!')")
    
    try:
        number = int('abc')
    except ValueError:
        print("   Output: Input bukan angka valid!")
    
    # IndexError
    print("\n   IndexError:")
    print("   try:")
    print("       my_list = [1, 2, 3]")
    print("       item = my_list[10]")
    print("   except IndexError:")
    print("       print('Index di luar jangkauan!')")
    
    try:
        my_list = [1, 2, 3]
        item = my_list[10]
    except IndexError:
        print("   Output: Index di luar jangkauan!")
    
    # KeyError
    print("\n   KeyError:")
    print("   try:")
    print("       my_dict = {'a': 1, 'b': 2}")
    print("       value = my_dict['c']")
    print("   except KeyError:")
    print("       print('Key tidak ditemukan!')")
    
    try:
        my_dict = {'a': 1, 'b': 2}
        value = my_dict['c']
    except KeyError:
        print("   Output: Key tidak ditemukan!")
    
    # FileNotFoundError
    print("\n   FileNotFoundError:")
    print("   try:")
    print("       with open('file_tidak_ada.txt', 'r') as f:")
    print("           content = f.read()")
    print("   except FileNotFoundError:")
    print("       print('File tidak ditemukan!')")
    
    try:
        with open('file_tidak_ada.txt', 'r') as f:
            content = f.read()
    except FileNotFoundError:
        print("   Output: File tidak ditemukan!")

def demonstrate_try_except_patterns():
    """Demonstrasi pola try-except"""
    print("POLA TRY-EXCEPT untuk menangani berbagai skenario error.")
    
    print("\n1. BASIC TRY-EXCEPT:")
    def safe_divide(a, b):
        try:
            return a / b
        except ZeroDivisionError:
            return "Error: Pembagian dengan nol"
    
    print("   def safe_divide(a, b):")
    print("       try:")
    print("           return a / b")
    print("       except ZeroDivisionError:")
    print("           return 'Error: Pembagian dengan nol'")
    
    test_cases = [(10, 2), (10, 0), (15, 3)]
    for a, b in test_cases:
        result = safe_divide(a, b)
        print(f"   safe_divide({a}, {b}) = {result}")
    
    print("\n2. MULTIPLE EXCEPT BLOCKS:")
    def safe_convert_and_access(data, index):
        try:
            number = int(data[index])
            return number * 2
        except IndexError:
            return "Error: Index tidak valid"
        except ValueError:
            return "Error: Tidak bisa konversi ke integer"
        except TypeError:
            return "Error: Data bukan sequence"
    
    print("   def safe_convert_and_access(data, index): ...")
    
    test_data = [
        (['1', '2', '3'], 1),    # Normal case
        (['1', '2', '3'], 5),    # IndexError
        (['1', 'abc', '3'], 1),  # ValueError
        (None, 0)                # TypeError
    ]
    
    for data, index in test_data:
        result = safe_convert_and_access(data, index)
        print(f"   safe_convert_and_access({data}, {index}) = {result}")
    
    print("\n3. CATCH ALL EXCEPTIONS:")
    def risky_operation(operation):
        try:
            if operation == "divide":
                return 10 / 0
            elif operation == "convert":
                return int("abc")
            elif operation == "access":
                return [1, 2][10]
            else:
                return "Unknown operation"
        except Exception as e:
            return f"Error: {type(e).__name__}: {e}"
    
    print("   def risky_operation(operation): ...")
    print("       except Exception as e:")
    print("           return f'Error: {type(e).__name__}: {e}'")
    
    operations = ["divide", "convert", "access", "normal"]
    for op in operations:
        result = risky_operation(op)
        print(f"   risky_operation('{op}') = {result}")

def demonstrate_try_except_else_finally():
    """Demonstrasi try-except-else-finally"""
    print("TRY-EXCEPT-ELSE-FINALLY memberikan kontrol penuh atas exception handling.")
    
    print("\n1. COMPLETE STRUCTURE:")
    def file_processor(filename, create_if_missing=False):
        print(f"   Processing file: {filename}")
        file_handle = None
        
        try:
            print("   Trying to open file...")
            if create_if_missing and not os.path.exists(filename):
                # Create temporary file for demo
                temp_dir = tempfile.mkdtemp()
                filename = os.path.join(temp_dir, "demo.txt")
                with open(filename, 'w') as f:
                    f.write("Demo content")
            
            file_handle = open(filename, 'r')
            content = file_handle.read()
            
        except FileNotFoundError:
            print("   Exception: File not found")
            return "File tidak ditemukan"
        
        except PermissionError:
            print("   Exception: Permission denied")
            return "Tidak ada permission"
        
        else:
            print("   Else: File berhasil dibaca")
            return f"Content: {content[:50]}..."
        
        finally:
            print("   Finally: Cleanup resources")
            if file_handle and not file_handle.closed:
                file_handle.close()
                print("   File handle closed")
    
    print("   def file_processor(filename, create_if_missing=False): ...")
    
    # Test cases
    print("\n   Test 1: File tidak ada")
    result1 = file_processor("nonexistent.txt")
    print(f"   Result: {result1}")
    
    print("\n   Test 2: File dibuat dan dibaca")
    result2 = file_processor("demo.txt", create_if_missing=True)
    print(f"   Result: {result2}")

def demonstrate_raising_exceptions():
    """Demonstrasi membuat exception sendiri"""
    print("RAISING EXCEPTIONS memungkinkan kita membuat error sendiri.")
    
    print("\n1. RAISE BUILT-IN EXCEPTIONS:")
    def validate_age(age):
        if not isinstance(age, int):
            raise TypeError("Age harus berupa integer")
        if age < 0:
            raise ValueError("Age tidak boleh negatif")
        if age > 150:
            raise ValueError("Age terlalu besar")
        return f"Age {age} valid"
    
    print("   def validate_age(age): ...")
    print("       if not isinstance(age, int):")
    print("           raise TypeError('Age harus berupa integer')")
    print("       if age < 0:")
    print("           raise ValueError('Age tidak boleh negatif')")
    
    test_ages = [25, -5, 200, "abc"]
    for age in test_ages:
        try:
            result = validate_age(age)
            print(f"   validate_age({repr(age)}) = {result}")
        except (TypeError, ValueError) as e:
            print(f"   validate_age({repr(age)}) = Error: {e}")
    
    print("\n2. CUSTOM EXCEPTIONS:")
    class CustomError(Exception):
        """Custom exception class"""
        pass
    
    class ValidationError(Exception):
        """Exception untuk validation errors"""
        def __init__(self, field, value, message):
            self.field = field
            self.value = value
            self.message = message
            super().__init__(f"Validation error in {field}: {message}")
    
    def validate_user_data(data):
        if 'name' not in data:
            raise ValidationError('name', None, 'Name is required')
        
        if len(data['name']) < 2:
            raise ValidationError('name', data['name'], 'Name too short')
        
        if 'email' not in data:
            raise ValidationError('email', None, 'Email is required')
        
        if '@' not in data['email']:
            raise ValidationError('email', data['email'], 'Invalid email format')
        
        return "Data valid"
    
    print("   class ValidationError(Exception): ...")
    print("   def validate_user_data(data): ...")
    
    test_data = [
        {'name': 'Alice', 'email': '<EMAIL>'},  # Valid
        {'name': 'A'},                                   # Missing email
        {'name': 'Bob', 'email': 'invalid-email'},      # Invalid email
        {}                                               # Missing name
    ]
    
    for data in test_data:
        try:
            result = validate_user_data(data)
            print(f"   validate_user_data({data}) = {result}")
        except ValidationError as e:
            print(f"   validate_user_data({data}) = Error: {e}")

def demonstrate_assertions():
    """Demonstrasi assertions"""
    print("ASSERTIONS digunakan untuk validasi kondisi yang harus True.")
    print("Jika kondisi False, AssertionError akan di-raise.")
    
    print("\n1. BASIC ASSERTIONS:")
    def calculate_average(numbers):
        assert len(numbers) > 0, "List tidak boleh kosong"
        assert all(isinstance(n, (int, float)) for n in numbers), "Semua element harus angka"
        
        return sum(numbers) / len(numbers)
    
    print("   def calculate_average(numbers):")
    print("       assert len(numbers) > 0, 'List tidak boleh kosong'")
    print("       assert all(isinstance(n, (int, float)) for n in numbers), 'Semua element harus angka'")
    print("       return sum(numbers) / len(numbers)")
    
    test_cases = [
        [1, 2, 3, 4, 5],      # Valid
        [],                    # Empty list
        [1, 2, 'abc', 4],     # Invalid type
        [10, 20, 30]          # Valid
    ]
    
    for numbers in test_cases:
        try:
            result = calculate_average(numbers)
            print(f"   calculate_average({numbers}) = {result}")
        except AssertionError as e:
            print(f"   calculate_average({numbers}) = AssertionError: {e}")
    
    print("\n2. ASSERTIONS FOR DEBUGGING:")
    def binary_search(arr, target):
        # Pre-conditions
        assert isinstance(arr, list), "Array harus berupa list"
        assert len(arr) > 0, "Array tidak boleh kosong"
        assert arr == sorted(arr), "Array harus sudah terurut"
        
        left, right = 0, len(arr) - 1
        
        while left <= right:
            mid = (left + right) // 2
            
            # Invariant: target ada di range [left, right] jika ada
            assert 0 <= mid < len(arr), f"Mid index {mid} out of bounds"
            
            if arr[mid] == target:
                return mid
            elif arr[mid] < target:
                left = mid + 1
            else:
                right = mid - 1
        
        return -1
    
    print("   def binary_search(arr, target): ...")
    print("       assert arr == sorted(arr), 'Array harus sudah terurut'")
    
    test_arrays = [
        ([1, 3, 5, 7, 9], 5),     # Valid sorted array
        ([9, 7, 5, 3, 1], 5),     # Unsorted array
        ([1, 2, 3, 4], 3)         # Valid
    ]
    
    for arr, target in test_arrays:
        try:
            result = binary_search(arr, target)
            print(f"   binary_search({arr}, {target}) = {result}")
        except AssertionError as e:
            print(f"   binary_search({arr}, {target}) = AssertionError: {e}")

def demonstrate_logging():
    """Demonstrasi logging"""
    print("LOGGING adalah cara yang lebih baik untuk mencatat informasi dan error")
    print("dibandingkan dengan print statements.")
    
    # Setup logging
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, "app.log")
    
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    
    print("\n1. LOGGING LEVELS:")
    print("   import logging")
    print("   logger = logging.getLogger(__name__)")
    
    print("\n   Logging different levels:")
    logger.debug("Debug message - detail informasi untuk debugging")
    logger.info("Info message - informasi umum")
    logger.warning("Warning message - peringatan")
    logger.error("Error message - error yang terjadi")
    logger.critical("Critical message - error kritis")
    
    print("\n2. LOGGING DENGAN EXCEPTION:")
    def divide_with_logging(a, b):
        logger.info(f"Attempting to divide {a} by {b}")
        
        try:
            result = a / b
            logger.info(f"Division successful: {a} / {b} = {result}")
            return result
        except ZeroDivisionError:
            logger.error(f"Division by zero attempted: {a} / {b}")
            logger.exception("Exception details:")  # Logs full traceback
            return None
        except Exception as e:
            logger.error(f"Unexpected error in division: {e}")
            logger.exception("Exception details:")
            return None
    
    print("   def divide_with_logging(a, b): ...")
    print("       logger.info(f'Attempting to divide {a} by {b}')")
    print("       try:")
    print("           result = a / b")
    print("           logger.info(f'Division successful: {a} / {b} = {result}')")
    print("       except ZeroDivisionError:")
    print("           logger.error(f'Division by zero attempted: {a} / {b}')")
    print("           logger.exception('Exception details:')")
    
    test_divisions = [(10, 2), (10, 0), (15, 3)]
    for a, b in test_divisions:
        result = divide_with_logging(a, b)
    
    print(f"\n3. LOG FILE CONTENT:")
    print(f"   Log file: {log_file}")
    try:
        with open(log_file, 'r') as f:
            log_content = f.read()
            print("   Log entries:")
            for line in log_content.splitlines()[-5:]:  # Show last 5 lines
                print(f"     {line}")
    except Exception as e:
        print(f"   Error reading log file: {e}")
    
    # Cleanup
    try:
        os.remove(log_file)
        os.rmdir(temp_dir)
    except:
        pass

def demonstrate_exception_chaining():
    """Demonstrasi exception chaining"""
    print("EXCEPTION CHAINING memungkinkan kita menangkap exception dan")
    print("membuat exception baru sambil mempertahankan informasi asli.")
    
    print("\n1. EXCEPTION CHAINING WITH 'raise from':")
    
    def process_data(data):
        try:
            # Simulate data processing
            if not isinstance(data, dict):
                raise TypeError("Data must be a dictionary")
            
            result = int(data['value']) * 2
            return result
            
        except KeyError as e:
            raise ValueError(f"Missing required field: {e}") from e
        except (TypeError, ValueError) as e:
            raise RuntimeError("Data processing failed") from e
    
    print("   def process_data(data): ...")
    print("       except KeyError as e:")
    print("           raise ValueError(f'Missing required field: {e}') from e")
    
    test_data = [
        {'value': '10'},      # Valid
        {'name': 'test'},     # Missing 'value' key
        'not a dict',         # Wrong type
        {'value': 'abc'}      # Invalid value
    ]
    
    for data in test_data:
        try:
            result = process_data(data)
            print(f"   process_data({data}) = {result}")
        except Exception as e:
            print(f"   process_data({data}) = {type(e).__name__}: {e}")
            if e.__cause__:
                print(f"     Caused by: {type(e.__cause__).__name__}: {e.__cause__}")

def interactive_exception_demo():
    """Demo interaktif exception handling"""
    print("\nDEMO INTERAKTIF EXCEPTION HANDLING")
    print("-" * 40)
    
    while True:
        print("\nPilih demo:")
        print("1. Calculator dengan error handling")
        print("2. File operations dengan error handling")
        print("3. Data validation")
        print("4. Keluar")
        
        choice = input("Pilihan (1-4): ").strip()
        
        if choice == "1":
            print("\nCalculator dengan Error Handling:")
            try:
                a = float(input("Angka pertama: "))
                op = input("Operator (+, -, *, /): ").strip()
                b = float(input("Angka kedua: "))
                
                if op == "+":
                    result = a + b
                elif op == "-":
                    result = a - b
                elif op == "*":
                    result = a * b
                elif op == "/":
                    if b == 0:
                        raise ZeroDivisionError("Tidak bisa membagi dengan nol")
                    result = a / b
                else:
                    raise ValueError(f"Operator '{op}' tidak valid")
                
                print(f"Hasil: {a} {op} {b} = {result}")
                
            except ValueError as e:
                print(f"Error: {e}")
            except ZeroDivisionError as e:
                print(f"Error: {e}")
            except Exception as e:
                print(f"Unexpected error: {e}")
        
        elif choice == "2":
            print("\nFile Operations dengan Error Handling:")
            filename = input("Nama file: ").strip()
            
            try:
                # Try to read file
                with open(filename, 'r') as f:
                    content = f.read()
                    print(f"File content ({len(content)} characters):")
                    print(content[:200] + "..." if len(content) > 200 else content)
                    
            except FileNotFoundError:
                print(f"File '{filename}' tidak ditemukan.")
                create = input("Buat file baru? (y/n): ").strip().lower()
                if create == 'y':
                    try:
                        content = input("Isi file: ")
                        with open(filename, 'w') as f:
                            f.write(content)
                        print(f"File '{filename}' berhasil dibuat.")
                    except Exception as e:
                        print(f"Error creating file: {e}")
                        
            except PermissionError:
                print(f"Tidak ada permission untuk membaca '{filename}'")
            except Exception as e:
                print(f"Unexpected error: {e}")
        
        elif choice == "3":
            print("\nData Validation:")
            try:
                name = input("Nama (min 2 karakter): ").strip()
                if len(name) < 2:
                    raise ValueError("Nama harus minimal 2 karakter")
                
                age = input("Umur: ").strip()
                age = int(age)
                if not 0 <= age <= 150:
                    raise ValueError("Umur harus antara 0-150")
                
                email = input("Email: ").strip()
                if '@' not in email or '.' not in email:
                    raise ValueError("Format email tidak valid")
                
                print(f"Data valid: {name}, {age} tahun, {email}")
                
            except ValueError as e:
                print(f"Validation error: {e}")
            except Exception as e:
                print(f"Unexpected error: {e}")
        
        elif choice == "4":
            print("Terima kasih!")
            break
        else:
            print("Pilihan tidak valid!")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI EXCEPTION/ERROR HANDLING PYTHON")
    print("========================================")
    print("Program ini mendemonstrasikan cara menangani error dan exception")
    print("untuk membuat program yang robust dan user-friendly.")
    
    print_separator("BASIC EXCEPTIONS")
    demonstrate_basic_exceptions()
    
    print_separator("TRY-EXCEPT PATTERNS")
    demonstrate_try_except_patterns()
    
    print_separator("TRY-EXCEPT-ELSE-FINALLY")
    demonstrate_try_except_else_finally()
    
    print_separator("RAISING EXCEPTIONS")
    demonstrate_raising_exceptions()
    
    print_separator("ASSERTIONS")
    demonstrate_assertions()
    
    print_separator("LOGGING")
    demonstrate_logging()
    
    print_separator("EXCEPTION CHAINING")
    demonstrate_exception_chaining()
    
    print_separator("DEMO INTERAKTIF")
    interactive_exception_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN EXCEPTION HANDLING:")
    print("- try/except: Menangkap dan menangani exception")
    print("- finally: Kode yang selalu dijalankan")
    print("- raise: Membuat exception sendiri")
    print("- assert: Validasi kondisi program")
    print("- logging: Mencatat informasi dan error")
    print("- Exception chaining: Menghubungkan exception")
    print("="*60)

if __name__ == "__main__":
    main()
