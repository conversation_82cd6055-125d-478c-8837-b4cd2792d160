"""
SIMULASI STRUKTUR DATA LINIER
=============================

Struktur data linier adalah struktur data di mana elemen-elemen
disusun secara berurutan/sequential. Setiap elemen memiliki posisi
yang unik dan dapat diakses berdasarkan indeks atau posisi.

Jenis struktur data linier:
1. ARRAY: Koleksi elemen dengan ukuran tetap (dalam Python: list)
2. LIST: Koleksi elemen yang dapat berubah ukuran
3. STACK: Last In First Out (LIFO) - seperti tumpukan piring
4. QUEUE: First In First Out (FIFO) - seperti antrian
5. DEQUE: Double-ended queue - dapat diakses dari kedua ujung

Struktur data linier penting untuk menyimpan dan mengorganisir data.
"""

from collections import deque
import time

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_arrays_lists():
    """Demonstrasi Array dan List"""
    print("ARRAY/LIST adalah struktur data paling dasar untuk menyimpan")
    print("koleksi elemen yang dapat diakses dengan indeks.")
    
    print("\n1. PEMBUATAN LIST:")
    # Berbagai cara membuat list
    empty_list = []
    numbers = [1, 2, 3, 4, 5]
    mixed = [1, "hello", 3.14, True]
    nested = [[1, 2], [3, 4], [5, 6]]
    
    print(f"   empty_list = {empty_list}")
    print(f"   numbers = {numbers}")
    print(f"   mixed = {mixed}")
    print(f"   nested = {nested}")
    
    # List comprehension
    squares = [x**2 for x in range(5)]
    evens = [x for x in range(10) if x % 2 == 0]
    
    print(f"   squares = [x**2 for x in range(5)] = {squares}")
    print(f"   evens = [x for x in range(10) if x % 2 == 0] = {evens}")
    
    print("\n2. AKSES ELEMEN:")
    fruits = ["apple", "banana", "orange", "grape", "kiwi"]
    print(f"   fruits = {fruits}")
    print(f"   fruits[0] = '{fruits[0]}' (elemen pertama)")
    print(f"   fruits[-1] = '{fruits[-1]}' (elemen terakhir)")
    print(f"   fruits[1:4] = {fruits[1:4]} (slicing)")
    print(f"   fruits[:3] = {fruits[:3]} (3 elemen pertama)")
    print(f"   fruits[2:] = {fruits[2:]} (dari index 2 sampai akhir)")
    
    print("\n3. MODIFIKASI LIST:")
    numbers = [1, 2, 3]
    print(f"   Original: numbers = {numbers}")
    
    # Append
    numbers.append(4)
    print(f"   numbers.append(4) -> {numbers}")
    
    # Insert
    numbers.insert(1, 1.5)
    print(f"   numbers.insert(1, 1.5) -> {numbers}")
    
    # Extend
    numbers.extend([5, 6])
    print(f"   numbers.extend([5, 6]) -> {numbers}")
    
    # Remove
    numbers.remove(1.5)
    print(f"   numbers.remove(1.5) -> {numbers}")
    
    # Pop
    last = numbers.pop()
    print(f"   last = numbers.pop() -> last={last}, numbers={numbers}")
    
    # Index assignment
    numbers[0] = 0
    print(f"   numbers[0] = 0 -> {numbers}")
    
    print("\n4. LIST METHODS:")
    data = [3, 1, 4, 1, 5, 9, 2, 6]
    print(f"   data = {data}")
    print(f"   len(data) = {len(data)}")
    print(f"   data.count(1) = {data.count(1)}")
    print(f"   data.index(4) = {data.index(4)}")
    print(f"   max(data) = {max(data)}")
    print(f"   min(data) = {min(data)}")
    print(f"   sum(data) = {sum(data)}")
    
    # Sort
    data_copy = data.copy()
    data_copy.sort()
    print(f"   data.sort() -> {data_copy}")
    
    # Reverse
    data.reverse()
    print(f"   data.reverse() -> {data}")
    
    print("\n5. LIST PERFORMANCE:")
    print("   Operasi dan kompleksitas waktu:")
    print("   - Akses by index: O(1)")
    print("   - Append: O(1)")
    print("   - Insert di awal: O(n)")
    print("   - Search: O(n)")
    print("   - Sort: O(n log n)")

def demonstrate_stack():
    """Demonstrasi Stack (LIFO)"""
    print("STACK adalah struktur data LIFO (Last In, First Out).")
    print("Seperti tumpukan piring - yang terakhir ditaruh, pertama diambil.")
    
    print("\n1. IMPLEMENTASI STACK DENGAN LIST:")
    
    class Stack:
        def __init__(self):
            self.items = []
        
        def push(self, item):
            """Menambah item ke top of stack"""
            self.items.append(item)
            print(f"     Push {item}: {self.items}")
        
        def pop(self):
            """Mengambil item dari top of stack"""
            if self.is_empty():
                raise IndexError("Stack kosong")
            item = self.items.pop()
            print(f"     Pop {item}: {self.items}")
            return item
        
        def peek(self):
            """Melihat item di top tanpa mengambil"""
            if self.is_empty():
                raise IndexError("Stack kosong")
            return self.items[-1]
        
        def is_empty(self):
            """Cek apakah stack kosong"""
            return len(self.items) == 0
        
        def size(self):
            """Ukuran stack"""
            return len(self.items)
        
        def __str__(self):
            return f"Stack({self.items})"
    
    print("   class Stack: ...")
    print("   Operasi stack:")
    
    stack = Stack()
    print(f"   stack = Stack() -> {stack}")
    
    # Push operations
    print("   Push operations:")
    for item in [1, 2, 3, 4, 5]:
        stack.push(item)
    
    print(f"   stack.peek() = {stack.peek()} (top element)")
    print(f"   stack.size() = {stack.size()}")
    
    # Pop operations
    print("   Pop operations:")
    while not stack.is_empty():
        stack.pop()
    
    print("\n2. CONTOH PENGGUNAAN STACK:")
    
    def check_balanced_parentheses(expression):
        """Cek apakah tanda kurung seimbang"""
        stack = Stack()
        pairs = {'(': ')', '[': ']', '{': '}'}
        
        for char in expression:
            if char in pairs:  # Opening bracket
                stack.push(char)
            elif char in pairs.values():  # Closing bracket
                if stack.is_empty():
                    return False
                if pairs[stack.pop()] != char:
                    return False
        
        return stack.is_empty()
    
    print("   def check_balanced_parentheses(expression): ...")
    
    test_expressions = [
        "()",
        "(())",
        "([{}])",
        "(()",
        "([)]",
        "{[()()]}"
    ]
    
    for expr in test_expressions:
        result = check_balanced_parentheses(expr)
        print(f"   '{expr}' -> {'Balanced' if result else 'Not balanced'}")
    
    def reverse_string(text):
        """Membalik string menggunakan stack"""
        stack = Stack()
        
        # Push semua karakter ke stack
        for char in text:
            stack.push(char)
        
        # Pop semua karakter untuk mendapat reverse
        reversed_text = ""
        while not stack.is_empty():
            reversed_text += stack.pop()
        
        return reversed_text
    
    print("\n   def reverse_string(text): ...")
    test_strings = ["hello", "python", "12345"]
    for text in test_strings:
        reversed_text = reverse_string(text)
        print(f"   reverse_string('{text}') = '{reversed_text}'")

def demonstrate_queue():
    """Demonstrasi Queue (FIFO)"""
    print("QUEUE adalah struktur data FIFO (First In, First Out).")
    print("Seperti antrian - yang pertama datang, pertama dilayani.")
    
    print("\n1. IMPLEMENTASI QUEUE DENGAN DEQUE:")
    
    class Queue:
        def __init__(self):
            self.items = deque()
        
        def enqueue(self, item):
            """Menambah item ke belakang queue"""
            self.items.append(item)
            print(f"     Enqueue {item}: {list(self.items)}")
        
        def dequeue(self):
            """Mengambil item dari depan queue"""
            if self.is_empty():
                raise IndexError("Queue kosong")
            item = self.items.popleft()
            print(f"     Dequeue {item}: {list(self.items)}")
            return item
        
        def front(self):
            """Melihat item di depan tanpa mengambil"""
            if self.is_empty():
                raise IndexError("Queue kosong")
            return self.items[0]
        
        def is_empty(self):
            """Cek apakah queue kosong"""
            return len(self.items) == 0
        
        def size(self):
            """Ukuran queue"""
            return len(self.items)
        
        def __str__(self):
            return f"Queue({list(self.items)})"
    
    print("   class Queue: ...")
    print("   Operasi queue:")
    
    queue = Queue()
    print(f"   queue = Queue() -> {queue}")
    
    # Enqueue operations
    print("   Enqueue operations:")
    for item in ['A', 'B', 'C', 'D', 'E']:
        queue.enqueue(item)
    
    print(f"   queue.front() = '{queue.front()}' (front element)")
    print(f"   queue.size() = {queue.size()}")
    
    # Dequeue operations
    print("   Dequeue operations:")
    while not queue.is_empty():
        queue.dequeue()
    
    print("\n2. CONTOH PENGGUNAAN QUEUE:")
    
    def simulate_printer_queue():
        """Simulasi antrian printer"""
        print("   Simulasi antrian printer:")
        printer_queue = Queue()
        
        # Jobs datang
        jobs = ["Document1.pdf", "Photo.jpg", "Report.docx", "Presentation.pptx"]
        print("   Jobs masuk ke antrian:")
        for job in jobs:
            printer_queue.enqueue(job)
        
        # Printer memproses jobs
        print("   Printer memproses jobs:")
        while not printer_queue.is_empty():
            current_job = printer_queue.dequeue()
            print(f"     Printing: {current_job}")
            time.sleep(0.1)  # Simulasi waktu print
        
        print("   Semua jobs selesai!")
    
    simulate_printer_queue()
    
    def breadth_first_search_demo():
        """Demo BFS menggunakan queue"""
        print("\n   BFS (Breadth-First Search) demo:")
        
        # Graph representation (adjacency list)
        graph = {
            'A': ['B', 'C'],
            'B': ['A', 'D', 'E'],
            'C': ['A', 'F'],
            'D': ['B'],
            'E': ['B', 'F'],
            'F': ['C', 'E']
        }
        
        def bfs(graph, start):
            visited = set()
            queue = Queue()
            result = []
            
            queue.enqueue(start)
            visited.add(start)
            
            while not queue.is_empty():
                vertex = queue.dequeue()
                result.append(vertex)
                
                for neighbor in graph[vertex]:
                    if neighbor not in visited:
                        visited.add(neighbor)
                        queue.enqueue(neighbor)
            
            return result
        
        print(f"   Graph: {graph}")
        bfs_result = bfs(graph, 'A')
        print(f"   BFS from 'A': {bfs_result}")
    
    breadth_first_search_demo()

def demonstrate_deque():
    """Demonstrasi Deque (Double-ended Queue)"""
    print("DEQUE adalah struktur data yang memungkinkan penambahan dan")
    print("penghapusan elemen dari kedua ujung dengan efisien.")
    
    print("\n1. OPERASI DEQUE:")
    
    dq = deque()
    print(f"   dq = deque() -> {list(dq)}")
    
    # Append operations
    print("   Append operations:")
    dq.append('right1')
    print(f"   dq.append('right1') -> {list(dq)}")
    
    dq.appendleft('left1')
    print(f"   dq.appendleft('left1') -> {list(dq)}")
    
    dq.append('right2')
    print(f"   dq.append('right2') -> {list(dq)}")
    
    dq.appendleft('left2')
    print(f"   dq.appendleft('left2') -> {list(dq)}")
    
    # Pop operations
    print("   Pop operations:")
    right = dq.pop()
    print(f"   dq.pop() = '{right}' -> {list(dq)}")
    
    left = dq.popleft()
    print(f"   dq.popleft() = '{left}' -> {list(dq)}")
    
    print("\n2. DEQUE DENGAN MAXLEN:")
    # Circular buffer
    circular = deque(maxlen=3)
    print(f"   circular = deque(maxlen=3)")
    
    for i in range(5):
        circular.append(i)
        print(f"   circular.append({i}) -> {list(circular)}")
    
    print("\n3. CONTOH PENGGUNAAN DEQUE:")
    
    def sliding_window_maximum(arr, k):
        """Mencari maksimum dalam sliding window"""
        if not arr or k <= 0:
            return []
        
        dq = deque()  # Menyimpan indeks
        result = []
        
        for i in range(len(arr)):
            # Hapus elemen yang keluar dari window
            while dq and dq[0] <= i - k:
                dq.popleft()
            
            # Hapus elemen yang lebih kecil dari elemen saat ini
            while dq and arr[dq[-1]] <= arr[i]:
                dq.pop()
            
            dq.append(i)
            
            # Jika window sudah penuh, tambahkan maksimum ke result
            if i >= k - 1:
                result.append(arr[dq[0]])
        
        return result
    
    print("   def sliding_window_maximum(arr, k): ...")
    
    test_array = [1, 3, -1, -3, 5, 3, 6, 7]
    window_size = 3
    
    print(f"   Array: {test_array}")
    print(f"   Window size: {window_size}")
    
    result = sliding_window_maximum(test_array, window_size)
    print(f"   Sliding window maximums: {result}")
    
    # Show windows
    print("   Windows:")
    for i in range(len(test_array) - window_size + 1):
        window = test_array[i:i + window_size]
        maximum = max(window)
        print(f"     {window} -> max = {maximum}")

def demonstrate_performance_comparison():
    """Demonstrasi perbandingan performa struktur data"""
    print("PERBANDINGAN PERFORMA berbagai operasi pada struktur data linier.")
    
    print("\n1. LIST vs DEQUE PERFORMANCE:")
    
    import time
    
    def time_operation(operation, iterations=10000):
        """Mengukur waktu eksekusi operasi"""
        start_time = time.time()
        operation(iterations)
        end_time = time.time()
        return end_time - start_time
    
    def list_append_left(n):
        """Append di awal list"""
        lst = []
        for i in range(n):
            lst.insert(0, i)
    
    def deque_append_left(n):
        """Append di awal deque"""
        dq = deque()
        for i in range(n):
            dq.appendleft(i)
    
    def list_append_right(n):
        """Append di akhir list"""
        lst = []
        for i in range(n):
            lst.append(i)
    
    def deque_append_right(n):
        """Append di akhir deque"""
        dq = deque()
        for i in range(n):
            dq.append(i)
    
    iterations = 1000
    print(f"   Testing dengan {iterations} operasi:")
    
    # Test append left
    list_left_time = time_operation(list_append_left, iterations)
    deque_left_time = time_operation(deque_append_left, iterations)
    
    print(f"   Append di awal:")
    print(f"     List: {list_left_time:.6f} seconds")
    print(f"     Deque: {deque_left_time:.6f} seconds")
    print(f"     Deque {list_left_time/deque_left_time:.1f}x lebih cepat")
    
    # Test append right
    list_right_time = time_operation(list_append_right, iterations)
    deque_right_time = time_operation(deque_append_right, iterations)
    
    print(f"   Append di akhir:")
    print(f"     List: {list_right_time:.6f} seconds")
    print(f"     Deque: {deque_right_time:.6f} seconds")
    
    print("\n2. KOMPLEKSITAS WAKTU:")
    complexity_table = [
        ("Operation", "List", "Deque"),
        ("Append right", "O(1)", "O(1)"),
        ("Append left", "O(n)", "O(1)"),
        ("Pop right", "O(1)", "O(1)"),
        ("Pop left", "O(n)", "O(1)"),
        ("Access by index", "O(1)", "O(n)"),
        ("Search", "O(n)", "O(n)")
    ]
    
    print("   " + "-" * 40)
    for row in complexity_table:
        print(f"   {row[0]:<15} | {row[1]:<6} | {row[2]:<6}")
    print("   " + "-" * 40)

def interactive_data_structure_demo():
    """Demo interaktif struktur data linier"""
    print("\nDEMO INTERAKTIF STRUKTUR DATA LINIER")
    print("-" * 45)
    
    stack = Stack()
    queue = Queue()
    my_list = []
    
    while True:
        print(f"\nStatus:")
        print(f"  List: {my_list}")
        print(f"  Stack: {stack}")
        print(f"  Queue: {queue}")
        
        print("\nPilih operasi:")
        print("1. List operations")
        print("2. Stack operations")
        print("3. Queue operations")
        print("4. Keluar")
        
        choice = input("Pilihan (1-4): ").strip()
        
        if choice == "1":
            print("\nList Operations:")
            print("a. Append")
            print("b. Insert")
            print("c. Remove")
            print("d. Pop")
            print("e. Sort")
            
            op = input("Operasi (a-e): ").strip().lower()
            
            if op == 'a':
                item = input("Item to append: ").strip()
                my_list.append(item)
                print(f"Appended '{item}'")
            
            elif op == 'b':
                try:
                    index = int(input("Index: "))
                    item = input("Item: ").strip()
                    my_list.insert(index, item)
                    print(f"Inserted '{item}' at index {index}")
                except (ValueError, IndexError) as e:
                    print(f"Error: {e}")
            
            elif op == 'c':
                if my_list:
                    item = input("Item to remove: ").strip()
                    try:
                        my_list.remove(item)
                        print(f"Removed '{item}'")
                    except ValueError:
                        print(f"'{item}' not found")
                else:
                    print("List is empty")
            
            elif op == 'd':
                if my_list:
                    item = my_list.pop()
                    print(f"Popped '{item}'")
                else:
                    print("List is empty")
            
            elif op == 'e':
                try:
                    # Try to sort as numbers
                    numeric_list = [float(x) for x in my_list]
                    my_list = sorted(my_list, key=lambda x: float(x))
                    print("Sorted as numbers")
                except ValueError:
                    # Sort as strings
                    my_list.sort()
                    print("Sorted as strings")
        
        elif choice == "2":
            print("\nStack Operations:")
            print("a. Push")
            print("b. Pop")
            print("c. Peek")
            
            op = input("Operasi (a-c): ").strip().lower()
            
            if op == 'a':
                item = input("Item to push: ").strip()
                stack.push(item)
            
            elif op == 'b':
                try:
                    item = stack.pop()
                    print(f"Popped: {item}")
                except IndexError as e:
                    print(f"Error: {e}")
            
            elif op == 'c':
                try:
                    item = stack.peek()
                    print(f"Top item: {item}")
                except IndexError as e:
                    print(f"Error: {e}")
        
        elif choice == "3":
            print("\nQueue Operations:")
            print("a. Enqueue")
            print("b. Dequeue")
            print("c. Front")
            
            op = input("Operasi (a-c): ").strip().lower()
            
            if op == 'a':
                item = input("Item to enqueue: ").strip()
                queue.enqueue(item)
            
            elif op == 'b':
                try:
                    item = queue.dequeue()
                    print(f"Dequeued: {item}")
                except IndexError as e:
                    print(f"Error: {e}")
            
            elif op == 'c':
                try:
                    item = queue.front()
                    print(f"Front item: {item}")
                except IndexError as e:
                    print(f"Error: {e}")
        
        elif choice == "4":
            print("Terima kasih!")
            break
        else:
            print("Pilihan tidak valid!")

# Define Stack and Queue classes at module level for interactive demo
class Stack:
    def __init__(self):
        self.items = []
    
    def push(self, item):
        self.items.append(item)
    
    def pop(self):
        if self.is_empty():
            raise IndexError("Stack kosong")
        return self.items.pop()
    
    def peek(self):
        if self.is_empty():
            raise IndexError("Stack kosong")
        return self.items[-1]
    
    def is_empty(self):
        return len(self.items) == 0
    
    def size(self):
        return len(self.items)
    
    def __str__(self):
        return f"Stack({self.items})"

class Queue:
    def __init__(self):
        self.items = deque()
    
    def enqueue(self, item):
        self.items.append(item)
    
    def dequeue(self):
        if self.is_empty():
            raise IndexError("Queue kosong")
        return self.items.popleft()
    
    def front(self):
        if self.is_empty():
            raise IndexError("Queue kosong")
        return self.items[0]
    
    def is_empty(self):
        return len(self.items) == 0
    
    def size(self):
        return len(self.items)
    
    def __str__(self):
        return f"Queue({list(self.items)})"

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI STRUKTUR DATA LINIER PYTHON")
    print("====================================")
    print("Program ini mendemonstrasikan struktur data linier:")
    print("Array/List, Stack, Queue, dan Deque.")
    
    print_separator("ARRAY DAN LIST")
    demonstrate_arrays_lists()
    
    print_separator("STACK (LIFO)")
    demonstrate_stack()
    
    print_separator("QUEUE (FIFO)")
    demonstrate_queue()
    
    print_separator("DEQUE (DOUBLE-ENDED QUEUE)")
    demonstrate_deque()
    
    print_separator("PERBANDINGAN PERFORMA")
    demonstrate_performance_comparison()
    
    print_separator("DEMO INTERAKTIF")
    interactive_data_structure_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN STRUKTUR DATA LINIER:")
    print("- List: Koleksi elemen dengan akses random O(1)")
    print("- Stack: LIFO - push/pop di satu ujung")
    print("- Queue: FIFO - enqueue di belakang, dequeue di depan")
    print("- Deque: Operasi efisien di kedua ujung")
    print("- Pilih struktur data sesuai kebutuhan operasi")
    print("="*60)

if __name__ == "__main__":
    main()
