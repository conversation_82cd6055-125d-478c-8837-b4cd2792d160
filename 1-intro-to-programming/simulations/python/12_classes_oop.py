"""
SIMULASI CLASSES DAN PEMROGRAMAN BERORIENTASI OBJEK (OOP)
=========================================================

Object-Oriented Programming (OOP) adalah paradigma pemrograman yang
mengorganisir kode dalam bentuk objek dan class. OOP memungkinkan
kita membuat kode yang lebih modular, reusable, dan mudah dipelihara.

Konsep utama OOP:
1. CLASS: Blueprint/template untuk membuat objek
2. OBJECT: Instance dari class
3. ENCAPSULATION: Menyembunyikan detail implementasi
4. ABSTRACTION: Menyederhanakan kompleksitas
5. ATTRIBUTES: Data/properti objek
6. METHODS: Fungsi yang dimiliki objek

OOP membantu memodelkan dunia nyata dalam kode program.
"""

import math
from datetime import datetime

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_basic_classes():
    """Demonstrasi class dan object dasar"""
    print("CLASS adalah template untuk membuat objek.")
    print("OBJECT adalah instance dari class.")
    
    print("\n1. DEFINISI CLASS SEDERHANA:")
    
    class Person:
        """Class untuk merepresentasikan seseorang"""
        
        def __init__(self, name, age):
            """Constructor - dipanggil saat objek dibuat"""
            self.name = name  # Instance attribute
            self.age = age
        
        def greet(self):
            """Method untuk menyapa"""
            return f"Hello, my name is {self.name}"
        
        def have_birthday(self):
            """Method untuk ulang tahun"""
            self.age += 1
            return f"{self.name} is now {self.age} years old"
        
        def __str__(self):
            """String representation of object"""
            return f"Person(name='{self.name}', age={self.age})"
    
    print("   class Person:")
    print("       def __init__(self, name, age):")
    print("           self.name = name")
    print("           self.age = age")
    print("       def greet(self):")
    print("           return f'Hello, my name is {self.name}'")
    
    print("\n2. MEMBUAT OBJEK:")
    person1 = Person("Alice", 25)
    person2 = Person("Bob", 30)
    
    print(f"   person1 = Person('Alice', 25) -> {person1}")
    print(f"   person2 = Person('Bob', 30) -> {person2}")
    
    print("\n3. MENGAKSES ATTRIBUTES:")
    print(f"   person1.name = '{person1.name}'")
    print(f"   person1.age = {person1.age}")
    print(f"   person2.name = '{person2.name}'")
    print(f"   person2.age = {person2.age}")
    
    print("\n4. MEMANGGIL METHODS:")
    print(f"   person1.greet() = '{person1.greet()}'")
    print(f"   person2.greet() = '{person2.greet()}'")
    
    # Modify object state
    birthday_msg = person1.have_birthday()
    print(f"   person1.have_birthday() = '{birthday_msg}'")
    print(f"   person1.age sekarang = {person1.age}")
    
    print("\n5. OBJECT IDENTITY:")
    print(f"   id(person1) = {id(person1)}")
    print(f"   id(person2) = {id(person2)}")
    print(f"   person1 is person2 = {person1 is person2}")
    print(f"   type(person1) = {type(person1)}")

def demonstrate_attributes_methods():
    """Demonstrasi berbagai jenis attributes dan methods"""
    print("ATTRIBUTES dan METHODS dapat memiliki berbagai jenis dan tingkat akses.")
    
    print("\n1. INSTANCE vs CLASS ATTRIBUTES:")
    
    class Student:
        # Class attribute (shared by all instances)
        school_name = "Python University"
        total_students = 0
        
        def __init__(self, name, student_id):
            # Instance attributes (unique to each instance)
            self.name = name
            self.student_id = student_id
            self.grades = []
            
            # Increment class attribute
            Student.total_students += 1
        
        def add_grade(self, grade):
            """Instance method"""
            self.grades.append(grade)
        
        def get_average(self):
            """Instance method"""
            if not self.grades:
                return 0
            return sum(self.grades) / len(self.grades)
        
        @classmethod
        def get_total_students(cls):
            """Class method - akses class attributes"""
            return cls.total_students
        
        @staticmethod
        def is_passing_grade(grade):
            """Static method - tidak akses instance atau class"""
            return grade >= 60
        
        def __str__(self):
            avg = self.get_average()
            return f"Student({self.name}, ID: {self.student_id}, Avg: {avg:.1f})"
    
    print("   class Student:")
    print("       school_name = 'Python University'  # Class attribute")
    print("       def __init__(self, name, student_id):")
    print("           self.name = name  # Instance attribute")
    
    print("\n2. MEMBUAT STUDENTS:")
    student1 = Student("Alice", "S001")
    student2 = Student("Bob", "S002")
    student3 = Student("Charlie", "S003")
    
    print(f"   student1 = Student('Alice', 'S001')")
    print(f"   student2 = Student('Bob', 'S002')")
    print(f"   student3 = Student('Charlie', 'S003')")
    
    print("\n3. CLASS ATTRIBUTES:")
    print(f"   Student.school_name = '{Student.school_name}'")
    print(f"   Student.total_students = {Student.total_students}")
    print(f"   student1.school_name = '{student1.school_name}' (akses via instance)")
    
    print("\n4. INSTANCE METHODS:")
    student1.add_grade(85)
    student1.add_grade(92)
    student1.add_grade(78)
    
    student2.add_grade(76)
    student2.add_grade(88)
    
    print(f"   student1.add_grade(85, 92, 78)")
    print(f"   student1.get_average() = {student1.get_average():.1f}")
    print(f"   student2.add_grade(76, 88)")
    print(f"   student2.get_average() = {student2.get_average():.1f}")
    
    print("\n5. CLASS METHOD:")
    total = Student.get_total_students()
    print(f"   Student.get_total_students() = {total}")
    
    print("\n6. STATIC METHOD:")
    print(f"   Student.is_passing_grade(85) = {Student.is_passing_grade(85)}")
    print(f"   Student.is_passing_grade(45) = {Student.is_passing_grade(45)}")
    
    print(f"\n7. OBJECT REPRESENTATION:")
    print(f"   student1 = {student1}")
    print(f"   student2 = {student2}")

def demonstrate_encapsulation():
    """Demonstrasi encapsulation dan access control"""
    print("ENCAPSULATION menyembunyikan detail implementasi dan melindungi data.")
    
    print("\n1. PRIVATE ATTRIBUTES (CONVENTION):")
    
    class BankAccount:
        def __init__(self, account_number, initial_balance=0):
            self.account_number = account_number  # Public
            self._balance = initial_balance       # Protected (convention)
            self.__pin = "1234"                  # Private (name mangling)
            self.__transaction_history = []      # Private
        
        def deposit(self, amount):
            """Public method untuk deposit"""
            if amount > 0:
                self._balance += amount
                self.__add_transaction(f"Deposit: +${amount}")
                return f"Deposited ${amount}. New balance: ${self._balance}"
            return "Invalid deposit amount"
        
        def withdraw(self, amount, pin):
            """Public method untuk withdraw"""
            if not self.__verify_pin(pin):
                return "Invalid PIN"
            
            if amount > 0 and amount <= self._balance:
                self._balance -= amount
                self.__add_transaction(f"Withdrawal: -${amount}")
                return f"Withdrew ${amount}. New balance: ${self._balance}"
            return "Invalid withdrawal amount or insufficient funds"
        
        def get_balance(self, pin):
            """Public method untuk cek saldo"""
            if self.__verify_pin(pin):
                return f"Current balance: ${self._balance}"
            return "Invalid PIN"
        
        def __verify_pin(self, pin):
            """Private method untuk verifikasi PIN"""
            return pin == self.__pin
        
        def __add_transaction(self, transaction):
            """Private method untuk menambah transaksi"""
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.__transaction_history.append(f"{timestamp}: {transaction}")
        
        def get_transaction_history(self, pin):
            """Public method untuk melihat history"""
            if self.__verify_pin(pin):
                return self.__transaction_history.copy()
            return "Invalid PIN"
        
        # Property untuk akses controlled
        @property
        def account_info(self):
            """Read-only property"""
            return f"Account: {self.account_number}"
    
    print("   class BankAccount:")
    print("       def __init__(self, account_number, initial_balance=0):")
    print("           self.account_number = account_number  # Public")
    print("           self._balance = initial_balance       # Protected")
    print("           self.__pin = '1234'                  # Private")
    
    print("\n2. MENGGUNAKAN BANK ACCOUNT:")
    account = BankAccount("ACC001", 1000)
    
    print(f"   account = BankAccount('ACC001', 1000)")
    print(f"   account.account_info = '{account.account_info}'")
    
    # Public interface
    print(f"   account.deposit(500) = '{account.deposit(500)}'")
    print(f"   account.withdraw(200, '1234') = '{account.withdraw(200, '1234')}'")
    print(f"   account.withdraw(200, 'wrong') = '{account.withdraw(200, 'wrong')}'")
    print(f"   account.get_balance('1234') = '{account.get_balance('1234')}'")
    
    print("\n3. AKSES ATTRIBUTES:")
    print(f"   account.account_number = '{account.account_number}' (public)")
    print(f"   account._balance = {account._balance} (protected - bisa diakses tapi tidak disarankan)")
    
    # Private attributes (name mangling)
    try:
        print(f"   account.__pin = {account.__pin}")
    except AttributeError:
        print("   account.__pin = AttributeError (private - tidak bisa diakses)")
    
    # Actual private attribute name (with name mangling)
    print(f"   account._BankAccount__pin = '{account._BankAccount__pin}' (name mangling)")
    
    print("\n4. TRANSACTION HISTORY:")
    history = account.get_transaction_history("1234")
    print("   Transaction history:")
    for transaction in history:
        print(f"     {transaction}")

def demonstrate_special_methods():
    """Demonstrasi special methods (dunder methods)"""
    print("SPECIAL METHODS memungkinkan objek berinteraksi dengan built-in functions")
    print("dan operators Python.")
    
    print("\n1. ARITHMETIC OPERATIONS:")
    
    class Vector:
        def __init__(self, x, y):
            self.x = x
            self.y = y
        
        def __str__(self):
            """String representation untuk print()"""
            return f"Vector({self.x}, {self.y})"
        
        def __repr__(self):
            """Developer representation"""
            return f"Vector(x={self.x}, y={self.y})"
        
        def __add__(self, other):
            """Addition: v1 + v2"""
            if isinstance(other, Vector):
                return Vector(self.x + other.x, self.y + other.y)
            return NotImplemented
        
        def __sub__(self, other):
            """Subtraction: v1 - v2"""
            if isinstance(other, Vector):
                return Vector(self.x - other.x, self.y - other.y)
            return NotImplemented
        
        def __mul__(self, scalar):
            """Scalar multiplication: v * scalar"""
            if isinstance(scalar, (int, float)):
                return Vector(self.x * scalar, self.y * scalar)
            return NotImplemented
        
        def __eq__(self, other):
            """Equality: v1 == v2"""
            if isinstance(other, Vector):
                return self.x == other.x and self.y == other.y
            return False
        
        def __len__(self):
            """Length: len(v) - magnitude of vector"""
            return int(math.sqrt(self.x**2 + self.y**2))
        
        def __getitem__(self, index):
            """Indexing: v[0] = x, v[1] = y"""
            if index == 0:
                return self.x
            elif index == 1:
                return self.y
            else:
                raise IndexError("Vector index out of range")
        
        def __setitem__(self, index, value):
            """Assignment: v[0] = value"""
            if index == 0:
                self.x = value
            elif index == 1:
                self.y = value
            else:
                raise IndexError("Vector index out of range")
    
    print("   class Vector:")
    print("       def __add__(self, other): ...")
    print("       def __str__(self): ...")
    print("       def __len__(self): ...")
    
    print("\n2. MENGGUNAKAN VECTOR:")
    v1 = Vector(3, 4)
    v2 = Vector(1, 2)
    
    print(f"   v1 = Vector(3, 4) -> {v1}")
    print(f"   v2 = Vector(1, 2) -> {v2}")
    
    # Arithmetic operations
    v3 = v1 + v2
    print(f"   v1 + v2 = {v3}")
    
    v4 = v1 - v2
    print(f"   v1 - v2 = {v4}")
    
    v5 = v1 * 2
    print(f"   v1 * 2 = {v5}")
    
    # Comparison
    print(f"   v1 == v2 = {v1 == v2}")
    print(f"   v1 == Vector(3, 4) = {v1 == Vector(3, 4)}")
    
    # Built-in functions
    print(f"   len(v1) = {len(v1)} (magnitude)")
    
    # Indexing
    print(f"   v1[0] = {v1[0]} (x component)")
    print(f"   v1[1] = {v1[1]} (y component)")
    
    v1[0] = 5
    print(f"   v1[0] = 5 -> v1 = {v1}")

def demonstrate_composition():
    """Demonstrasi composition - objek yang mengandung objek lain"""
    print("COMPOSITION adalah hubungan 'has-a' antara objek.")
    print("Objek dapat mengandung objek lain sebagai bagian dari dirinya.")
    
    print("\n1. COMPOSITION EXAMPLE:")
    
    class Address:
        def __init__(self, street, city, postal_code):
            self.street = street
            self.city = city
            self.postal_code = postal_code
        
        def __str__(self):
            return f"{self.street}, {self.city} {self.postal_code}"
    
    class Employee:
        def __init__(self, name, employee_id, salary):
            self.name = name
            self.employee_id = employee_id
            self.salary = salary
            self.address = None  # Will be set later
            self.skills = []     # List of skills
        
        def set_address(self, address):
            """Set employee address"""
            self.address = address
        
        def add_skill(self, skill):
            """Add a skill"""
            if skill not in self.skills:
                self.skills.append(skill)
        
        def get_info(self):
            """Get employee information"""
            info = f"Employee: {self.name} (ID: {self.employee_id})\n"
            info += f"Salary: ${self.salary}\n"
            if self.address:
                info += f"Address: {self.address}\n"
            if self.skills:
                info += f"Skills: {', '.join(self.skills)}"
            return info
        
        def __str__(self):
            return f"Employee({self.name}, {self.employee_id})"
    
    class Company:
        def __init__(self, name):
            self.name = name
            self.employees = []  # List of Employee objects
            self.departments = {}  # Dict of department -> employees
        
        def hire_employee(self, employee, department="General"):
            """Hire a new employee"""
            self.employees.append(employee)
            
            if department not in self.departments:
                self.departments[department] = []
            self.departments[department].append(employee)
        
        def get_employee_count(self):
            """Get total number of employees"""
            return len(self.employees)
        
        def get_department_info(self):
            """Get information about departments"""
            info = f"Company: {self.name}\n"
            info += f"Total Employees: {self.get_employee_count()}\n"
            info += "Departments:\n"
            
            for dept, emps in self.departments.items():
                info += f"  {dept}: {len(emps)} employees\n"
                for emp in emps:
                    info += f"    - {emp.name}\n"
            
            return info
    
    print("   class Address: ...")
    print("   class Employee:")
    print("       def __init__(self, name, employee_id, salary):")
    print("           self.address = None  # Composition")
    print("   class Company:")
    print("       def __init__(self, name):")
    print("           self.employees = []  # Composition")
    
    print("\n2. MEMBUAT OBJEK DENGAN COMPOSITION:")
    
    # Create company
    company = Company("Tech Solutions Inc.")
    
    # Create employees
    emp1 = Employee("Alice Johnson", "E001", 75000)
    emp2 = Employee("Bob Smith", "E002", 68000)
    emp3 = Employee("Charlie Brown", "E003", 82000)
    
    # Create addresses
    addr1 = Address("123 Main St", "Jakarta", "12345")
    addr2 = Address("456 Oak Ave", "Bandung", "67890")
    
    # Set addresses
    emp1.set_address(addr1)
    emp2.set_address(addr2)
    
    # Add skills
    emp1.add_skill("Python")
    emp1.add_skill("Django")
    emp1.add_skill("PostgreSQL")
    
    emp2.add_skill("JavaScript")
    emp2.add_skill("React")
    emp2.add_skill("Node.js")
    
    emp3.add_skill("Java")
    emp3.add_skill("Spring")
    emp3.add_skill("MySQL")
    
    # Hire employees
    company.hire_employee(emp1, "Development")
    company.hire_employee(emp2, "Development")
    company.hire_employee(emp3, "Development")
    
    print("   Created company, employees, and addresses")
    print("   Set up composition relationships")
    
    print("\n3. MENGGUNAKAN COMPOSED OBJECTS:")
    print(f"   Company info:")
    print(company.get_department_info())
    
    print("   Employee details:")
    for emp in company.employees:
        print(f"   {emp.get_info()}")
        print()

def interactive_oop_demo():
    """Demo interaktif OOP concepts"""
    print("\nDEMO INTERAKTIF OOP")
    print("-" * 25)
    
    # Simple library system
    class Book:
        def __init__(self, title, author, isbn):
            self.title = title
            self.author = author
            self.isbn = isbn
            self.is_borrowed = False
            self.borrower = None
        
        def borrow(self, borrower_name):
            if not self.is_borrowed:
                self.is_borrowed = True
                self.borrower = borrower_name
                return f"'{self.title}' borrowed by {borrower_name}"
            return f"'{self.title}' is already borrowed"
        
        def return_book(self):
            if self.is_borrowed:
                borrower = self.borrower
                self.is_borrowed = False
                self.borrower = None
                return f"'{self.title}' returned by {borrower}"
            return f"'{self.title}' was not borrowed"
        
        def __str__(self):
            status = f"(Borrowed by {self.borrower})" if self.is_borrowed else "(Available)"
            return f"'{self.title}' by {self.author} {status}"
    
    class Library:
        def __init__(self, name):
            self.name = name
            self.books = []
        
        def add_book(self, book):
            self.books.append(book)
        
        def list_books(self):
            if not self.books:
                return "No books in library"
            return "\n".join(f"{i+1}. {book}" for i, book in enumerate(self.books))
        
        def find_book(self, title):
            for book in self.books:
                if title.lower() in book.title.lower():
                    return book
            return None
    
    # Initialize library
    library = Library("Python Learning Library")
    
    # Add some books
    books_data = [
        ("Python Crash Course", "Eric Matthes", "978-1593276034"),
        ("Automate the Boring Stuff", "Al Sweigart", "978-1593275990"),
        ("Clean Code", "Robert Martin", "978-0132350884"),
        ("Design Patterns", "Gang of Four", "978-0201633612")
    ]
    
    for title, author, isbn in books_data:
        library.add_book(Book(title, author, isbn))
    
    print(f"Welcome to {library.name}!")
    
    while True:
        print(f"\nLibrary System:")
        print("1. List all books")
        print("2. Borrow a book")
        print("3. Return a book")
        print("4. Add a book")
        print("5. Exit")
        
        choice = input("Choose option (1-5): ").strip()
        
        if choice == "1":
            print("\nBooks in library:")
            print(library.list_books())
        
        elif choice == "2":
            print("\nAvailable books:")
            available_books = [book for book in library.books if not book.is_borrowed]
            if not available_books:
                print("No books available")
                continue
            
            for i, book in enumerate(available_books):
                print(f"{i+1}. {book}")
            
            try:
                book_num = int(input("Select book number: ")) - 1
                if 0 <= book_num < len(available_books):
                    borrower = input("Borrower name: ").strip()
                    if borrower:
                        result = available_books[book_num].borrow(borrower)
                        print(result)
                    else:
                        print("Please enter borrower name")
                else:
                    print("Invalid book number")
            except ValueError:
                print("Please enter a valid number")
        
        elif choice == "3":
            print("\nBorrowed books:")
            borrowed_books = [book for book in library.books if book.is_borrowed]
            if not borrowed_books:
                print("No books are currently borrowed")
                continue
            
            for i, book in enumerate(borrowed_books):
                print(f"{i+1}. {book}")
            
            try:
                book_num = int(input("Select book number to return: ")) - 1
                if 0 <= book_num < len(borrowed_books):
                    result = borrowed_books[book_num].return_book()
                    print(result)
                else:
                    print("Invalid book number")
            except ValueError:
                print("Please enter a valid number")
        
        elif choice == "4":
            title = input("Book title: ").strip()
            author = input("Author: ").strip()
            isbn = input("ISBN: ").strip()
            
            if title and author:
                new_book = Book(title, author, isbn)
                library.add_book(new_book)
                print(f"Added '{title}' to library")
            else:
                print("Title and author are required")
        
        elif choice == "5":
            print("Thank you for using the library system!")
            break
        
        else:
            print("Invalid choice")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI CLASSES DAN OOP PYTHON")
    print("================================")
    print("Program ini mendemonstrasikan konsep Object-Oriented Programming")
    print("dalam Python: classes, objects, encapsulation, dan composition.")
    
    print_separator("BASIC CLASSES DAN OBJECTS")
    demonstrate_basic_classes()
    
    print_separator("ATTRIBUTES DAN METHODS")
    demonstrate_attributes_methods()
    
    print_separator("ENCAPSULATION")
    demonstrate_encapsulation()
    
    print_separator("SPECIAL METHODS")
    demonstrate_special_methods()
    
    print_separator("COMPOSITION")
    demonstrate_composition()
    
    print_separator("DEMO INTERAKTIF")
    interactive_oop_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN OOP:")
    print("- Class: Template untuk membuat objek")
    print("- Object: Instance dari class dengan state dan behavior")
    print("- Encapsulation: Menyembunyikan detail implementasi")
    print("- Methods: Instance, class, static methods")
    print("- Special methods: Integrasi dengan Python built-ins")
    print("- Composition: Objek mengandung objek lain")
    print("="*60)

if __name__ == "__main__":
    main()
