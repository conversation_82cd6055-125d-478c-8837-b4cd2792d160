"""
SIMULASI STRUKTUR DATA BERBASIS HASH
====================================

Struktur data berbasis hash menggunakan hash function untuk memetakan
key ke index dalam tabel. Ini memungkinkan akses data yang sangat cepat
dengan kompleksitas waktu rata-rata O(1).

Jenis struktur data berbasis hash:
1. SET: Koleksi elemen unik tanpa duplikasi
2. DICTIONARY: Koleksi pasangan key-value
3. HASH TABLE: Implementasi dasar struktur hash
4. COUNTER: Menghitung frekuensi elemen
5. DEFAULT DICT: Dictionary dengan nilai default

Hash-based structures sangat efisien untuk pencarian, penambahan,
dan penghapusan data.
"""

from collections import Counter, defaultdict, OrderedDict
import hashlib
import time

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_sets():
    """Demonstrasi Set operations"""
    print("SET adalah koleksi elemen unik tanpa duplikasi.")
    print("Set menggunakan hash table untuk penyimpanan yang efisien.")

    print("\n1. PEMBUATAN SET:")
    # Berbagai cara membuat set
    empty_set = set()
    numbers_set = {1, 2, 3, 4, 5}
    from_list = set([1, 2, 2, 3, 3, 4])  # Duplikasi dihilangkan
    from_string = set("hello")  # Set dari karakter

    print(f"   empty_set = set() -> {empty_set}")
    print(f"   numbers_set = {{1, 2, 3, 4, 5}} -> {numbers_set}")
    print(f"   set([1, 2, 2, 3, 3, 4]) -> {from_list} (duplikasi dihilangkan)")
    print(f"   set('hello') -> {from_string}")

    # Set comprehension
    squares_set = {x**2 for x in range(5)}
    even_set = {x for x in range(10) if x % 2 == 0}

    print(f"   {{x**2 for x in range(5)}} -> {squares_set}")
    print(f"   {{x for x in range(10) if x % 2 == 0}} -> {even_set}")

    print("\n2. OPERASI SET DASAR:")
    fruits = {"apple", "banana", "orange"}
    print(f"   fruits = {fruits}")

    # Add
    fruits.add("grape")
    print(f"   fruits.add('grape') -> {fruits}")

    # Update (add multiple)
    fruits.update(["kiwi", "mango"])
    print(f"   fruits.update(['kiwi', 'mango']) -> {fruits}")

    # Remove
    fruits.remove("banana")
    print(f"   fruits.remove('banana') -> {fruits}")

    # Discard (tidak error jika tidak ada)
    fruits.discard("watermelon")  # Tidak ada error
    print(f"   fruits.discard('watermelon') -> {fruits} (tidak error)")

    # Pop (random element)
    popped = fruits.pop()
    print(f"   fruits.pop() -> removed '{popped}', remaining: {fruits}")

    print("\n3. MEMBERSHIP TESTING:")
    test_set = {1, 2, 3, 4, 5}
    print(f"   test_set = {test_set}")
    print(f"   3 in test_set -> {3 in test_set}")
    print(f"   6 in test_set -> {6 in test_set}")
    print(f"   len(test_set) -> {len(test_set)}")

    print("\n4. SET OPERATIONS (MATEMATIKA):")
    set_a = {1, 2, 3, 4, 5}
    set_b = {4, 5, 6, 7, 8}

    print(f"   set_a = {set_a}")
    print(f"   set_b = {set_b}")

    # Union (gabungan)
    union = set_a | set_b
    print(f"   set_a | set_b (union) -> {union}")

    # Intersection (irisan)
    intersection = set_a & set_b
    print(f"   set_a & set_b (intersection) -> {intersection}")

    # Difference (selisih)
    difference = set_a - set_b
    print(f"   set_a - set_b (difference) -> {difference}")

    # Symmetric difference (selisih simetris)
    sym_diff = set_a ^ set_b
    print(f"   set_a ^ set_b (symmetric difference) -> {sym_diff}")

    # Subset dan superset
    subset = {1, 2, 3}
    print(f"   subset = {subset}")
    print(f"   subset.issubset(set_a) -> {subset.issubset(set_a)}")
    print(f"   set_a.issuperset(subset) -> {set_a.issuperset(subset)}")

    print("\n5. CONTOH PRAKTIS SET:")

    def find_unique_words(text):
        """Mencari kata-kata unik dalam teks"""
        words = text.lower().split()
        # Hapus tanda baca sederhana
        clean_words = [word.strip('.,!?;:') for word in words]
        return set(clean_words)

    text = "Python is great. Python is powerful. Programming with Python is fun."
    unique_words = find_unique_words(text)
    print(f"   Text: '{text}'")
    print(f"   Unique words: {unique_words}")
    print(f"   Total unique words: {len(unique_words)}")

    def find_common_interests(person1_interests, person2_interests):
        """Mencari minat yang sama antara dua orang"""
        set1 = set(person1_interests)
        set2 = set(person2_interests)

        common = set1 & set2
        only_person1 = set1 - set2
        only_person2 = set2 - set1

        return {
            'common': common,
            'only_person1': only_person1,
            'only_person2': only_person2
        }

    alice_interests = ["reading", "swimming", "coding", "music"]
    bob_interests = ["swimming", "gaming", "music", "cooking"]

    result = find_common_interests(alice_interests, bob_interests)
    print(f"\n   Alice interests: {alice_interests}")
    print(f"   Bob interests: {bob_interests}")
    print(f"   Common interests: {result['common']}")
    print(f"   Only Alice: {result['only_person1']}")
    print(f"   Only Bob: {result['only_person2']}")

def demonstrate_dictionaries():
    """Demonstrasi Dictionary operations"""
    print("DICTIONARY adalah koleksi pasangan key-value.")
    print("Menggunakan hash table untuk akses cepat berdasarkan key.")

    print("\n1. PEMBUATAN DICTIONARY:")
    # Berbagai cara membuat dictionary
    empty_dict = {}
    person = {"name": "Alice", "age": 25, "city": "Jakarta"}
    from_pairs = dict([("a", 1), ("b", 2), ("c", 3)])
    from_kwargs = dict(x=1, y=2, z=3)

    print(f"   empty_dict = {{}} -> {empty_dict}")
    print(f"   person = {person}")
    print(f"   dict([('a', 1), ('b', 2), ('c', 3)]) -> {from_pairs}")
    print(f"   dict(x=1, y=2, z=3) -> {from_kwargs}")

    # Dictionary comprehension
    squares_dict = {x: x**2 for x in range(5)}
    filtered_dict = {k: v for k, v in person.items() if isinstance(v, str)}

    print(f"   {{x: x**2 for x in range(5)}} -> {squares_dict}")
    print(f"   String values only: {filtered_dict}")

    print("\n2. AKSES DAN MODIFIKASI:")
    student = {"name": "Bob", "grade": "A", "score": 85}
    print(f"   student = {student}")

    # Akses nilai
    print(f"   student['name'] -> '{student['name']}'")
    print(f"   student.get('age', 'Unknown') -> '{student.get('age', 'Unknown')}'")

    # Modifikasi
    student["age"] = 20
    print(f"   student['age'] = 20 -> {student}")

    student.update({"semester": 3, "major": "CS"})
    print(f"   student.update(...) -> {student}")

    # Hapus
    removed_score = student.pop("score")
    print(f"   student.pop('score') -> removed {removed_score}, remaining: {student}")

    del student["grade"]
    print(f"   del student['grade'] -> {student}")

    print("\n3. DICTIONARY METHODS:")
    data = {"a": 1, "b": 2, "c": 3, "d": 4}
    print(f"   data = {data}")

    print(f"   data.keys() -> {list(data.keys())}")
    print(f"   data.values() -> {list(data.values())}")
    print(f"   data.items() -> {list(data.items())}")

    # Iterasi
    print("   Iterating over items:")
    for key, value in data.items():
        print(f"     {key}: {value}")

    print("\n4. NESTED DICTIONARIES:")
    company = {
        "name": "Tech Corp",
        "employees": {
            "alice": {"position": "Developer", "salary": 75000},
            "bob": {"position": "Designer", "salary": 65000},
            "charlie": {"position": "Manager", "salary": 85000}
        },
        "departments": ["IT", "HR", "Finance"]
    }

    print(f"   company = {company}")
    print(f"   Alice's position: {company['employees']['alice']['position']}")
    print(f"   All employees:")
    for name, info in company["employees"].items():
        print(f"     {name}: {info['position']} - ${info['salary']}")

    print("\n5. CONTOH PRAKTIS DICTIONARY:")

    def count_characters(text):
        """Menghitung frekuensi karakter"""
        char_count = {}
        for char in text.lower():
            if char.isalpha():  # Hanya huruf
                char_count[char] = char_count.get(char, 0) + 1
        return char_count

    text = "Hello World"
    char_freq = count_characters(text)
    print(f"   Text: '{text}'")
    print(f"   Character frequency: {char_freq}")

    def group_by_length(words):
        """Mengelompokkan kata berdasarkan panjang"""
        groups = {}
        for word in words:
            length = len(word)
            if length not in groups:
                groups[length] = []
            groups[length].append(word)
        return groups

    words = ["cat", "dog", "elephant", "bird", "butterfly", "ant"]
    grouped = group_by_length(words)
    print(f"   Words: {words}")
    print(f"   Grouped by length: {grouped}")

def demonstrate_advanced_hash_structures():
    """Demonstrasi struktur hash lanjutan"""
    print("STRUKTUR HASH LANJUTAN untuk kasus penggunaan khusus.")

    print("\n1. COUNTER - Menghitung Frekuensi:")
    from collections import Counter

    # Counter dari list
    numbers = [1, 2, 3, 2, 1, 3, 1, 4, 5, 4]
    counter = Counter(numbers)
    print(f"   numbers = {numbers}")
    print(f"   Counter(numbers) = {counter}")

    # Counter dari string
    text = "hello world"
    char_counter = Counter(text)
    print(f"   Counter('{text}') = {char_counter}")

    # Most common
    print(f"   Most common 3: {counter.most_common(3)}")

    # Counter operations
    counter1 = Counter("aabbcc")
    counter2 = Counter("abccdd")
    print(f"   counter1 = {counter1}")
    print(f"   counter2 = {counter2}")
    print(f"   counter1 + counter2 = {counter1 + counter2}")
    print(f"   counter1 - counter2 = {counter1 - counter2}")
    print(f"   counter1 & counter2 = {counter1 & counter2}")  # Intersection
    print(f"   counter1 | counter2 = {counter1 | counter2}")  # Union

    print("\n2. DEFAULTDICT - Dictionary dengan Nilai Default:")
    from collections import defaultdict

    # defaultdict dengan list
    dd_list = defaultdict(list)
    words = ["apple", "banana", "apricot", "blueberry", "avocado"]

    for word in words:
        first_letter = word[0]
        dd_list[first_letter].append(word)

    print(f"   words = {words}")
    print(f"   Grouped by first letter: {dict(dd_list)}")

    # defaultdict dengan int (untuk counting)
    dd_int = defaultdict(int)
    text = "hello world"
    for char in text:
        dd_int[char] += 1

    print(f"   Character count using defaultdict: {dict(dd_int)}")

    # defaultdict dengan set
    dd_set = defaultdict(set)
    relationships = [
        ("Alice", "Bob"),
        ("Alice", "Charlie"),
        ("Bob", "David"),
        ("Charlie", "Alice"),
        ("David", "Bob")
    ]

    for person1, person2 in relationships:
        dd_set[person1].add(person2)
        dd_set[person2].add(person1)

    print(f"   Relationships: {relationships}")
    print(f"   Friend networks: {dict(dd_set)}")

    print("\n3. ORDEREDDICT - Dictionary yang Mempertahankan Urutan:")
    # Note: Python 3.7+ dict sudah ordered, tapi OrderedDict masih berguna
    from collections import OrderedDict

    od = OrderedDict()
    od["first"] = 1
    od["second"] = 2
    od["third"] = 3

    print(f"   OrderedDict: {od}")

    # Move to end
    od.move_to_end("first")
    print(f"   After move_to_end('first'): {od}")

    # Pop last item
    last_item = od.popitem(last=True)
    print(f"   Popped last item: {last_item}, remaining: {od}")

def demonstrate_hash_implementation():
    """Demonstrasi implementasi hash table sederhana"""
    print("IMPLEMENTASI HASH TABLE SEDERHANA untuk memahami cara kerja internal.")

    class SimpleHashTable:
        def __init__(self, size=10):
            self.size = size
            self.table = [[] for _ in range(size)]  # Chaining untuk collision

        def _hash(self, key):
            """Hash function sederhana"""
            if isinstance(key, str):
                return sum(ord(char) for char in key) % self.size
            elif isinstance(key, int):
                return key % self.size
            else:
                return hash(key) % self.size

        def put(self, key, value):
            """Menambah atau update key-value pair"""
            index = self._hash(key)
            bucket = self.table[index]

            # Cari apakah key sudah ada
            for i, (k, v) in enumerate(bucket):
                if k == key:
                    bucket[i] = (key, value)  # Update
                    return

            # Key belum ada, tambah baru
            bucket.append((key, value))

        def get(self, key):
            """Mengambil value berdasarkan key"""
            index = self._hash(key)
            bucket = self.table[index]

            for k, v in bucket:
                if k == key:
                    return v

            raise KeyError(f"Key '{key}' not found")

        def delete(self, key):
            """Menghapus key-value pair"""
            index = self._hash(key)
            bucket = self.table[index]

            for i, (k, v) in enumerate(bucket):
                if k == key:
                    del bucket[i]
                    return v

            raise KeyError(f"Key '{key}' not found")

        def display(self):
            """Menampilkan isi hash table"""
            for i, bucket in enumerate(self.table):
                if bucket:
                    print(f"     Bucket {i}: {bucket}")

    print("\n   class SimpleHashTable: ...")

    ht = SimpleHashTable(5)
    print("   ht = SimpleHashTable(5)")

    # Add items
    items = [("apple", 5), ("banana", 3), ("orange", 8), ("grape", 12), ("kiwi", 7)]
    print("   Adding items:")
    for key, value in items:
        ht.put(key, value)
        print(f"     ht.put('{key}', {value})")

    print("   Hash table contents:")
    ht.display()

    # Get items
    print("   Getting items:")
    for key, _ in items[:3]:
        try:
            value = ht.get(key)
            print(f"     ht.get('{key}') = {value}")
        except KeyError as e:
            print(f"     ht.get('{key}') = {e}")

    # Delete item
    print("   Deleting 'banana':")
    try:
        deleted_value = ht.delete("banana")
        print(f"     Deleted value: {deleted_value}")
        ht.display()
    except KeyError as e:
        print(f"     Error: {e}")

def demonstrate_performance_comparison():
    """Demonstrasi perbandingan performa hash structures"""
    print("PERBANDINGAN PERFORMA operasi pada hash-based structures.")

    import time
    import random

    def time_operation(operation, description):
        """Mengukur waktu eksekusi operasi"""
        start_time = time.time()
        operation()
        end_time = time.time()
        print(f"   {description}: {(end_time - start_time)*1000:.2f} ms")

    # Prepare data
    n = 10000
    keys = [f"key_{i}" for i in range(n)]
    values = list(range(n))
    search_keys = random.sample(keys, 1000)

    print(f"\n1. PERFORMANCE TEST dengan {n} items:")

    # Dictionary operations
    def dict_operations():
        d = {}
        # Insert
        for k, v in zip(keys, values):
            d[k] = v
        # Search
        for k in search_keys:
            _ = d[k]
        # Delete some items
        for k in search_keys[:100]:
            del d[k]

    # Set operations
    def set_operations():
        s = set()
        # Insert
        for k in keys:
            s.add(k)
        # Search
        for k in search_keys:
            _ = k in s
        # Delete some items
        for k in search_keys[:100]:
            s.discard(k)

    # List operations (for comparison)
    def list_operations():
        l = []
        # Insert
        for k in keys:
            l.append(k)
        # Search
        for k in search_keys:
            _ = k in l
        # Delete some items (expensive!)
        for k in search_keys[:10]:  # Only 10 items due to O(n) complexity
            if k in l:
                l.remove(k)

    time_operation(dict_operations, "Dictionary operations")
    time_operation(set_operations, "Set operations")
    time_operation(list_operations, "List operations (limited)")

    print("\n2. KOMPLEKSITAS WAKTU:")
    complexity_table = [
        ("Operation", "Dict/Set", "List"),
        ("Insert", "O(1) avg", "O(1) append"),
        ("Search", "O(1) avg", "O(n)"),
        ("Delete", "O(1) avg", "O(n)"),
        ("Memory", "Higher", "Lower")
    ]

    print("   " + "-" * 35)
    for row in complexity_table:
        print(f"   {row[0]:<10} | {row[1]:<10} | {row[2]:<10}")
    print("   " + "-" * 35)

    print("\n3. HASH COLLISION DEMO:")

    class CollisionDemo:
        def __init__(self, size=5):
            self.size = size
            self.table = [[] for _ in range(size)]

        def simple_hash(self, key):
            """Hash function yang mudah collision"""
            return len(str(key)) % self.size

        def add(self, key):
            index = self.simple_hash(key)
            self.table[index].append(key)
            print(f"     '{key}' -> hash({len(str(key))}) % {self.size} = {index}")

        def display(self):
            for i, bucket in enumerate(self.table):
                print(f"     Bucket {i}: {bucket}")

    print("   Demonstrasi hash collision:")
    demo = CollisionDemo()
    test_keys = ["a", "bb", "ccc", "dd", "e", "fff"]

    for key in test_keys:
        demo.add(key)

    print("   Final hash table:")
    demo.display()
    print("   Collision terjadi ketika multiple keys hash ke index yang sama")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI STRUKTUR DATA BERBASIS HASH PYTHON")
    print("===========================================")
    print("Program ini mendemonstrasikan Set, Dictionary, dan")
    print("struktur data hash lainnya dalam Python.")

    print_separator("SET OPERATIONS")
    demonstrate_sets()

    print_separator("DICTIONARY OPERATIONS")
    demonstrate_dictionaries()

    print_separator("ADVANCED HASH STRUCTURES")
    demonstrate_advanced_hash_structures()

    print_separator("HASH TABLE IMPLEMENTATION")
    demonstrate_hash_implementation()

    print_separator("PERFORMANCE COMPARISON")
    demonstrate_performance_comparison()

    print("\n" + "="*60)
    print("RINGKASAN HASH-BASED STRUCTURES:")
    print("- Set: Koleksi elemen unik, operasi matematika set")
    print("- Dict: Key-value pairs, akses cepat berdasarkan key")
    print("- Counter: Menghitung frekuensi elemen")
    print("- defaultdict: Dictionary dengan nilai default")
    print("- Hash table: O(1) average untuk insert/search/delete")
    print("- Trade-off: Memori lebih besar untuk kecepatan akses")
    print("="*60)

if __name__ == "__main__":
    main()