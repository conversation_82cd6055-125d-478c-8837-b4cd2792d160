"""
SIMULASI INHERITANCE (PEWARISAN)
===============================

Inheritance adalah salah satu pilar utama OOP yang memungkinkan
class baru (child/subclass) mewarisi properties dan methods dari
class yang sudah ada (parent/superclass).

Konsep utama Inheritance:
1. PARENT CLASS (Superclass): Class yang diwarisi
2. CHILD CLASS (Subclass): Class yang mewarisi
3. METHOD OVERRIDING: Mengubah implementasi method parent
4. SUPER(): Mengakses method parent dari child
5. MULTIPLE INHERITANCE: Mewarisi dari multiple parents
6. METHOD RESOLUTION ORDER (MRO): Urutan pencarian method

Inheritance memungkinkan code reuse dan hierarki yang logis.
"""

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_basic_inheritance():
    """Demonstrasi inheritance dasar"""
    print("INHERITANCE memungkinkan class baru mewarisi dari class yang ada.")
    
    print("\n1. PARENT CLASS:")
    
    class Animal:
        """Parent class untuk semua hewan"""
        
        def __init__(self, name, species):
            self.name = name
            self.species = species
            self.is_alive = True
        
        def eat(self):
            return f"{self.name} is eating"
        
        def sleep(self):
            return f"{self.name} is sleeping"
        
        def make_sound(self):
            return f"{self.name} makes a sound"
        
        def get_info(self):
            return f"{self.name} is a {self.species}"
        
        def __str__(self):
            return f"Animal(name='{self.name}', species='{self.species}')"
    
    print("   class Animal:")
    print("       def __init__(self, name, species):")
    print("           self.name = name")
    print("           self.species = species")
    print("       def eat(self): ...")
    print("       def make_sound(self): ...")
    
    print("\n2. CHILD CLASSES:")
    
    class Dog(Animal):
        """Child class yang mewarisi dari Animal"""
        
        def __init__(self, name, breed):
            # Panggil constructor parent
            super().__init__(name, "Canine")
            self.breed = breed
            self.tricks = []
        
        def make_sound(self):
            """Override method parent"""
            return f"{self.name} barks: Woof! Woof!"
        
        def fetch(self):
            """Method khusus untuk Dog"""
            return f"{self.name} fetches the ball"
        
        def learn_trick(self, trick):
            """Method khusus untuk Dog"""
            self.tricks.append(trick)
            return f"{self.name} learned {trick}"
        
        def get_info(self):
            """Override dan extend method parent"""
            base_info = super().get_info()
            return f"{base_info}, breed: {self.breed}"
    
    class Cat(Animal):
        """Child class lain yang mewarisi dari Animal"""
        
        def __init__(self, name, color):
            super().__init__(name, "Feline")
            self.color = color
            self.lives_remaining = 9
        
        def make_sound(self):
            """Override method parent"""
            return f"{self.name} meows: Meow!"
        
        def climb(self):
            """Method khusus untuk Cat"""
            return f"{self.name} climbs the tree"
        
        def use_life(self):
            """Method khusus untuk Cat"""
            if self.lives_remaining > 0:
                self.lives_remaining -= 1
                return f"{self.name} used a life. {self.lives_remaining} lives remaining"
            return f"{self.name} has no lives left!"
    
    print("   class Dog(Animal):")
    print("       def __init__(self, name, breed):")
    print("           super().__init__(name, 'Canine')")
    print("       def make_sound(self):  # Override")
    print("           return f'{self.name} barks: Woof!'")
    
    print("\n3. MENGGUNAKAN INHERITANCE:")
    
    # Create instances
    generic_animal = Animal("Unknown", "Unknown Species")
    dog = Dog("Buddy", "Golden Retriever")
    cat = Cat("Whiskers", "Orange")
    
    print(f"   generic_animal = Animal('Unknown', 'Unknown Species')")
    print(f"   dog = Dog('Buddy', 'Golden Retriever')")
    print(f"   cat = Cat('Whiskers', 'Orange')")
    
    print("\n4. INHERITED METHODS:")
    animals = [generic_animal, dog, cat]
    
    for animal in animals:
        print(f"   {animal.get_info()}")
        print(f"   {animal.eat()}")
        print(f"   {animal.make_sound()}")  # Polymorphism!
        print()
    
    print("5. CHILD-SPECIFIC METHODS:")
    print(f"   dog.fetch() = '{dog.fetch()}'")
    print(f"   dog.learn_trick('sit') = '{dog.learn_trick('sit')}'")
    print(f"   dog.learn_trick('roll over') = '{dog.learn_trick('roll over')}'")
    print(f"   dog.tricks = {dog.tricks}")
    
    print(f"   cat.climb() = '{cat.climb()}'")
    print(f"   cat.use_life() = '{cat.use_life()}'")
    print(f"   cat.lives_remaining = {cat.lives_remaining}")
    
    print("\n6. ISINSTANCE DAN ISSUBCLASS:")
    print(f"   isinstance(dog, Dog) = {isinstance(dog, Dog)}")
    print(f"   isinstance(dog, Animal) = {isinstance(dog, Animal)}")
    print(f"   isinstance(dog, Cat) = {isinstance(dog, Cat)}")
    print(f"   issubclass(Dog, Animal) = {issubclass(Dog, Animal)}")
    print(f"   issubclass(Animal, Dog) = {issubclass(Animal, Dog)}")

def demonstrate_method_overriding():
    """Demonstrasi method overriding dan super()"""
    print("METHOD OVERRIDING memungkinkan child class mengubah behavior parent.")
    
    print("\n1. VEHICLE HIERARCHY:")
    
    class Vehicle:
        """Base class untuk kendaraan"""
        
        def __init__(self, brand, model, year):
            self.brand = brand
            self.model = model
            self.year = year
            self.fuel_level = 100
        
        def start_engine(self):
            return f"{self.brand} {self.model} engine started"
        
        def stop_engine(self):
            return f"{self.brand} {self.model} engine stopped"
        
        def drive(self, distance):
            fuel_needed = distance * 0.1  # 0.1 fuel per km
            if self.fuel_level >= fuel_needed:
                self.fuel_level -= fuel_needed
                return f"Drove {distance}km. Fuel remaining: {self.fuel_level:.1f}%"
            return "Not enough fuel!"
        
        def refuel(self):
            self.fuel_level = 100
            return "Tank refueled to 100%"
        
        def get_info(self):
            return f"{self.year} {self.brand} {self.model}"
    
    class ElectricCar(Vehicle):
        """Electric car dengan battery instead of fuel"""
        
        def __init__(self, brand, model, year, battery_capacity):
            super().__init__(brand, model, year)
            self.battery_capacity = battery_capacity
            self.battery_level = 100  # Percentage
            # Override fuel_level untuk electric car
            self.fuel_level = self.battery_level
        
        def start_engine(self):
            """Override - electric cars don't have engines"""
            return f"{self.brand} {self.model} electric motor activated"
        
        def drive(self, distance):
            """Override dengan electric-specific logic"""
            energy_needed = distance * 0.2  # Electric cars use more energy per km
            if self.battery_level >= energy_needed:
                self.battery_level -= energy_needed
                self.fuel_level = self.battery_level  # Keep in sync
                return f"Drove {distance}km electrically. Battery: {self.battery_level:.1f}%"
            return "Battery too low!"
        
        def refuel(self):
            """Override - electric cars charge, not refuel"""
            self.battery_level = 100
            self.fuel_level = 100
            return "Battery charged to 100%"
        
        def charge(self, hours):
            """Electric-specific method"""
            charge_rate = 10  # 10% per hour
            charge_amount = min(hours * charge_rate, 100 - self.battery_level)
            self.battery_level += charge_amount
            self.fuel_level = self.battery_level
            return f"Charged for {hours}h. Battery: {self.battery_level:.1f}%"
    
    class Motorcycle(Vehicle):
        """Motorcycle dengan fuel efficiency yang berbeda"""
        
        def __init__(self, brand, model, year, engine_size):
            super().__init__(brand, model, year)
            self.engine_size = engine_size
        
        def start_engine(self):
            """Override dengan motorcycle-specific message"""
            base_message = super().start_engine()
            return f"{base_message} - {self.engine_size}cc engine roaring!"
        
        def drive(self, distance):
            """Override - motorcycles are more fuel efficient"""
            fuel_needed = distance * 0.05  # Half the fuel consumption
            if self.fuel_level >= fuel_needed:
                self.fuel_level -= fuel_needed
                return f"Rode {distance}km on motorcycle. Fuel: {self.fuel_level:.1f}%"
            return "Not enough fuel!"
        
        def wheelie(self):
            """Motorcycle-specific method"""
            return f"{self.brand} {self.model} does a wheelie!"
    
    print("   class Vehicle: ...")
    print("   class ElectricCar(Vehicle):")
    print("       def start_engine(self):  # Override")
    print("           return f'{self.brand} electric motor activated'")
    print("       def drive(self, distance):  # Override")
    print("           # Electric-specific logic")
    
    print("\n2. CREATING VEHICLES:")
    
    regular_car = Vehicle("Toyota", "Camry", 2020)
    electric_car = ElectricCar("Tesla", "Model 3", 2022, 75)
    motorcycle = Motorcycle("Honda", "CBR600", 2021, 600)
    
    vehicles = [regular_car, electric_car, motorcycle]
    
    print("   regular_car = Vehicle('Toyota', 'Camry', 2020)")
    print("   electric_car = ElectricCar('Tesla', 'Model 3', 2022, 75)")
    print("   motorcycle = Motorcycle('Honda', 'CBR600', 2021, 600)")
    
    print("\n3. POLYMORPHISM IN ACTION:")
    
    for vehicle in vehicles:
        print(f"   {vehicle.get_info()}:")
        print(f"     {vehicle.start_engine()}")
        print(f"     {vehicle.drive(50)}")
        print(f"     {vehicle.stop_engine()}")
        print()
    
    print("4. SPECIFIC METHODS:")
    print(f"   electric_car.charge(3) = '{electric_car.charge(3)}'")
    print(f"   motorcycle.wheelie() = '{motorcycle.wheelie()}'")
    
    print("\n5. REFUELING/CHARGING:")
    for vehicle in vehicles:
        print(f"   {vehicle.get_info()}: {vehicle.refuel()}")

def demonstrate_multiple_inheritance():
    """Demonstrasi multiple inheritance"""
    print("MULTIPLE INHERITANCE memungkinkan class mewarisi dari multiple parents.")
    
    print("\n1. MULTIPLE PARENT CLASSES:")
    
    class Flyable:
        """Mixin untuk objek yang bisa terbang"""
        
        def __init__(self):
            self.altitude = 0
            self.is_flying = False
        
        def take_off(self):
            if not self.is_flying:
                self.is_flying = True
                self.altitude = 100
                return f"Taking off! Altitude: {self.altitude}m"
            return "Already flying!"
        
        def land(self):
            if self.is_flying:
                self.is_flying = False
                self.altitude = 0
                return "Landing complete"
            return "Already on ground!"
        
        def fly_to_altitude(self, target_altitude):
            if self.is_flying:
                self.altitude = target_altitude
                return f"Flying at {self.altitude}m"
            return "Must take off first!"
    
    class Swimmable:
        """Mixin untuk objek yang bisa berenang"""
        
        def __init__(self):
            self.depth = 0
            self.is_swimming = False
        
        def dive(self):
            if not self.is_swimming:
                self.is_swimming = True
                self.depth = 5
                return f"Diving! Depth: {self.depth}m"
            return "Already swimming!"
        
        def surface(self):
            if self.is_swimming:
                self.is_swimming = False
                self.depth = 0
                return "Surfaced"
            return "Not swimming!"
        
        def swim_to_depth(self, target_depth):
            if self.is_swimming:
                self.depth = target_depth
                return f"Swimming at {self.depth}m depth"
            return "Must dive first!"
    
    class Animal:
        """Base animal class"""
        
        def __init__(self, name, species):
            self.name = name
            self.species = species
        
        def eat(self):
            return f"{self.name} is eating"
        
        def sleep(self):
            return f"{self.name} is sleeping"
    
    print("   class Flyable: ...")
    print("   class Swimmable: ...")
    print("   class Animal: ...")
    
    print("\n2. MULTIPLE INHERITANCE:")
    
    class Duck(Animal, Flyable, Swimmable):
        """Duck yang bisa terbang dan berenang"""
        
        def __init__(self, name):
            # Panggil semua parent constructors
            Animal.__init__(self, name, "Duck")
            Flyable.__init__(self)
            Swimmable.__init__(self)
            self.feathers_wet = False
        
        def quack(self):
            return f"{self.name} says: Quack!"
        
        def preen_feathers(self):
            self.feathers_wet = False
            return f"{self.name} preens feathers"
        
        def dive(self):
            """Override to add duck-specific behavior"""
            result = super().dive()
            self.feathers_wet = True
            return f"{result} - Feathers are now wet"
    
    class Penguin(Animal, Swimmable):
        """Penguin yang bisa berenang tapi tidak terbang"""
        
        def __init__(self, name):
            Animal.__init__(self, name, "Penguin")
            Swimmable.__init__(self)
            self.body_temperature = 37
        
        def slide_on_ice(self):
            return f"{self.name} slides on ice"
        
        def huddle_for_warmth(self):
            self.body_temperature += 2
            return f"{self.name} huddles for warmth. Temperature: {self.body_temperature}°C"
    
    class Eagle(Animal, Flyable):
        """Eagle yang bisa terbang tapi tidak berenang"""
        
        def __init__(self, name):
            Animal.__init__(self, name, "Eagle")
            Flyable.__init__(self)
            self.prey_spotted = False
        
        def hunt(self):
            if self.is_flying:
                self.prey_spotted = True
                return f"{self.name} spots prey from above"
            return f"{self.name} needs to be flying to hunt"
        
        def dive_attack(self):
            if self.prey_spotted and self.is_flying:
                return f"{self.name} dives to attack prey"
            return f"{self.name} has no target or not flying"
    
    print("   class Duck(Animal, Flyable, Swimmable): ...")
    print("   class Penguin(Animal, Swimmable): ...")
    print("   class Eagle(Animal, Flyable): ...")
    
    print("\n3. USING MULTIPLE INHERITANCE:")
    
    duck = Duck("Donald")
    penguin = Penguin("Pingu")
    eagle = Eagle("Aquila")
    
    print(f"   duck = Duck('Donald')")
    print(f"   penguin = Penguin('Pingu')")
    print(f"   eagle = Eagle('Aquila')")
    
    print("\n4. DUCK ABILITIES:")
    print(f"   {duck.quack()}")
    print(f"   {duck.take_off()}")
    print(f"   {duck.fly_to_altitude(200)}")
    print(f"   {duck.land()}")
    print(f"   {duck.dive()}")
    print(f"   {duck.swim_to_depth(3)}")
    print(f"   {duck.surface()}")
    print(f"   {duck.preen_feathers()}")
    
    print("\n5. PENGUIN ABILITIES:")
    print(f"   {penguin.slide_on_ice()}")
    print(f"   {penguin.dive()}")
    print(f"   {penguin.swim_to_depth(10)}")
    print(f"   {penguin.huddle_for_warmth()}")
    
    print("\n6. EAGLE ABILITIES:")
    print(f"   {eagle.take_off()}")
    print(f"   {eagle.hunt()}")
    print(f"   {eagle.dive_attack()}")
    
    print("\n7. METHOD RESOLUTION ORDER (MRO):")
    print(f"   Duck MRO: {[cls.__name__ for cls in Duck.__mro__]}")
    print(f"   Penguin MRO: {[cls.__name__ for cls in Penguin.__mro__]}")
    print(f"   Eagle MRO: {[cls.__name__ for cls in Eagle.__mro__]}")

def demonstrate_abstract_classes():
    """Demonstrasi abstract classes dan methods"""
    print("ABSTRACT CLASSES mendefinisikan interface yang harus diimplementasi")
    print("oleh subclasses.")
    
    print("\n1. ABSTRACT BASE CLASS:")
    
    from abc import ABC, abstractmethod
    
    class Shape(ABC):
        """Abstract base class untuk shapes"""
        
        def __init__(self, name):
            self.name = name
        
        @abstractmethod
        def area(self):
            """Abstract method - harus diimplementasi oleh subclass"""
            pass
        
        @abstractmethod
        def perimeter(self):
            """Abstract method - harus diimplementasi oleh subclass"""
            pass
        
        def describe(self):
            """Concrete method yang bisa digunakan semua subclass"""
            return f"This is a {self.name} with area {self.area():.2f} and perimeter {self.perimeter():.2f}"
    
    print("   from abc import ABC, abstractmethod")
    print("   class Shape(ABC):")
    print("       @abstractmethod")
    print("       def area(self): pass")
    print("       @abstractmethod")
    print("       def perimeter(self): pass")
    
    print("\n2. CONCRETE IMPLEMENTATIONS:")
    
    class Rectangle(Shape):
        """Concrete implementation of Shape"""
        
        def __init__(self, width, height):
            super().__init__("Rectangle")
            self.width = width
            self.height = height
        
        def area(self):
            """Implement abstract method"""
            return self.width * self.height
        
        def perimeter(self):
            """Implement abstract method"""
            return 2 * (self.width + self.height)
    
    class Circle(Shape):
        """Concrete implementation of Shape"""
        
        def __init__(self, radius):
            super().__init__("Circle")
            self.radius = radius
        
        def area(self):
            """Implement abstract method"""
            import math
            return math.pi * self.radius ** 2
        
        def perimeter(self):
            """Implement abstract method"""
            import math
            return 2 * math.pi * self.radius
    
    class Triangle(Shape):
        """Concrete implementation of Shape"""
        
        def __init__(self, side1, side2, side3):
            super().__init__("Triangle")
            self.side1 = side1
            self.side2 = side2
            self.side3 = side3
        
        def area(self):
            """Implement abstract method using Heron's formula"""
            s = self.perimeter() / 2
            import math
            return math.sqrt(s * (s - self.side1) * (s - self.side2) * (s - self.side3))
        
        def perimeter(self):
            """Implement abstract method"""
            return self.side1 + self.side2 + self.side3
    
    print("   class Rectangle(Shape):")
    print("       def area(self): return self.width * self.height")
    print("       def perimeter(self): return 2 * (self.width + self.height)")
    
    print("\n3. USING ABSTRACT CLASSES:")
    
    # Cannot instantiate abstract class
    print("   # shape = Shape('Generic')  # This would raise TypeError!")
    
    # Create concrete instances
    rectangle = Rectangle(5, 3)
    circle = Circle(4)
    triangle = Triangle(3, 4, 5)
    
    shapes = [rectangle, circle, triangle]
    
    print("   rectangle = Rectangle(5, 3)")
    print("   circle = Circle(4)")
    print("   triangle = Triangle(3, 4, 5)")
    
    print("\n4. POLYMORPHISM WITH ABSTRACT CLASSES:")
    for shape in shapes:
        print(f"   {shape.describe()}")
    
    print("\n5. SHAPE CALCULATIONS:")
    total_area = sum(shape.area() for shape in shapes)
    total_perimeter = sum(shape.perimeter() for shape in shapes)
    
    print(f"   Total area of all shapes: {total_area:.2f}")
    print(f"   Total perimeter of all shapes: {total_perimeter:.2f}")

def interactive_inheritance_demo():
    """Demo interaktif inheritance concepts"""
    print("\nDEMO INTERAKTIF INHERITANCE")
    print("-" * 30)
    
    # Employee hierarchy
    class Employee:
        def __init__(self, name, employee_id, salary):
            self.name = name
            self.employee_id = employee_id
            self.salary = salary
        
        def work(self):
            return f"{self.name} is working"
        
        def get_info(self):
            return f"Employee: {self.name} (ID: {self.employee_id}), Salary: ${self.salary}"
    
    class Developer(Employee):
        def __init__(self, name, employee_id, salary, programming_language):
            super().__init__(name, employee_id, salary)
            self.programming_language = programming_language
            self.projects = []
        
        def work(self):
            return f"{self.name} is coding in {self.programming_language}"
        
        def add_project(self, project):
            self.projects.append(project)
            return f"Added project: {project}"
        
        def get_info(self):
            base_info = super().get_info()
            return f"{base_info}, Language: {self.programming_language}, Projects: {len(self.projects)}"
    
    class Manager(Employee):
        def __init__(self, name, employee_id, salary, department):
            super().__init__(name, employee_id, salary)
            self.department = department
            self.team_members = []
        
        def work(self):
            return f"{self.name} is managing the {self.department} department"
        
        def add_team_member(self, employee):
            self.team_members.append(employee)
            return f"Added {employee.name} to team"
        
        def get_info(self):
            base_info = super().get_info()
            return f"{base_info}, Department: {self.department}, Team size: {len(self.team_members)}"
    
    # Create some employees
    employees = []
    
    print("Company Employee Management System")
    
    while True:
        print("\nOptions:")
        print("1. Add Developer")
        print("2. Add Manager")
        print("3. List all employees")
        print("4. Employee details")
        print("5. Add project to developer")
        print("6. Add team member to manager")
        print("7. Exit")
        
        choice = input("Choose option (1-7): ").strip()
        
        if choice == "1":
            name = input("Developer name: ").strip()
            emp_id = input("Employee ID: ").strip()
            try:
                salary = float(input("Salary: "))
                language = input("Programming language: ").strip()
                
                dev = Developer(name, emp_id, salary, language)
                employees.append(dev)
                print(f"Added developer: {name}")
            except ValueError:
                print("Invalid salary amount")
        
        elif choice == "2":
            name = input("Manager name: ").strip()
            emp_id = input("Employee ID: ").strip()
            try:
                salary = float(input("Salary: "))
                department = input("Department: ").strip()
                
                mgr = Manager(name, emp_id, salary, department)
                employees.append(mgr)
                print(f"Added manager: {name}")
            except ValueError:
                print("Invalid salary amount")
        
        elif choice == "3":
            if not employees:
                print("No employees found")
            else:
                print("\nAll employees:")
                for i, emp in enumerate(employees, 1):
                    print(f"{i}. {emp.get_info()}")
        
        elif choice == "4":
            if not employees:
                print("No employees found")
                continue
            
            try:
                emp_num = int(input(f"Employee number (1-{len(employees)}): ")) - 1
                if 0 <= emp_num < len(employees):
                    emp = employees[emp_num]
                    print(f"\nEmployee Details:")
                    print(emp.get_info())
                    print(f"Current activity: {emp.work()}")
                    
                    if isinstance(emp, Developer):
                        print(f"Projects: {emp.projects}")
                    elif isinstance(emp, Manager):
                        print(f"Team members: {[member.name for member in emp.team_members]}")
                else:
                    print("Invalid employee number")
            except ValueError:
                print("Please enter a valid number")
        
        elif choice == "5":
            developers = [emp for emp in employees if isinstance(emp, Developer)]
            if not developers:
                print("No developers found")
                continue
            
            print("Developers:")
            for i, dev in enumerate(developers, 1):
                print(f"{i}. {dev.name}")
            
            try:
                dev_num = int(input(f"Developer number (1-{len(developers)}): ")) - 1
                if 0 <= dev_num < len(developers):
                    project = input("Project name: ").strip()
                    if project:
                        result = developers[dev_num].add_project(project)
                        print(result)
                else:
                    print("Invalid developer number")
            except ValueError:
                print("Please enter a valid number")
        
        elif choice == "6":
            managers = [emp for emp in employees if isinstance(emp, Manager)]
            if not managers:
                print("No managers found")
                continue
            
            print("Managers:")
            for i, mgr in enumerate(managers, 1):
                print(f"{i}. {mgr.name}")
            
            try:
                mgr_num = int(input(f"Manager number (1-{len(managers)}): ")) - 1
                if 0 <= mgr_num < len(managers):
                    print("Available employees:")
                    available = [emp for emp in employees if emp not in managers[mgr_num].team_members]
                    for i, emp in enumerate(available, 1):
                        print(f"{i}. {emp.name}")
                    
                    emp_num = int(input(f"Employee number (1-{len(available)}): ")) - 1
                    if 0 <= emp_num < len(available):
                        result = managers[mgr_num].add_team_member(available[emp_num])
                        print(result)
                    else:
                        print("Invalid employee number")
                else:
                    print("Invalid manager number")
            except ValueError:
                print("Please enter a valid number")
        
        elif choice == "7":
            print("Thank you!")
            break
        
        else:
            print("Invalid choice")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI INHERITANCE PYTHON")
    print("===========================")
    print("Program ini mendemonstrasikan konsep inheritance dalam OOP:")
    print("pewarisan, method overriding, multiple inheritance, dan abstract classes.")
    
    print_separator("BASIC INHERITANCE")
    demonstrate_basic_inheritance()
    
    print_separator("METHOD OVERRIDING")
    demonstrate_method_overriding()
    
    print_separator("MULTIPLE INHERITANCE")
    demonstrate_multiple_inheritance()
    
    print_separator("ABSTRACT CLASSES")
    demonstrate_abstract_classes()
    
    print_separator("DEMO INTERAKTIF")
    interactive_inheritance_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN INHERITANCE:")
    print("- Inheritance: Child class mewarisi dari parent class")
    print("- Method overriding: Mengubah implementasi method parent")
    print("- super(): Mengakses method parent dari child")
    print("- Multiple inheritance: Mewarisi dari multiple parents")
    print("- Abstract classes: Mendefinisikan interface untuk subclasses")
    print("- Polymorphism: Objek berbeda merespons method yang sama")
    print("="*60)

if __name__ == "__main__":
    main()
