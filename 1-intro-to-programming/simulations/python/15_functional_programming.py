"""
SIMULASI PEMROGRAMAN FUNGSIONAL / FUNGSI DAN PROSEDUR
=====================================================

Pemrograman fungsional adalah paradigma yang memperlakukan komputasi
sebagai evaluasi fungsi matematika. Python mendukung konsep functional
programming meskipun bukan bahasa functional murni.

Konsep utama Functional Programming:
1. FUNCTIONS: First-class objects yang bisa dipassing sebagai argument
2. PURE FUNCTIONS: Tidak ada side effects, output hanya bergantung input
3. HIGHER-ORDER FUNCTIONS: Fungsi yang menerima atau mengembalikan fungsi
4. LAMBDA FUNCTIONS: Anonymous functions
5. MAP, FILTER, REDUCE: Built-in functional tools
6. CLOSURES: Fungsi yang mengakses variabel dari scope luar
7. DECORATORS: Fungsi yang memodifikasi fungsi lain

Functional programming membuat kode lebih predictable dan testable.
"""

from functools import reduce, partial, wraps
import time
import math

# Global variables for demonstration
counter = 0
log_messages = []

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_basic_functions():
    """Demonstrasi fungsi dasar dan konsep"""
    print("FUNCTIONS adalah blok kode yang dapat dipanggil berulang kali.")
    print("Python memperlakukan functions sebagai first-class objects.")

    print("\n1. DEFINISI FUNGSI:")

    def greet(name):
        """Fungsi sederhana dengan parameter"""
        return f"Hello, {name}!"

    def add(a, b):
        """Fungsi dengan multiple parameters"""
        return a + b

    def calculate_area(length, width=1):
        """Fungsi dengan default parameter"""
        return length * width

    def get_stats(*numbers):
        """Fungsi dengan variable arguments"""
        if not numbers:
            return {"count": 0, "sum": 0, "avg": 0}

        return {
            "count": len(numbers),
            "sum": sum(numbers),
            "avg": sum(numbers) / len(numbers),
            "min": min(numbers),
            "max": max(numbers)
        }

    def create_person(**kwargs):
        """Fungsi dengan keyword arguments"""
        person = {
            "name": kwargs.get("name", "Unknown"),
            "age": kwargs.get("age", 0),
            "city": kwargs.get("city", "Unknown")
        }
        return person

    print("   def greet(name):")
    print("       return f'Hello, {name}!'")
    print("   def add(a, b):")
    print("       return a + b")
    print("   def get_stats(*numbers): ...")
    print("   def create_person(**kwargs): ...")

    print("\n2. MEMANGGIL FUNGSI:")
    print(f"   greet('Alice') = '{greet('Alice')}'")
    print(f"   add(5, 3) = {add(5, 3)}")
    print(f"   calculate_area(5) = {calculate_area(5)} (default width=1)")
    print(f"   calculate_area(5, 3) = {calculate_area(5, 3)}")

    stats = get_stats(1, 2, 3, 4, 5)
    print(f"   get_stats(1, 2, 3, 4, 5) = {stats}")

    person = create_person(name="Bob", age=30, city="Jakarta")
    print(f"   create_person(name='Bob', age=30, city='Jakarta') = {person}")

    print("\n3. FUNCTIONS SEBAGAI FIRST-CLASS OBJECTS:")

    # Assign function ke variabel
    my_func = greet
    print(f"   my_func = greet")
    print(f"   my_func('Charlie') = '{my_func('Charlie')}'")

    # Function dalam list
    operations = [add, lambda x, y: x - y, lambda x, y: x * y, lambda x, y: x / y]
    print(f"   operations = [add, subtract, multiply, divide]")

    a, b = 10, 3
    for i, op in enumerate(operations):
        try:
            result = op(a, b)
            op_name = ["add", "subtract", "multiply", "divide"][i]
            print(f"   {op_name}({a}, {b}) = {result}")
        except ZeroDivisionError:
            print(f"   divide({a}, {b}) = Error: Division by zero")

    print("\n4. NESTED FUNCTIONS:")

    def outer_function(x):
        """Fungsi yang mengandung fungsi lain"""

        def inner_function(y):
            """Nested function"""
            return x + y

        return inner_function

    print("   def outer_function(x):")
    print("       def inner_function(y):")
    print("           return x + y")
    print("       return inner_function")

    add_five = outer_function(5)
    print(f"   add_five = outer_function(5)")
    print(f"   add_five(3) = {add_five(3)}")
    print(f"   add_five(7) = {add_five(7)}")

def demonstrate_pure_functions():
    """Demonstrasi pure functions vs impure functions"""
    print("PURE FUNCTIONS tidak memiliki side effects dan output hanya")
    print("bergantung pada input. Ini membuat fungsi predictable dan testable.")

    print("\n1. PURE FUNCTIONS:")

    def pure_add(a, b):
        """Pure function - no side effects"""
        return a + b

    def pure_multiply_list(numbers, factor):
        """Pure function - returns new list"""
        return [num * factor for num in numbers]

    def pure_calculate_tax(amount, rate):
        """Pure function - deterministic"""
        return amount * rate

    print("   def pure_add(a, b):")
    print("       return a + b  # No side effects")

    print("   def pure_multiply_list(numbers, factor):")
    print("       return [num * factor for num in numbers]  # Returns new list")

    # Test pure functions
    print(f"   pure_add(5, 3) = {pure_add(5, 3)}")
    print(f"   pure_add(5, 3) = {pure_add(5, 3)} (same result)")

    original_list = [1, 2, 3, 4]
    multiplied = pure_multiply_list(original_list, 2)
    print(f"   original_list = {original_list}")
    print(f"   pure_multiply_list(original_list, 2) = {multiplied}")
    print(f"   original_list = {original_list} (unchanged)")

    print("\n2. IMPURE FUNCTIONS:")

    # Reset global state for demonstration
    global counter, log_messages
    counter = 0
    log_messages = []

    def impure_increment():
        """Impure function - modifies global state"""
        global counter
        counter += 1
        return counter

    def impure_log_and_add(a, b):
        """Impure function - has side effects"""
        result = a + b
        log_messages.append(f"Added {a} + {b} = {result}")
        print(f"   LOG: Added {a} + {b} = {result}")
        return result

    def impure_modify_list(numbers, factor):
        """Impure function - modifies input"""
        for i in range(len(numbers)):
            numbers[i] *= factor
        return numbers

    print("   def impure_increment():")
    print("       global counter")
    print("       counter += 1  # Modifies global state")
    print("       return counter")

    print(f"   counter = {counter}")
    print(f"   impure_increment() = {impure_increment()}")
    print(f"   impure_increment() = {impure_increment()}")
    print(f"   counter = {counter} (changed)")

    print(f"   impure_log_and_add(3, 4) = {impure_log_and_add(3, 4)}")
    print(f"   log_messages = {log_messages}")

    test_list = [1, 2, 3]
    print(f"   test_list = {test_list}")
    result = impure_modify_list(test_list, 3)
    print(f"   impure_modify_list(test_list, 3) = {result}")
    print(f"   test_list = {test_list} (modified!)")

    print("\n3. MENGAPA PURE FUNCTIONS LEBIH BAIK:")
    print("   - Predictable: Same input always gives same output")
    print("   - Testable: Easy to unit test")
    print("   - Cacheable: Results can be memoized")
    print("   - Parallelizable: Safe to run concurrently")
    print("   - Debuggable: No hidden dependencies")

def demonstrate_higher_order_functions():
    """Demonstrasi higher-order functions"""
    print("HIGHER-ORDER FUNCTIONS adalah fungsi yang menerima fungsi lain")
    print("sebagai parameter atau mengembalikan fungsi.")

    print("\n1. FUNCTIONS SEBAGAI PARAMETERS:")

    def apply_operation(numbers, operation):
        """Apply operation to all numbers"""
        return [operation(num) for num in numbers]

    def apply_binary_operation(a, b, operation):
        """Apply binary operation to two values"""
        return operation(a, b)

    def filter_numbers(numbers, condition):
        """Filter numbers based on condition"""
        return [num for num in numbers if condition(num)]

    # Define some operations
    def square(x):
        return x ** 2

    def cube(x):
        return x ** 3

    def is_even(x):
        return x % 2 == 0

    def is_positive(x):
        return x > 0

    print("   def apply_operation(numbers, operation):")
    print("       return [operation(num) for num in numbers]")

    numbers = [1, 2, 3, 4, 5]
    print(f"   numbers = {numbers}")

    squared = apply_operation(numbers, square)
    print(f"   apply_operation(numbers, square) = {squared}")

    cubed = apply_operation(numbers, cube)
    print(f"   apply_operation(numbers, cube) = {cubed}")

    test_numbers = [-2, -1, 0, 1, 2, 3, 4]
    evens = filter_numbers(test_numbers, is_even)
    positives = filter_numbers(test_numbers, is_positive)

    print(f"   test_numbers = {test_numbers}")
    print(f"   filter_numbers(test_numbers, is_even) = {evens}")
    print(f"   filter_numbers(test_numbers, is_positive) = {positives}")

    print("\n2. FUNCTIONS YANG MENGEMBALIKAN FUNCTIONS:")

    def create_multiplier(factor):
        """Returns a function that multiplies by factor"""
        def multiplier(x):
            return x * factor
        return multiplier

    def create_validator(min_val, max_val):
        """Returns a validation function"""
        def validator(value):
            return min_val <= value <= max_val
        return validator

    def create_formatter(prefix, suffix):
        """Returns a formatting function"""
        def formatter(text):
            return f"{prefix}{text}{suffix}"
        return formatter

    print("   def create_multiplier(factor):")
    print("       def multiplier(x):")
    print("           return x * factor")
    print("       return multiplier")

    multiply_by_3 = create_multiplier(3)
    multiply_by_10 = create_multiplier(10)

    print(f"   multiply_by_3 = create_multiplier(3)")
    print(f"   multiply_by_3(5) = {multiply_by_3(5)}")
    print(f"   multiply_by_10(7) = {multiply_by_10(7)}")

    age_validator = create_validator(0, 120)
    score_validator = create_validator(0, 100)

    print(f"   age_validator = create_validator(0, 120)")
    print(f"   age_validator(25) = {age_validator(25)}")
    print(f"   age_validator(150) = {age_validator(150)}")

    html_formatter = create_formatter("<b>", "</b>")
    print(f"   html_formatter = create_formatter('<b>', '</b>')")
    print(f"   html_formatter('Hello') = '{html_formatter('Hello')}'")

def demonstrate_lambda_functions():
    """Demonstrasi lambda functions"""
    print("LAMBDA FUNCTIONS adalah anonymous functions yang ditulis dalam")
    print("satu baris. Berguna untuk operasi sederhana.")

    print("\n1. BASIC LAMBDA FUNCTIONS:")

    # Basic lambda
    add = lambda x, y: x + y
    square = lambda x: x ** 2
    is_even = lambda x: x % 2 == 0

    print("   add = lambda x, y: x + y")
    print("   square = lambda x: x ** 2")
    print("   is_even = lambda x: x % 2 == 0")

    print(f"   add(5, 3) = {add(5, 3)}")
    print(f"   square(4) = {square(4)}")
    print(f"   is_even(6) = {is_even(6)}")
    print(f"   is_even(7) = {is_even(7)}")

    print("\n2. LAMBDA DENGAN HIGHER-ORDER FUNCTIONS:")

    numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    print(f"   numbers = {numbers}")

    # Using lambda with map
    squared = list(map(lambda x: x ** 2, numbers))
    print(f"   map(lambda x: x ** 2, numbers) = {squared}")

    # Using lambda with filter
    evens = list(filter(lambda x: x % 2 == 0, numbers))
    print(f"   filter(lambda x: x % 2 == 0, numbers) = {evens}")

    # Using lambda with sorted
    words = ["python", "java", "c", "javascript", "go"]
    by_length = sorted(words, key=lambda x: len(x))
    print(f"   words = {words}")
    print(f"   sorted(words, key=lambda x: len(x)) = {by_length}")

    # Using lambda with reduce
    product = reduce(lambda x, y: x * y, [1, 2, 3, 4, 5])
    print(f"   reduce(lambda x, y: x * y, [1, 2, 3, 4, 5]) = {product}")

    print("\n3. LAMBDA DALAM DATA STRUCTURES:")

    # Dictionary dengan lambda values
    operations = {
        'add': lambda x, y: x + y,
        'subtract': lambda x, y: x - y,
        'multiply': lambda x, y: x * y,
        'divide': lambda x, y: x / y if y != 0 else None
    }

    print("   operations = {")
    print("       'add': lambda x, y: x + y,")
    print("       'subtract': lambda x, y: x - y,")
    print("       'multiply': lambda x, y: x * y,")
    print("       'divide': lambda x, y: x / y if y != 0 else None")
    print("   }")

    a, b = 10, 3
    for op_name, op_func in operations.items():
        result = op_func(a, b)
        print(f"   operations['{op_name}']({a}, {b}) = {result}")

    # List dengan lambda functions
    validators = [
        lambda x: x > 0,           # positive
        lambda x: x % 2 == 0,      # even
        lambda x: x < 100,         # less than 100
        lambda x: len(str(x)) <= 2 # max 2 digits
    ]

    test_value = 42
    print(f"   test_value = {test_value}")
    print("   Validation results:")

    validation_names = ["positive", "even", "less than 100", "max 2 digits"]
    for i, validator in enumerate(validators):
        result = validator(test_value)
        print(f"     {validation_names[i]}: {result}")

def demonstrate_map_filter_reduce():
    """Demonstrasi map, filter, dan reduce"""
    print("MAP, FILTER, dan REDUCE adalah built-in functions untuk")
    print("functional programming yang sangat powerful.")

    print("\n1. MAP - Apply function to all elements:")

    numbers = [1, 2, 3, 4, 5]
    print(f"   numbers = {numbers}")

    # Map dengan function
    def celsius_to_fahrenheit(c):
        return (c * 9/5) + 32

    celsius_temps = [0, 20, 30, 37, 100]
    fahrenheit_temps = list(map(celsius_to_fahrenheit, celsius_temps))

    print(f"   celsius_temps = {celsius_temps}")
    print(f"   map(celsius_to_fahrenheit, celsius_temps) = {fahrenheit_temps}")

    # Map dengan lambda
    squared = list(map(lambda x: x ** 2, numbers))
    print(f"   map(lambda x: x ** 2, numbers) = {squared}")

    # Map dengan multiple iterables
    list1 = [1, 2, 3, 4]
    list2 = [10, 20, 30, 40]
    sums = list(map(lambda x, y: x + y, list1, list2))

    print(f"   list1 = {list1}")
    print(f"   list2 = {list2}")
    print(f"   map(lambda x, y: x + y, list1, list2) = {sums}")

    print("\n2. FILTER - Filter elements based on condition:")

    numbers = list(range(1, 21))  # 1 to 20
    print(f"   numbers = {numbers}")

    # Filter even numbers
    evens = list(filter(lambda x: x % 2 == 0, numbers))
    print(f"   filter(lambda x: x % 2 == 0, numbers) = {evens}")

    # Filter numbers > 10
    greater_than_10 = list(filter(lambda x: x > 10, numbers))
    print(f"   filter(lambda x: x > 10, numbers) = {greater_than_10}")

    # Filter strings
    words = ["python", "java", "c", "javascript", "go", "rust"]
    long_words = list(filter(lambda word: len(word) > 4, words))

    print(f"   words = {words}")
    print(f"   filter(lambda word: len(word) > 4, words) = {long_words}")

    # Filter with custom function
    def is_prime(n):
        if n < 2:
            return False
        for i in range(2, int(n ** 0.5) + 1):
            if n % i == 0:
                return False
        return True

    primes = list(filter(is_prime, numbers))
    print(f"   filter(is_prime, numbers) = {primes}")

    print("\n3. REDUCE - Reduce sequence to single value:")

    numbers = [1, 2, 3, 4, 5]
    print(f"   numbers = {numbers}")

    # Sum using reduce
    total = reduce(lambda x, y: x + y, numbers)
    print(f"   reduce(lambda x, y: x + y, numbers) = {total}")

    # Product using reduce
    product = reduce(lambda x, y: x * y, numbers)
    print(f"   reduce(lambda x, y: x * y, numbers) = {product}")

    # Find maximum using reduce
    numbers_with_duplicates = [3, 7, 2, 9, 1, 8, 5]
    maximum = reduce(lambda x, y: x if x > y else y, numbers_with_duplicates)
    print(f"   numbers = {numbers_with_duplicates}")
    print(f"   reduce(lambda x, y: x if x > y else y, numbers) = {maximum}")

    # Concatenate strings
    words = ["Hello", "functional", "programming", "world"]
    sentence = reduce(lambda x, y: x + " " + y, words)
    print(f"   words = {words}")
    print(f"   reduce(lambda x, y: x + ' ' + y, words) = '{sentence}'")

    # Reduce with initial value
    numbers = [1, 2, 3, 4, 5]
    sum_with_initial = reduce(lambda x, y: x + y, numbers, 100)
    print(f"   reduce(lambda x, y: x + y, numbers, 100) = {sum_with_initial}")

def demonstrate_closures():
    """Demonstrasi closures"""
    print("CLOSURES adalah fungsi yang mengakses variabel dari scope luar")
    print("dan 'mengingat' nilai tersebut bahkan setelah scope luar selesai.")

    print("\n1. BASIC CLOSURE:")

    def create_counter():
        """Returns a counter function with closure"""
        count = 0

        def counter():
            nonlocal count
            count += 1
            return count

        return counter

    print("   def create_counter():")
    print("       count = 0")
    print("       def counter():")
    print("           nonlocal count")
    print("           count += 1")
    print("           return count")
    print("       return counter")

    counter1 = create_counter()
    counter2 = create_counter()

    print(f"   counter1 = create_counter()")
    print(f"   counter2 = create_counter()")

    print(f"   counter1() = {counter1()}")
    print(f"   counter1() = {counter1()}")
    print(f"   counter2() = {counter2()}")
    print(f"   counter1() = {counter1()}")
    print(f"   counter2() = {counter2()}")

    print("\n2. CLOSURE DENGAN PARAMETERS:")

    def create_accumulator(initial_value):
        """Returns an accumulator function"""
        total = initial_value

        def accumulate(value):
            nonlocal total
            total += value
            return total

        return accumulate

    def create_multiplier_closure(factor):
        """Returns a multiplier function with closure"""
        def multiply(value):
            return value * factor
        return multiply

    print("   def create_accumulator(initial_value): ...")

    acc1 = create_accumulator(0)
    acc2 = create_accumulator(100)

    print(f"   acc1 = create_accumulator(0)")
    print(f"   acc2 = create_accumulator(100)")

    print(f"   acc1(5) = {acc1(5)}")
    print(f"   acc1(10) = {acc1(10)}")
    print(f"   acc2(5) = {acc2(5)}")
    print(f"   acc1(3) = {acc1(3)}")

    # Multiple multipliers
    double = create_multiplier_closure(2)
    triple = create_multiplier_closure(3)

    print(f"   double = create_multiplier_closure(2)")
    print(f"   triple = create_multiplier_closure(3)")
    print(f"   double(5) = {double(5)}")
    print(f"   triple(5) = {triple(5)}")

    print("\n3. PRACTICAL CLOSURE EXAMPLE:")

    def create_bank_account(initial_balance):
        """Bank account with closure for encapsulation"""
        balance = initial_balance

        def deposit(amount):
            nonlocal balance
            if amount > 0:
                balance += amount
                return f"Deposited ${amount}. New balance: ${balance}"
            return "Invalid deposit amount"

        def withdraw(amount):
            nonlocal balance
            if 0 < amount <= balance:
                balance -= amount
                return f"Withdrew ${amount}. New balance: ${balance}"
            return "Invalid withdrawal amount or insufficient funds"

        def get_balance():
            return f"Current balance: ${balance}"

        # Return dictionary of functions
        return {
            'deposit': deposit,
            'withdraw': withdraw,
            'balance': get_balance
        }

    print("   def create_bank_account(initial_balance): ...")

    account = create_bank_account(1000)
    print(f"   account = create_bank_account(1000)")

    print(f"   account['balance']() = '{account['balance']()}'")
    print(f"   account['deposit'](500) = '{account['deposit'](500)}'")
    print(f"   account['withdraw'](200) = '{account['withdraw'](200)}'")
    print(f"   account['balance']() = '{account['balance']()}'")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI PEMROGRAMAN FUNGSIONAL PYTHON")
    print("======================================")
    print("Program ini mendemonstrasikan konsep functional programming:")
    print("functions, pure functions, higher-order functions, lambda, dan closures.")

    print_separator("BASIC FUNCTIONS")
    demonstrate_basic_functions()

    print_separator("PURE vs IMPURE FUNCTIONS")
    demonstrate_pure_functions()

    print_separator("HIGHER-ORDER FUNCTIONS")
    demonstrate_higher_order_functions()

    print_separator("LAMBDA FUNCTIONS")
    demonstrate_lambda_functions()

    print_separator("MAP, FILTER, REDUCE")
    demonstrate_map_filter_reduce()

    print_separator("CLOSURES")
    demonstrate_closures()

    print("\n" + "="*60)
    print("RINGKASAN FUNCTIONAL PROGRAMMING:")
    print("- Functions: First-class objects, dapat dipassing sebagai argument")
    print("- Pure functions: No side effects, predictable output")
    print("- Higher-order functions: Menerima atau mengembalikan functions")
    print("- Lambda: Anonymous functions untuk operasi sederhana")
    print("- Map/Filter/Reduce: Built-in tools untuk data transformation")
    print("- Closures: Functions yang mengakses variabel dari outer scope")
    print("="*60)

if __name__ == "__main__":
    main()