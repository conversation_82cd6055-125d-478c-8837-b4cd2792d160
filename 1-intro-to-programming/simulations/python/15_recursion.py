"""
SIMULASI REKURSI
================

Rekursi adalah teknik pemrograman di mana fungsi memanggil dirinya sendiri
untuk menyelesaikan masalah yang lebih kecil. Rekursi sangat berguna untuk
masalah yang memiliki struktur berulang atau dapat dipecah menjadi submasalah.

Komponen utama rekursi:
1. BASE CASE: Kondisi yang menghentikan rekursi
2. RECURSIVE CASE: Fungsi memanggil dirinya dengan input yang lebih kecil
3. PROGRESS: Setiap panggilan harus menuju ke base case

Rekursi vs Iterasi:
- Rekursi: Lebih elegant untuk masalah tertentu, tapi menggunakan lebih banyak memori
- Iterasi: Lebih efisien memori, tapi kadang lebih kompleks untuk ditulis

Rekursi berguna untuk: tree traversal, mathematical sequences, divide & conquer.
"""

import sys
import time
from functools import lru_cache

# Global variables for demonstration
call_count = 0
call_count_lru = 0

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_basic_recursion():
    """Demonstrasi rekursi dasar"""
    print("REKURSI adalah fungsi yang memanggil dirinya sendiri.")
    print("Setiap fungsi rekursif harus memiliki base case dan recursive case.")
    
    print("\n1. CONTOH SEDERHANA - COUNTDOWN:")
    
    def countdown(n):
        """Recursive countdown function"""
        print(f"     {n}")
        
        # Base case
        if n <= 0:
            print("     Blast off!")
            return
        
        # Recursive case
        countdown(n - 1)
    
    print("   def countdown(n):")
    print("       print(f'{n}')")
    print("       if n <= 0:")
    print("           print('Blast off!')")
    print("           return")
    print("       countdown(n - 1)")
    
    print("   countdown(5):")
    countdown(5)
    
    print("\n2. FACTORIAL - Contoh Klasik:")
    
    def factorial_recursive(n):
        """Calculate factorial using recursion"""
        # Base case
        if n <= 1:
            return 1
        
        # Recursive case
        return n * factorial_recursive(n - 1)
    
    def factorial_iterative(n):
        """Calculate factorial using iteration for comparison"""
        result = 1
        for i in range(1, n + 1):
            result *= i
        return result
    
    print("   def factorial_recursive(n):")
    print("       if n <= 1:")
    print("           return 1")
    print("       return n * factorial_recursive(n - 1)")
    
    test_numbers = [0, 1, 5, 7, 10]
    print("   Comparison - Recursive vs Iterative:")
    for num in test_numbers:
        rec_result = factorial_recursive(num)
        iter_result = factorial_iterative(num)
        print(f"     factorial({num}): recursive={rec_result}, iterative={iter_result}")
    
    print("\n3. FIBONACCI SEQUENCE:")
    
    def fibonacci_recursive(n):
        """Calculate nth Fibonacci number using recursion"""
        # Base cases
        if n <= 0:
            return 0
        elif n == 1:
            return 1
        
        # Recursive case
        return fibonacci_recursive(n - 1) + fibonacci_recursive(n - 2)
    
    def fibonacci_iterative(n):
        """Calculate nth Fibonacci number using iteration"""
        if n <= 0:
            return 0
        elif n == 1:
            return 1
        
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b
    
    print("   def fibonacci_recursive(n):")
    print("       if n <= 0: return 0")
    print("       elif n == 1: return 1")
    print("       return fibonacci_recursive(n-1) + fibonacci_recursive(n-2)")
    
    print("   Fibonacci sequence (first 10 numbers):")
    fib_sequence = []
    for i in range(10):
        fib_num = fibonacci_recursive(i)
        fib_sequence.append(fib_num)
    
    print(f"     {fib_sequence}")
    
    # Performance comparison for small numbers
    print("   Performance comparison (n=10):")
    
    start_time = time.time()
    rec_result = fibonacci_recursive(10)
    rec_time = time.time() - start_time
    
    start_time = time.time()
    iter_result = fibonacci_iterative(10)
    iter_time = time.time() - start_time
    
    print(f"     Recursive: {rec_result} (time: {rec_time:.6f}s)")
    print(f"     Iterative: {iter_result} (time: {iter_time:.6f}s)")

def demonstrate_recursion_with_data_structures():
    """Demonstrasi rekursi dengan struktur data"""
    print("REKURSI sangat berguna untuk memproses struktur data hierarkis")
    print("seperti lists, trees, dan nested structures.")
    
    print("\n1. RECURSIVE LIST OPERATIONS:")
    
    def sum_list_recursive(lst):
        """Calculate sum of list using recursion"""
        # Base case
        if not lst:
            return 0
        
        # Recursive case
        return lst[0] + sum_list_recursive(lst[1:])
    
    def find_max_recursive(lst):
        """Find maximum in list using recursion"""
        # Base case
        if len(lst) == 1:
            return lst[0]
        
        # Recursive case
        max_of_rest = find_max_recursive(lst[1:])
        return lst[0] if lst[0] > max_of_rest else max_of_rest
    
    def reverse_list_recursive(lst):
        """Reverse list using recursion"""
        # Base case
        if len(lst) <= 1:
            return lst
        
        # Recursive case
        return reverse_list_recursive(lst[1:]) + [lst[0]]
    
    print("   def sum_list_recursive(lst):")
    print("       if not lst: return 0")
    print("       return lst[0] + sum_list_recursive(lst[1:])")
    
    test_list = [1, 3, 5, 7, 9, 2, 4, 6, 8]
    print(f"   test_list = {test_list}")
    
    print(f"   sum_list_recursive(test_list) = {sum_list_recursive(test_list)}")
    print(f"   find_max_recursive(test_list) = {find_max_recursive(test_list)}")
    print(f"   reverse_list_recursive(test_list) = {reverse_list_recursive(test_list)}")
    
    print("\n2. NESTED LIST PROCESSING:")
    
    def flatten_list(nested_list):
        """Flatten nested list using recursion"""
        result = []
        
        for item in nested_list:
            if isinstance(item, list):
                # Recursive case: item is a list
                result.extend(flatten_list(item))
            else:
                # Base case: item is not a list
                result.append(item)
        
        return result
    
    def count_elements(nested_list):
        """Count total elements in nested list"""
        count = 0
        
        for item in nested_list:
            if isinstance(item, list):
                # Recursive case
                count += count_elements(item)
            else:
                # Base case
                count += 1
        
        return count
    
    nested = [1, [2, 3], [4, [5, 6]], 7, [8, [9, [10]]]]
    print(f"   nested = {nested}")
    
    flattened = flatten_list(nested)
    total_count = count_elements(nested)
    
    print(f"   flatten_list(nested) = {flattened}")
    print(f"   count_elements(nested) = {total_count}")
    
    print("\n3. BINARY TREE TRAVERSAL:")
    
    class TreeNode:
        def __init__(self, value):
            self.value = value
            self.left = None
            self.right = None
        
        def __str__(self):
            return str(self.value)
    
    def inorder_traversal(node):
        """Inorder traversal: left -> root -> right"""
        if node is None:
            return []
        
        result = []
        result.extend(inorder_traversal(node.left))   # Left
        result.append(node.value)                     # Root
        result.extend(inorder_traversal(node.right))  # Right
        
        return result
    
    def preorder_traversal(node):
        """Preorder traversal: root -> left -> right"""
        if node is None:
            return []
        
        result = []
        result.append(node.value)                      # Root
        result.extend(preorder_traversal(node.left))   # Left
        result.extend(preorder_traversal(node.right))  # Right
        
        return result
    
    def tree_height(node):
        """Calculate height of tree"""
        if node is None:
            return 0
        
        left_height = tree_height(node.left)
        right_height = tree_height(node.right)
        
        return 1 + max(left_height, right_height)
    
    # Create a sample binary tree
    #       1
    #      / \
    #     2   3
    #    / \   \
    #   4   5   6
    
    root = TreeNode(1)
    root.left = TreeNode(2)
    root.right = TreeNode(3)
    root.left.left = TreeNode(4)
    root.left.right = TreeNode(5)
    root.right.right = TreeNode(6)
    
    print("   Binary tree:")
    print("         1")
    print("        / \\")
    print("       2   3")
    print("      / \\   \\")
    print("     4   5   6")
    
    print(f"   inorder_traversal(root) = {inorder_traversal(root)}")
    print(f"   preorder_traversal(root) = {preorder_traversal(root)}")
    print(f"   tree_height(root) = {tree_height(root)}")

def demonstrate_advanced_recursion():
    """Demonstrasi rekursi lanjutan"""
    print("REKURSI LANJUTAN untuk algoritma yang lebih kompleks.")
    
    print("\n1. TOWER OF HANOI:")
    
    def hanoi(n, source, destination, auxiliary):
        """Solve Tower of Hanoi puzzle"""
        if n == 1:
            print(f"     Move disk 1 from {source} to {destination}")
            return
        
        # Move n-1 disks from source to auxiliary
        hanoi(n-1, source, auxiliary, destination)
        
        # Move the largest disk from source to destination
        print(f"     Move disk {n} from {source} to {destination}")
        
        # Move n-1 disks from auxiliary to destination
        hanoi(n-1, auxiliary, destination, source)
    
    print("   def hanoi(n, source, destination, auxiliary): ...")
    print("   Solution for 3 disks:")
    hanoi(3, "A", "C", "B")
    
    print("\n2. QUICK SORT:")
    
    def quicksort(arr):
        """Sort array using quicksort algorithm"""
        # Base case
        if len(arr) <= 1:
            return arr
        
        # Choose pivot (middle element)
        pivot = arr[len(arr) // 2]
        
        # Partition
        left = [x for x in arr if x < pivot]
        middle = [x for x in arr if x == pivot]
        right = [x for x in arr if x > pivot]
        
        # Recursive case
        return quicksort(left) + middle + quicksort(right)
    
    def quicksort_with_steps(arr, depth=0):
        """Quicksort with step-by-step visualization"""
        indent = "  " * depth
        print(f"{indent}Sorting: {arr}")
        
        if len(arr) <= 1:
            print(f"{indent}Base case reached: {arr}")
            return arr
        
        pivot = arr[len(arr) // 2]
        print(f"{indent}Pivot: {pivot}")
        
        left = [x for x in arr if x < pivot]
        middle = [x for x in arr if x == pivot]
        right = [x for x in arr if x > pivot]
        
        print(f"{indent}Left: {left}, Middle: {middle}, Right: {right}")
        
        sorted_left = quicksort_with_steps(left, depth + 1)
        sorted_right = quicksort_with_steps(right, depth + 1)
        
        result = sorted_left + middle + sorted_right
        print(f"{indent}Result: {result}")
        return result
    
    print("   def quicksort(arr): ...")
    
    unsorted = [64, 34, 25, 12, 22, 11, 90]
    print(f"   Original array: {unsorted}")
    
    sorted_array = quicksort(unsorted.copy())
    print(f"   Sorted array: {sorted_array}")
    
    print("\n   Step-by-step quicksort for [3, 1, 4, 1, 5]:")
    quicksort_with_steps([3, 1, 4, 1, 5])
    
    print("\n3. PERMUTATIONS:")
    
    def generate_permutations(lst):
        """Generate all permutations of a list"""
        # Base case
        if len(lst) <= 1:
            return [lst]
        
        permutations = []
        
        for i in range(len(lst)):
            # Take current element
            current = lst[i]
            
            # Get remaining elements
            remaining = lst[:i] + lst[i+1:]
            
            # Generate permutations of remaining elements
            for perm in generate_permutations(remaining):
                # Add current element to front of each permutation
                permutations.append([current] + perm)
        
        return permutations
    
    print("   def generate_permutations(lst): ...")
    
    test_list = [1, 2, 3]
    perms = generate_permutations(test_list)
    
    print(f"   Permutations of {test_list}:")
    for i, perm in enumerate(perms, 1):
        print(f"     {i}: {perm}")

def demonstrate_memoization():
    """Demonstrasi memoization untuk optimasi rekursi"""
    global call_count, call_count_lru

    print("MEMOIZATION adalah teknik optimasi yang menyimpan hasil")
    print("perhitungan untuk menghindari perhitungan berulang.")

    print("\n1. FIBONACCI TANPA MEMOIZATION:")

    # Reset global counter
    call_count = 0

    def fibonacci_slow(n):
        """Fibonacci without memoization (slow for large n)"""
        global call_count
        call_count += 1
        
        if n <= 0:
            return 0
        elif n == 1:
            return 1
        
        return fibonacci_slow(n - 1) + fibonacci_slow(n - 2)
    
    print("   def fibonacci_slow(n): ...")
    
    call_count = 0
    result = fibonacci_slow(10)
    print(f"   fibonacci_slow(10) = {result}")
    print(f"   Function calls: {call_count}")
    
    print("\n2. FIBONACCI DENGAN MANUAL MEMOIZATION:")
    
    def fibonacci_memo():
        """Fibonacci with manual memoization"""
        cache = {}
        call_count = 0
        
        def fib(n):
            nonlocal call_count
            call_count += 1
            
            if n in cache:
                return cache[n]
            
            if n <= 0:
                result = 0
            elif n == 1:
                result = 1
            else:
                result = fib(n - 1) + fib(n - 2)
            
            cache[n] = result
            return result
        
        return fib, lambda: call_count, lambda: cache
    
    fib_func, get_count, get_cache = fibonacci_memo()
    
    result = fib_func(10)
    print(f"   fibonacci_memo(10) = {result}")
    print(f"   Function calls: {get_count()}")
    print(f"   Cache: {get_cache()}")
    
    print("\n3. FIBONACCI DENGAN @lru_cache:")

    # Reset global counter
    call_count_lru = 0

    @lru_cache(maxsize=None)
    def fibonacci_lru(n):
        """Fibonacci with LRU cache decorator"""
        global call_count_lru
        call_count_lru += 1
        
        if n <= 0:
            return 0
        elif n == 1:
            return 1
        
        return fibonacci_lru(n - 1) + fibonacci_lru(n - 2)
    
    print("   @lru_cache(maxsize=None)")
    print("   def fibonacci_lru(n): ...")
    
    call_count_lru = 0
    result = fibonacci_lru(10)
    print(f"   fibonacci_lru(10) = {result}")
    print(f"   Function calls: {call_count_lru}")
    print(f"   Cache info: {fibonacci_lru.cache_info()}")
    
    print("\n4. PERFORMANCE COMPARISON:")
    
    import time
    
    def time_function(func, n, name):
        """Time a function call"""
        start_time = time.time()
        result = func(n)
        end_time = time.time()
        print(f"   {name}({n}) = {result} (time: {(end_time - start_time)*1000:.2f}ms)")
        return result
    
    n = 20
    print(f"   Calculating Fibonacci({n}):")

    # Reset counters
    call_count = 0
    call_count_lru = 0
    fibonacci_lru.cache_clear()
    
    # Note: fibonacci_slow(20) would be very slow, so we use smaller number
    if n <= 15:
        time_function(fibonacci_slow, n, "fibonacci_slow")
    
    fib_func, get_count, get_cache = fibonacci_memo()
    time_function(fib_func, n, "fibonacci_memo")
    
    time_function(fibonacci_lru, n, "fibonacci_lru")

def demonstrate_recursion_limits():
    """Demonstrasi batas rekursi dan tail recursion"""
    print("RECURSION LIMITS dan cara mengatasinya.")
    
    print("\n1. PYTHON RECURSION LIMIT:")
    
    current_limit = sys.getrecursionlimit()
    print(f"   Current recursion limit: {current_limit}")
    
    def deep_recursion(n):
        """Function that goes deep into recursion"""
        if n <= 0:
            return 0
        return 1 + deep_recursion(n - 1)
    
    print("   def deep_recursion(n):")
    print("       if n <= 0: return 0")
    print("       return 1 + deep_recursion(n - 1)")
    
    # Test with safe number
    safe_n = min(100, current_limit // 2)
    result = deep_recursion(safe_n)
    print(f"   deep_recursion({safe_n}) = {result}")
    
    print(f"   Note: Calling deep_recursion({current_limit}) would cause RecursionError")
    
    print("\n2. CONVERTING RECURSION TO ITERATION:")
    
    def factorial_iterative_safe(n):
        """Safe iterative factorial for large numbers"""
        if n < 0:
            return None
        
        result = 1
        for i in range(1, n + 1):
            result *= i
        return result
    
    def sum_to_n_iterative(n):
        """Iterative sum instead of recursive"""
        return n * (n + 1) // 2  # Mathematical formula
    
    def sum_to_n_recursive(n):
        """Recursive sum (for comparison)"""
        if n <= 0:
            return 0
        return n + sum_to_n_recursive(n - 1)
    
    print("   Converting recursive solutions to iterative:")
    
    large_n = 1000
    
    # Factorial
    iter_factorial = factorial_iterative_safe(large_n)
    print(f"   factorial_iterative({large_n}) = {str(iter_factorial)[:50]}... (very large number)")
    
    # Sum
    iter_sum = sum_to_n_iterative(large_n)
    rec_sum = sum_to_n_recursive(min(large_n, 100))  # Safe recursive call
    
    print(f"   sum_to_n_iterative({large_n}) = {iter_sum}")
    print(f"   sum_to_n_recursive(100) = {rec_sum} (limited for safety)")
    
    print("\n3. WHEN TO USE RECURSION:")
    print("   Use recursion when:")
    print("   - Problem has recursive structure (trees, fractals)")
    print("   - Divide and conquer algorithms")
    print("   - Backtracking problems")
    print("   - Mathematical sequences")
    print("   ")
    print("   Avoid recursion when:")
    print("   - Simple iteration would work")
    print("   - Deep recursion is expected")
    print("   - Performance is critical")
    print("   - Memory usage is a concern")

def interactive_recursion_demo():
    """Demo interaktif konsep rekursi"""
    print("\nDEMO INTERAKTIF REKURSI")
    print("-" * 25)
    
    def recursive_calculator():
        """Interactive recursive calculator"""
        
        def power(base, exp):
            """Calculate base^exp recursively"""
            if exp == 0:
                return 1
            elif exp < 0:
                return 1 / power(base, -exp)
            else:
                return base * power(base, exp - 1)
        
        def gcd(a, b):
            """Greatest Common Divisor using Euclidean algorithm"""
            if b == 0:
                return a
            return gcd(b, a % b)
        
        def binary_search(arr, target, left=0, right=None):
            """Recursive binary search"""
            if right is None:
                right = len(arr) - 1
            
            if left > right:
                return -1
            
            mid = (left + right) // 2
            
            if arr[mid] == target:
                return mid
            elif arr[mid] < target:
                return binary_search(arr, target, mid + 1, right)
            else:
                return binary_search(arr, target, left, mid - 1)
        
        while True:
            print("\nRecursive Calculator:")
            print("1. Power (base^exp)")
            print("2. Greatest Common Divisor")
            print("3. Binary Search")
            print("4. Factorial")
            print("5. Fibonacci")
            print("6. Exit")
            
            choice = input("Choose option (1-6): ").strip()
            
            if choice == "1":
                try:
                    base = float(input("Base: "))
                    exp = int(input("Exponent: "))
                    result = power(base, exp)
                    print(f"Result: {base}^{exp} = {result}")
                except ValueError:
                    print("Please enter valid numbers")
                except RecursionError:
                    print("Exponent too large for recursive calculation")
            
            elif choice == "2":
                try:
                    a = int(input("First number: "))
                    b = int(input("Second number: "))
                    result = gcd(abs(a), abs(b))
                    print(f"GCD({a}, {b}) = {result}")
                except ValueError:
                    print("Please enter valid integers")
            
            elif choice == "3":
                try:
                    arr_input = input("Enter sorted array (comma-separated): ")
                    arr = [int(x.strip()) for x in arr_input.split(",")]
                    target = int(input("Target value: "))
                    
                    result = binary_search(arr, target)
                    if result != -1:
                        print(f"Target {target} found at index {result}")
                    else:
                        print(f"Target {target} not found in array")
                except ValueError:
                    print("Please enter valid numbers")
            
            elif choice == "4":
                try:
                    n = int(input("Number: "))
                    if n < 0:
                        print("Factorial is not defined for negative numbers")
                    elif n > 20:
                        print("Number too large for recursive calculation")
                    else:
                        def factorial(n):
                            if n <= 1:
                                return 1
                            return n * factorial(n - 1)
                        
                        result = factorial(n)
                        print(f"Factorial({n}) = {result}")
                except ValueError:
                    print("Please enter a valid integer")
            
            elif choice == "5":
                try:
                    n = int(input("Position in Fibonacci sequence: "))
                    if n < 0:
                        print("Position must be non-negative")
                    elif n > 30:
                        print("Position too large for recursive calculation")
                    else:
                        @lru_cache(maxsize=None)
                        def fibonacci(n):
                            if n <= 0:
                                return 0
                            elif n == 1:
                                return 1
                            return fibonacci(n - 1) + fibonacci(n - 2)
                        
                        result = fibonacci(n)
                        print(f"Fibonacci({n}) = {result}")
                        
                        # Show sequence up to n
                        if n <= 10:
                            sequence = [fibonacci(i) for i in range(n + 1)]
                            print(f"Sequence: {sequence}")
                        
                        fibonacci.cache_clear()
                except ValueError:
                    print("Please enter a valid integer")
            
            elif choice == "6":
                print("Thank you!")
                break
            
            else:
                print("Invalid choice")
    
    recursive_calculator()

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI REKURSI PYTHON")
    print("=======================")
    print("Program ini mendemonstrasikan konsep rekursi:")
    print("basic recursion, data structures, advanced algorithms, dan optimasi.")
    
    print_separator("BASIC RECURSION")
    demonstrate_basic_recursion()
    
    print_separator("RECURSION WITH DATA STRUCTURES")
    demonstrate_recursion_with_data_structures()
    
    print_separator("ADVANCED RECURSION")
    demonstrate_advanced_recursion()
    
    print_separator("MEMOIZATION")
    demonstrate_memoization()
    
    print_separator("RECURSION LIMITS")
    demonstrate_recursion_limits()
    
    print_separator("DEMO INTERAKTIF")
    interactive_recursion_demo()
    
    print("\n" + "="*60)
    print("RINGKASAN REKURSI:")
    print("- Base case: Kondisi yang menghentikan rekursi")
    print("- Recursive case: Fungsi memanggil dirinya dengan input lebih kecil")
    print("- Memoization: Optimasi dengan menyimpan hasil perhitungan")
    print("- Trade-off: Elegance vs memory usage dan performance")
    print("- Best for: Tree structures, divide & conquer, mathematical sequences")
    print("="*60)

if __name__ == "__main__":
    main()
