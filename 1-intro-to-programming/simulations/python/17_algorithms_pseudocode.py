"""
SIMULASI ALGORITMA DAN PSEUDOCODE
=================================

Algoritma adalah langkah-langkah sistematis untuk menyelesaikan masalah.
Pseudocode adalah cara menulis algoritma dalam bahasa yang mirip dengan
bahasa pemrograman tetapi lebih mudah dipahami manusia.

Konsep penting:
1. ALGORITMA: Urutan langkah logis untuk menyelesaikan masalah
2. PSEUDOCODE: Representasi algoritma dalam bahasa semi-formal
3. FLOWCHART: Representasi visual algoritma
4. PROBLEM SOLVING: Pendekatan sistematis memecahkan masalah
5. COMPLEXITY: Analisis efisiensi algoritma

Algoritma adalah fondasi pemrograman - sebelum menulis kode,
kita harus memahami langkah-langkah penyelesaian masalah.
"""

import time
import random

def bubble_sort_demo(arr):
    """Bubble sort with step-by-step demonstration"""
    arr = arr.copy()
    n = len(arr)
    print(f"   Array awal: {arr}")

    for i in range(n-1):
        swapped = False
        for j in range(n-1-i):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
                swapped = True
        print(f"   Pass {i+1}: {arr}")
        if not swapped:
            break

    return arr

def selection_sort_demo(arr):
    """Selection sort with demonstration"""
    arr = arr.copy()
    n = len(arr)
    print(f"   Array awal: {arr}")

    for i in range(n-1):
        min_idx = i
        for j in range(i+1, n):
            if arr[j] < arr[min_idx]:
                min_idx = j

        if min_idx != i:
            arr[i], arr[min_idx] = arr[min_idx], arr[i]

        print(f"   Step {i+1}: {arr} (min dari index {i} ke {n-1} adalah {arr[i]})")

    return arr

def linear_search_demo(arr, target):
    """Linear search with demonstration"""
    print(f"   Mencari {target} dalam {arr}")

    for i in range(len(arr)):
        print(f"   Step {i+1}: Periksa index {i} = {arr[i]}", end="")
        if arr[i] == target:
            print(f" ✓ FOUND!")
            return i
        else:
            print(f" ✗")

    print("   NOT FOUND")
    return -1

def binary_search_demo(arr, target):
    """Binary search with demonstration"""
    arr = sorted(arr)  # Ensure sorted
    print(f"   Mencari {target} dalam {arr} (sorted)")

    left, right = 0, len(arr) - 1
    step = 1

    while left <= right:
        mid = (left + right) // 2
        print(f"   Step {step}: left={left}, right={right}, mid={mid}")
        print(f"   arr[{mid}] = {arr[mid]}", end="")

        if arr[mid] == target:
            print(f" ✓ FOUND!")
            return mid
        elif arr[mid] < target:
            print(f" < {target}, search right half")
            left = mid + 1
        else:
            print(f" > {target}, search left half")
            right = mid - 1

        step += 1

    print("   NOT FOUND")
    return -1

def print_separator(title):
    """Fungsi untuk mencetak pemisah dengan judul"""
    print("\n" + "="*60)
    print(f" {title} ")
    print("="*60)

def demonstrate_algorithm_basics():
    """Demonstrasi konsep dasar algoritma"""
    print("ALGORITMA adalah urutan langkah-langkah logis untuk menyelesaikan masalah.")
    
    print("\n1. CONTOH ALGORITMA SEHARI-HARI:")
    print("   Membuat teh:")
    print("   1. Siapkan cangkir")
    print("   2. Masukkan kantong teh ke cangkir")
    print("   3. Didihkan air")
    print("   4. Tuang air panas ke cangkir")
    print("   5. Tunggu 3-5 menit")
    print("   6. Angkat kantong teh")
    print("   7. Tambahkan gula/madu jika diinginkan")
    print("   8. Aduk dan sajikan")
    
    print("\n2. KARAKTERISTIK ALGORITMA YANG BAIK:")
    print("   ✓ FINITE: Memiliki langkah yang terbatas")
    print("   ✓ DEFINITE: Setiap langkah jelas dan tidak ambigu")
    print("   ✓ INPUT: Memiliki input yang jelas (bisa nol atau lebih)")
    print("   ✓ OUTPUT: Menghasilkan output yang diinginkan")
    print("   ✓ EFFECTIVE: Setiap langkah dapat dilaksanakan")
    
    print("\n3. ALGORITMA MATEMATIKA SEDERHANA:")
    print("   Mencari bilangan terbesar dari 3 bilangan:")
    print("   ")
    print("   INPUT: a, b, c")
    print("   1. SET max = a")
    print("   2. IF b > max THEN max = b")
    print("   3. IF c > max THEN max = c")
    print("   4. OUTPUT max")
    
    # Implementasi
    a, b, c = 15, 23, 8
    print(f"\n   Contoh: a={a}, b={b}, c={c}")
    
    max_val = a
    print(f"   Step 1: max = {max_val}")
    
    if b > max_val:
        max_val = b
        print(f"   Step 2: b > max, jadi max = {max_val}")
    else:
        print(f"   Step 2: b <= max, max tetap = {max_val}")
    
    if c > max_val:
        max_val = c
        print(f"   Step 3: c > max, jadi max = {max_val}")
    else:
        print(f"   Step 3: c <= max, max tetap = {max_val}")
    
    print(f"   Output: Bilangan terbesar = {max_val}")

def demonstrate_pseudocode():
    """Demonstrasi pseudocode"""
    print("PSEUDOCODE adalah cara menulis algoritma dalam bahasa semi-formal")
    print("yang mudah dipahami dan dapat diterjemahkan ke bahasa pemrograman.")
    
    print("\n1. KONVENSI PSEUDOCODE:")
    print("   • BEGIN/END: Menandai awal dan akhir")
    print("   • INPUT/OUTPUT: Operasi input dan output")
    print("   • SET/ASSIGN: Pemberian nilai variabel")
    print("   • IF-THEN-ELSE: Percabangan")
    print("   • WHILE/FOR: Perulangan")
    print("   • FUNCTION/PROCEDURE: Definisi fungsi")
    
    print("\n2. CONTOH PSEUDOCODE - MENGHITUNG FAKTORIAL:")
    print("   ")
    print("   ALGORITHM Factorial")
    print("   BEGIN")
    print("       INPUT n")
    print("       IF n < 0 THEN")
    print("           OUTPUT 'Error: negative number'")
    print("       ELSE IF n = 0 OR n = 1 THEN")
    print("           OUTPUT 1")
    print("       ELSE")
    print("           SET result = 1")
    print("           FOR i = 2 TO n DO")
    print("               SET result = result * i")
    print("           END FOR")
    print("           OUTPUT result")
    print("       END IF")
    print("   END")
    
    print("\n3. IMPLEMENTASI PYTHON:")
    print("   def factorial(n):")
    print("       if n < 0:")
    print("           return 'Error: negative number'")
    print("       elif n == 0 or n == 1:")
    print("           return 1")
    print("       else:")
    print("           result = 1")
    print("           for i in range(2, n + 1):")
    print("               result = result * i")
    print("           return result")
    
    def factorial(n):
        if n < 0:
            return 'Error: negative number'
        elif n == 0 or n == 1:
            return 1
        else:
            result = 1
            for i in range(2, n + 1):
                result = result * i
            return result
    
    print("\n4. TESTING:")
    test_values = [0, 1, 5, 7, -3]
    for val in test_values:
        result = factorial(val)
        print(f"   factorial({val}) = {result}")

def demonstrate_problem_solving_approach():
    """Demonstrasi pendekatan pemecahan masalah"""
    print("PENDEKATAN SISTEMATIS untuk memecahkan masalah pemrograman.")
    
    print("\n1. LANGKAH-LANGKAH PROBLEM SOLVING:")
    print("   1️⃣  UNDERSTAND: Pahami masalah dengan jelas")
    print("   2️⃣  ANALYZE: Analisis input, output, dan constraints")
    print("   3️⃣  DESIGN: Rancang algoritma (pseudocode/flowchart)")
    print("   4️⃣  IMPLEMENT: Tulis kode program")
    print("   5️⃣  TEST: Uji dengan berbagai test case")
    print("   6️⃣  DEBUG: Perbaiki jika ada error")
    print("   7️⃣  OPTIMIZE: Tingkatkan efisiensi jika perlu")
    
    print("\n2. CONTOH MASALAH: MENCARI BILANGAN PRIMA")
    print("   ")
    print("   📋 UNDERSTAND:")
    print("   - Bilangan prima: bilangan > 1 yang hanya habis dibagi 1 dan dirinya")
    print("   - Input: sebuah bilangan bulat")
    print("   - Output: True jika prima, False jika bukan")
    
    print("\n   🔍 ANALYZE:")
    print("   - Input: integer n")
    print("   - Constraints: n >= 1")
    print("   - Edge cases: n = 1, n = 2, bilangan negatif")
    
    print("\n   📝 DESIGN (Pseudocode):")
    print("   ALGORITHM IsPrime")
    print("   BEGIN")
    print("       INPUT n")
    print("       IF n <= 1 THEN")
    print("           OUTPUT False")
    print("       ELSE IF n = 2 THEN")
    print("           OUTPUT True")
    print("       ELSE IF n MOD 2 = 0 THEN")
    print("           OUTPUT False")
    print("       ELSE")
    print("           FOR i = 3 TO sqrt(n) STEP 2 DO")
    print("               IF n MOD i = 0 THEN")
    print("                   OUTPUT False")
    print("               END IF")
    print("           END FOR")
    print("           OUTPUT True")
    print("       END IF")
    print("   END")
    
    print("\n   💻 IMPLEMENT:")
    def is_prime(n):
        """Check if a number is prime"""
        if n <= 1:
            return False
        elif n == 2:
            return True
        elif n % 2 == 0:
            return False
        else:
            for i in range(3, int(n**0.5) + 1, 2):
                if n % i == 0:
                    return False
            return True
    
    print("   def is_prime(n):")
    print("       if n <= 1: return False")
    print("       elif n == 2: return True")
    print("       elif n % 2 == 0: return False")
    print("       else:")
    print("           for i in range(3, int(n**0.5) + 1, 2):")
    print("               if n % i == 0: return False")
    print("           return True")
    
    print("\n   🧪 TEST:")
    test_cases = [1, 2, 3, 4, 5, 17, 25, 29, 100, 101]
    for num in test_cases:
        result = is_prime(num)
        print(f"   is_prime({num}) = {result}")

def demonstrate_sorting_algorithms():
    """Demonstrasi algoritma sorting"""
    print("ALGORITMA SORTING untuk mengurutkan data.")
    
    print("\n1. BUBBLE SORT:")
    print("   Konsep: Bandingkan elemen bersebelahan, tukar jika tidak urut")
    print("   ")
    print("   PSEUDOCODE:")
    print("   FOR i = 0 TO n-2 DO")
    print("       FOR j = 0 TO n-2-i DO")
    print("           IF arr[j] > arr[j+1] THEN")
    print("               SWAP arr[j] AND arr[j+1]")
    print("           END IF")
    print("       END FOR")
    print("   END FOR")
    

    
    print("\n   Demonstrasi:")
    test_array = [64, 34, 25, 12, 22]
    sorted_array = bubble_sort_demo(test_array)
    print(f"   Hasil akhir: {sorted_array}")
    
    print("\n2. SELECTION SORT:")
    print("   Konsep: Cari elemen terkecil, tukar dengan elemen pertama")
    print("   ")
    print("   PSEUDOCODE:")
    print("   FOR i = 0 TO n-2 DO")
    print("       SET min_idx = i")
    print("       FOR j = i+1 TO n-1 DO")
    print("           IF arr[j] < arr[min_idx] THEN")
    print("               SET min_idx = j")
    print("           END IF")
    print("       END FOR")
    print("       SWAP arr[i] AND arr[min_idx]")
    print("   END FOR")
    

    
    print("\n   Demonstrasi:")
    test_array = [64, 25, 12, 22, 11]
    sorted_array = selection_sort_demo(test_array)

def demonstrate_search_algorithms():
    """Demonstrasi algoritma pencarian"""
    print("ALGORITMA SEARCH untuk mencari data.")
    
    print("\n1. LINEAR SEARCH:")
    print("   Konsep: Periksa setiap elemen satu per satu")
    print("   ")
    print("   PSEUDOCODE:")
    print("   FOR i = 0 TO n-1 DO")
    print("       IF arr[i] = target THEN")
    print("           OUTPUT i")
    print("       END IF")
    print("   END FOR")
    print("   OUTPUT -1  // not found")
    

    
    print("\n   Demonstrasi:")
    test_array = [64, 25, 12, 22, 11]
    target = 22
    result = linear_search_demo(test_array, target)
    
    print("\n2. BINARY SEARCH:")
    print("   Konsep: Bagi array menjadi dua, bandingkan dengan middle element")
    print("   Syarat: Array harus sudah terurut")
    print("   ")
    print("   PSEUDOCODE:")
    print("   SET left = 0, right = n-1")
    print("   WHILE left <= right DO")
    print("       SET mid = (left + right) / 2")
    print("       IF arr[mid] = target THEN")
    print("           OUTPUT mid")
    print("       ELSE IF arr[mid] < target THEN")
    print("           SET left = mid + 1")
    print("       ELSE")
    print("           SET right = mid - 1")
    print("       END IF")
    print("   END WHILE")
    print("   OUTPUT -1  // not found")
    

    
    print("\n   Demonstrasi:")
    test_array = [64, 25, 12, 22, 11]
    target = 22
    result = binary_search_demo(test_array, target)

def demonstrate_algorithm_complexity():
    """Demonstrasi kompleksitas algoritma"""
    print("KOMPLEKSITAS ALGORITMA mengukur efisiensi dalam hal waktu dan ruang.")
    
    print("\n1. BIG O NOTATION:")
    print("   O(1)     - Constant time")
    print("   O(log n) - Logarithmic time")
    print("   O(n)     - Linear time")
    print("   O(n²)    - Quadratic time")
    print("   O(2ⁿ)    - Exponential time")
    
    print("\n2. PERBANDINGAN KOMPLEKSITAS:")
    print("   Algorithm        | Best    | Average | Worst   | Space")
    print("   -----------------|---------|---------|---------|-------")
    print("   Linear Search    | O(1)    | O(n)    | O(n)    | O(1)")
    print("   Binary Search    | O(1)    | O(log n)| O(log n)| O(1)")
    print("   Bubble Sort      | O(n)    | O(n²)   | O(n²)   | O(1)")
    print("   Selection Sort   | O(n²)   | O(n²)   | O(n²)   | O(1)")
    print("   Quick Sort       | O(n log n)| O(n log n)| O(n²) | O(log n)")
    
    print("\n3. PERFORMANCE TESTING:")
    
    def time_algorithm(func, data, *args):
        """Measure algorithm execution time"""
        start_time = time.time()
        result = func(data, *args)
        end_time = time.time()
        return result, (end_time - start_time) * 1000  # in milliseconds
    
    def linear_search(arr, target):
        for i, val in enumerate(arr):
            if val == target:
                return i
        return -1
    
    def binary_search(arr, target):
        left, right = 0, len(arr) - 1
        while left <= right:
            mid = (left + right) // 2
            if arr[mid] == target:
                return mid
            elif arr[mid] < target:
                left = mid + 1
            else:
                right = mid - 1
        return -1
    
    # Test with different sizes
    sizes = [100, 1000, 10000]
    
    for size in sizes:
        data = list(range(size))
        target = size - 1  # Worst case - last element
        
        linear_result, linear_time = time_algorithm(linear_search, data, target)
        binary_result, binary_time = time_algorithm(binary_search, data, target)
        
        print(f"   Size {size:5d}: Linear={linear_time:6.2f}ms, Binary={binary_time:6.2f}ms")

def interactive_algorithm_lab():
    """Lab interaktif untuk algoritma"""
    print("\nLAB INTERAKTIF ALGORITMA")
    print("-" * 30)
    
    while True:
        print("\nPilih algoritma untuk dicoba:")
        print("1. Sorting algorithms")
        print("2. Search algorithms")
        print("3. Mathematical algorithms")
        print("4. Algorithm complexity demo")
        print("5. Keluar")
        
        choice = input("Pilihan (1-5): ").strip()
        
        if choice == "1":
            print("\n=== SORTING ALGORITHMS ===")
            try:
                data_input = input("Masukkan angka (pisahkan dengan koma): ")
                data = [int(x.strip()) for x in data_input.split(",")]
                
                print("Pilih algoritma:")
                print("1. Bubble Sort")
                print("2. Selection Sort")
                
                sort_choice = input("Pilihan (1-2): ").strip()
                
                if sort_choice == "1":
                    print("\nBubble Sort:")
                    bubble_sort_demo(data)
                elif sort_choice == "2":
                    print("\nSelection Sort:")
                    selection_sort_demo(data)
                
            except ValueError:
                print("Error: Masukkan angka yang valid!")
        
        elif choice == "2":
            print("\n=== SEARCH ALGORITHMS ===")
            try:
                data_input = input("Masukkan angka (pisahkan dengan koma): ")
                data = [int(x.strip()) for x in data_input.split(",")]
                target = int(input("Angka yang dicari: "))
                
                print("Pilih algoritma:")
                print("1. Linear Search")
                print("2. Binary Search")
                
                search_choice = input("Pilihan (1-2): ").strip()
                
                if search_choice == "1":
                    print("\nLinear Search:")
                    linear_search_demo(data, target)
                elif search_choice == "2":
                    print("\nBinary Search:")
                    binary_search_demo(data, target)
                
            except ValueError:
                print("Error: Masukkan angka yang valid!")
        
        elif choice == "3":
            print("\n=== MATHEMATICAL ALGORITHMS ===")
            try:
                n = int(input("Masukkan angka: "))
                
                print("Pilih operasi:")
                print("1. Factorial")
                print("2. Prime check")
                print("3. Fibonacci")
                
                math_choice = input("Pilihan (1-3): ").strip()
                
                if math_choice == "1":
                    def factorial(n):
                        if n <= 1:
                            return 1
                        result = 1
                        for i in range(2, n + 1):
                            result *= i
                        return result
                    
                    result = factorial(n)
                    print(f"Factorial({n}) = {result}")
                
                elif math_choice == "2":
                    def is_prime(n):
                        if n <= 1:
                            return False
                        for i in range(2, int(n**0.5) + 1):
                            if n % i == 0:
                                return False
                        return True
                    
                    result = is_prime(n)
                    print(f"{n} adalah {'prima' if result else 'bukan prima'}")
                
                elif math_choice == "3":
                    def fibonacci(n):
                        if n <= 1:
                            return n
                        a, b = 0, 1
                        for _ in range(2, n + 1):
                            a, b = b, a + b
                        return b
                    
                    result = fibonacci(n)
                    print(f"Fibonacci({n}) = {result}")
                
            except ValueError:
                print("Error: Masukkan angka yang valid!")
        
        elif choice == "4":
            print("\n=== ALGORITHM COMPLEXITY DEMO ===")
            demonstrate_algorithm_complexity()
        
        elif choice == "5":
            print("Terima kasih!")
            break
        
        else:
            print("Pilihan tidak valid!")

def main():
    """Fungsi utama untuk menjalankan semua simulasi"""
    print("SIMULASI ALGORITMA DAN PSEUDOCODE")
    print("=================================")
    print("Belajar memecahkan masalah dengan pendekatan sistematis")
    
    print_separator("KONSEP DASAR ALGORITMA")
    demonstrate_algorithm_basics()
    
    print_separator("PSEUDOCODE")
    demonstrate_pseudocode()
    
    print_separator("PROBLEM SOLVING APPROACH")
    demonstrate_problem_solving_approach()
    
    print_separator("SORTING ALGORITHMS")
    demonstrate_sorting_algorithms()
    
    print_separator("SEARCH ALGORITHMS")
    demonstrate_search_algorithms()
    
    print_separator("ALGORITHM COMPLEXITY")
    demonstrate_algorithm_complexity()
    
    print_separator("LAB INTERAKTIF")
    interactive_algorithm_lab()
    
    print("\n" + "="*60)
    print("RINGKASAN:")
    print("- Algoritma: Langkah sistematis menyelesaikan masalah")
    print("- Pseudocode: Representasi algoritma dalam bahasa semi-formal")
    print("- Problem solving: Understand → Analyze → Design → Implement → Test")
    print("- Complexity: Analisis efisiensi algoritma (Big O notation)")
    print("- Practice: Semakin banyak berlatih, semakin mahir!")
    print("="*60)

if __name__ == "__main__":
    main()
