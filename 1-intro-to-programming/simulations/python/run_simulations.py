#!/usr/bin/env python3
"""
PYTHON PROGRAMMING SIMULATIONS RUNNER
=====================================

Program ini dibuat untuk kelas Mini Computer Science oleh minics.id
Menyediakan menu interaktif untuk menjalankan semua simulasi pemrograman
dalam kursus Introduction to Programming. Setiap simulasi mendemonstrasikan
konsep pemrograman kunci dengan contoh hands-on dan demo interaktif.

Created for Mini Computer Science class by minics.id
Website: https://minics.id
Course: Introduction to Programming with Python

Usage:
    python run_simulations.py

Features:
- Interactive menu system
- Course progress tracking
- Simulation descriptions
- Easy navigation between topics
- Educational guidance
- Beginner-friendly explanations in Indonesian
"""

import os
import sys
import subprocess
import time

def print_header():
    """Print the main header with Mini Computer Science branding"""
    print("=" * 70)
    print("🐍 PYTHON PROGRAMMING SIMULATIONS")
    print("📚 Introduction to Programming Course")
    print("🎓 Mini Computer Science Class")
    print("🌐 Created by minics.id")
    print("=" * 70)
    print()

def print_separator():
    """Print a section separator"""
    print("-" * 50)

def get_simulations():
    """Get list of all simulation files with descriptions"""
    simulations = [
        {
            "file": "00_hello_world.py",
            "title": "Hello World & Basic Output",
            "description": "First program, print statements, comments, basic syntax",
            "category": "Basics",
            "difficulty": "Beginner"
        },
        {
            "file": "01_number_systems.py",
            "title": "Number Systems",
            "description": "Binary, decimal, hexadecimal, octal number systems",
            "category": "Basics",
            "difficulty": "Beginner"
        },
        {
            "file": "02_syntax_semantics.py",
            "title": "Syntax & Semantics",
            "description": "Language rules, syntax vs semantics, code structure",
            "category": "Basics",
            "difficulty": "Beginner"
        },
        {
            "file": "03_data_types.py",
            "title": "Data Types",
            "description": "Primitive and composite data types, type conversion",
            "category": "Basics",
            "difficulty": "Beginner"
        },
        {
            "file": "04_variables_constants.py",
            "title": "Variables & Constants",
            "description": "Variable declaration, scope, naming conventions, constants",
            "category": "Basics",
            "difficulty": "Beginner"
        },
        {
            "file": "05_operators_expressions.py",
            "title": "Operators & Expressions",
            "description": "Arithmetic, logical, comparison operators, precedence",
            "category": "Basics",
            "difficulty": "Beginner"
        },
        {
            "file": "06_control_structures.py",
            "title": "Control Structures",
            "description": "if/else statements, loops, break/continue, match-case",
            "category": "Control Flow",
            "difficulty": "Intermediate"
        },
        {
            "file": "07_char_string_slicing.py",
            "title": "Characters & String Operations",
            "description": "Character manipulation, string methods, slicing, formatting",
            "category": "Control Flow",
            "difficulty": "Intermediate"
        },
        {
            "file": "08_io_operations.py",
            "title": "Input/Output Operations",
            "description": "Console I/O, file operations, JSON, CSV, serialization",
            "category": "I/O & Errors",
            "difficulty": "Intermediate"
        },
        {
            "file": "09_exception_handling.py",
            "title": "Exception Handling",
            "description": "Try/catch blocks, error types, logging, debugging",
            "category": "I/O & Errors",
            "difficulty": "Intermediate"
        },
        {
            "file": "10_linear_data_structures.py",
            "title": "Linear Data Structures",
            "description": "Lists, stacks, queues, deques, performance comparison",
            "category": "Data Structures",
            "difficulty": "Intermediate"
        },
        {
            "file": "11_hash_data_structures.py",
            "title": "Hash-Based Data Structures",
            "description": "Sets, dictionaries, hash tables, Counter, defaultdict",
            "category": "Data Structures",
            "difficulty": "Intermediate"
        },
        {
            "file": "12_classes_oop.py",
            "title": "Classes & Object-Oriented Programming",
            "description": "Classes, objects, encapsulation, methods, composition",
            "category": "OOP",
            "difficulty": "Advanced"
        },
        {
            "file": "13_inheritance.py",
            "title": "Inheritance & Polymorphism",
            "description": "Inheritance, method overriding, multiple inheritance, abstract classes",
            "category": "OOP",
            "difficulty": "Advanced"
        },
        {
            "file": "14_functional_programming.py",
            "title": "Functional Programming",
            "description": "Functions, lambdas, map/filter/reduce, closures, decorators",
            "category": "Advanced",
            "difficulty": "Advanced"
        },
        {
            "file": "15_recursion.py",
            "title": "Recursion",
            "description": "Recursive algorithms, memoization, optimization, limits",
            "category": "Advanced",
            "difficulty": "Advanced"
        },
        {
            "file": "16_algorithms_pseudocode.py",
            "title": "Algorithms & Pseudocode",
            "description": "Problem-solving approach, algorithm design, pseudocode",
            "category": "Advanced",
            "difficulty": "Advanced"
        }
    ]
    return simulations

def display_menu(simulations):
    """Display the main menu"""
    print_header()
    
    # Group by category
    categories = {}
    for sim in simulations:
        category = sim["category"]
        if category not in categories:
            categories[category] = []
        categories[category].append(sim)
    
    print("📋 AVAILABLE SIMULATIONS:")
    print()
    
    sim_number = 1
    for category, sims in categories.items():
        print(f"🔹 {category.upper()}")
        for sim in sims:
            difficulty_icon = {"Beginner": "🟢", "Intermediate": "🟡", "Advanced": "🔴"}
            icon = difficulty_icon.get(sim["difficulty"], "⚪")
            
            print(f"   {sim_number:2d}. {icon} {sim['title']}")
            print(f"       {sim['description']}")
            sim_number += 1
        print()
    
    print("🔧 ADDITIONAL OPTIONS:")
    print(f"   {sim_number}. 📖 View Course Information")
    print(f"   {sim_number + 1}. 🎯 Learning Path Guidance")
    print(f"   {sim_number + 2}. ❌ Exit")
    print()

def run_simulation(simulation):
    """Run a specific simulation"""
    filename = simulation["file"]
    
    if not os.path.exists(filename):
        print(f"❌ Error: File '{filename}' not found!")
        return False
    
    print(f"🚀 Running: {simulation['title']}")
    print(f"📝 Description: {simulation['description']}")
    print(f"📊 Difficulty: {simulation['difficulty']}")
    print()
    print("Press Enter to start the simulation...")
    input()
    
    try:
        # Run the simulation
        result = subprocess.run([sys.executable, filename], 
                              capture_output=False, 
                              text=True)
        
        print()
        print_separator()
        if result.returncode == 0:
            print("✅ Simulation completed successfully!")
        else:
            print("⚠️  Simulation ended with errors.")
        
        print("Press Enter to return to menu...")
        input()
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️  Simulation interrupted by user.")
        print("Press Enter to return to menu...")
        input()
        return True
        
    except Exception as e:
        print(f"\n❌ Error running simulation: {e}")
        print("Press Enter to return to menu...")
        input()
        return False

def show_course_info():
    """Display course information"""
    print_header()
    print("📚 COURSE INFORMATION")
    print_separator()
    print()
    print("🎓 TENTANG MINI COMPUTER SCIENCE:")
    print("• Kelas pemrograman untuk pemula yang ingin belajar Computer Science")
    print("• Dibuat oleh minics.id - platform edukasi teknologi Indonesia")
    print("• Fokus pada pembelajaran praktis dengan simulasi interaktif")
    print("• Menggunakan bahasa Indonesia untuk penjelasan yang mudah dipahami")
    print("• Website: https://minics.id")
    print()
    print("🎯 LEARNING OBJECTIVES:")
    print("• Master fundamental programming concepts")
    print("• Understand data structures and algorithms")
    print("• Learn object-oriented programming principles")
    print("• Practice functional programming techniques")
    print("• Develop problem-solving skills")
    print()
    print("📖 COURSE STRUCTURE:")
    print("• 17 comprehensive simulations")
    print("• Progressive difficulty from beginner to advanced")
    print("• Interactive demos and hands-on examples")
    print("• Real-world applications and scenarios")
    print("• Performance analysis and best practices")
    print("• Penjelasan dalam Bahasa Indonesia")
    print()
    print("⏱️  ESTIMATED TIME:")
    print("• Each simulation: 30-60 minutes")
    print("• Complete course: 10-15 hours")
    print("• Self-paced learning supported")
    print()
    print("🔧 REQUIREMENTS:")
    print("• Python 3.6 or higher")
    print("• Basic computer literacy")
    print("• No prior programming experience needed")
    print()
    print("📞 CONTACT & SUPPORT:")
    print("• Website: https://minics.id")
    print("• Email: <EMAIL>")
    print("• Follow us for more programming courses!")
    print()
    print("Press Enter to return to menu...")
    input()

def show_learning_path():
    """Display learning path guidance"""
    print_header()
    print("🎯 LEARNING PATH GUIDANCE")
    print_separator()
    print()
    print("📈 RECOMMENDED SEQUENCE:")
    print()
    
    paths = [
        {
            "phase": "Phase 1: Foundation",
            "simulations": [1, 2, 3, 4, 5, 6],
            "description": "Build fundamental programming knowledge",
            "time": "4-5 hours"
        },
        {
            "phase": "Phase 2: Control & I/O",
            "simulations": [7, 8, 9, 10],
            "description": "Master program flow and data handling",
            "time": "3-4 hours"
        },
        {
            "phase": "Phase 3: Data Structures",
            "simulations": [11, 12],
            "description": "Learn efficient data organization",
            "time": "2-3 hours"
        },
        {
            "phase": "Phase 4: Object-Oriented",
            "simulations": [13, 14],
            "description": "Understand OOP principles and design",
            "time": "2-3 hours"
        },
        {
            "phase": "Phase 5: Advanced Topics",
            "simulations": [15, 16, 17],
            "description": "Explore functional programming, recursion, and algorithms",
            "time": "3-4 hours"
        }
    ]
    
    for i, path in enumerate(paths, 1):
        print(f"🔹 {path['phase']}")
        print(f"   Simulations: {', '.join(map(str, path['simulations']))}")
        print(f"   Focus: {path['description']}")
        print(f"   Time: {path['time']}")
        print()
    
    print("💡 LEARNING TIPS:")
    print("• Complete simulations in order for best understanding")
    print("• Take breaks between phases to absorb concepts")
    print("• Practice with the interactive demos")
    print("• Review previous concepts if needed")
    print("• Don't rush - understanding is more important than speed")
    print()
    print("🎓 ASSESSMENT:")
    print("• Try to explain concepts in your own words")
    print("• Modify example code to test understanding")
    print("• Create small projects using learned concepts")
    print("• Teach concepts to others (best way to learn!)")
    print()
    print("Press Enter to return to menu...")
    input()

def main():
    """Main program loop"""
    simulations = get_simulations()
    
    while True:
        # Clear screen (works on most terminals)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        display_menu(simulations)
        
        try:
            choice = input("Enter your choice: ").strip()
            
            if not choice:
                continue
                
            choice_num = int(choice)
            
            # Check if it's a simulation
            if 1 <= choice_num <= len(simulations):
                simulation = simulations[choice_num - 1]
                run_simulation(simulation)
                
            # Additional options
            elif choice_num == len(simulations) + 1:
                show_course_info()
                
            elif choice_num == len(simulations) + 2:
                show_learning_path()
                
            elif choice_num == len(simulations) + 3:
                print("\n👋 Thank you for using Python Programming Simulations!")
                print("🎓 Mini Computer Science Class by minics.id")
                print("🌐 Visit https://minics.id for more courses!")
                print("💻 Happy learning and coding!")
                break
                
            else:
                print(f"❌ Invalid choice: {choice_num}")
                print("Please enter a number from the menu.")
                time.sleep(2)
                
        except ValueError:
            print("❌ Please enter a valid number.")
            time.sleep(2)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye from Mini Computer Science!")
            print("🌐 Visit minics.id for more courses!")
            break

if __name__ == "__main__":
    main()
