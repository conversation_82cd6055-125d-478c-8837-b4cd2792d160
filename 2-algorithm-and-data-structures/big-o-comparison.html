<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perbandingan Big O Notation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .container {
            max-width: 900px;
            width: 100%;
            margin: 20px auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .visualization-area {
            display: flex;
            justify-content: center;
            position: relative;
            margin: 30px 0;
            min-height: 400px;
            border: 1px dashed #ccc;
            padding: 15px;
            background-color: #fafafa;
            border-radius: 4px;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .explanation {
            margin-top: 20px;
        }
        .big-o-item {
            margin-bottom: 15px;
            padding: 10px;
            border-left: 4px solid;
            background-color: #f9f9f9;
        }
        .big-o-item h3 {
            margin-top: 0;
        }
        .big-o-item.o1 { border-color: #4285f4; }
        .big-o-item.ologn { border-color: #34a853; }
        .big-o-item.on { border-color: #fbbc05; }
        .big-o-item.onlogn { border-color: #ea4335; }
        .big-o-item.on2 { border-color: #9c27b0; }
        .big-o-item.o2n { border-color: #ff5722; }

        .legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 10px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 5px;
            border-radius: 3px;
        }
        .slider-container {
            width: 80%;
            margin: 20px auto;
            text-align: center;
        }
        #n-slider {
            width: 100%;
            margin-bottom: 10px;
        }
        #n-value {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Perbandingan Laju Pertumbuhan Big O Notation</h1>

    <div class="container">
        <div class="slider-container">
            <label for="n-slider">Nilai N: <span id="n-value">50</span></label>
            <input type="range" id="n-slider" min="1" max="100" value="50">
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #4285f4;"></div>
                <span>O(1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #34a853;"></div>
                <span>O(log n)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fbbc05;"></div>
                <span>O(n)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ea4335;"></div>
                <span>O(n log n)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #9c27b0;"></div>
                <span>O(n²)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ff5722;"></div>
                <span>O(2ⁿ)</span>
            </div>
        </div>

        <div class="visualization-area">
            <canvas id="big-o-chart" width="800" height="400"></canvas>
        </div>

        <div class="explanation">
            <h2>Penjelasan Big O Notation</h2>

            <div class="big-o-item o1">
                <h3>O(1) - Constant Time</h3>
                <p>Waktu eksekusi tidak bergantung pada ukuran input N. Sangat cepat.</p>
                <p><strong>Contoh:</strong> Akses elemen array berdasarkan indeks (my_array[5]), push/pop pada Stack (implementasi array), lookup di Hash Map (average).</p>
            </div>

            <div class="big-o-item ologn">
                <h3>O(log n) - Logarithmic Time</h3>
                <p>Waktu eksekusi tumbuh sangat lambat seiring N bertambah. Biasanya terjadi ketika algoritma membagi masalah menjadi dua pada setiap langkah. Sangat efisien.</p>
                <p><strong>Contoh:</strong> Binary Search pada data terurut, operasi pada Binary Search Tree (balanced).</p>
            </div>

            <div class="big-o-item on">
                <h3>O(n) - Linear Time</h3>
                <p>Waktu eksekusi tumbuh secara linear seiring N bertambah. Cukup efisien untuk banyak kasus.</p>
                <p><strong>Contoh:</strong> Linear Search, traversal Linked List, mencari nilai min/max dalam array tak terurut.</p>
            </div>

            <div class="big-o-item onlogn">
                <h3>O(n log n) - Log-Linear Time</h3>
                <p>Waktu eksekusi tumbuh sedikit lebih cepat dari linear. Batas efisiensi umum untuk algoritma pengurutan berbasis perbandingan.</p>
                <p><strong>Contoh:</strong> Merge Sort, Quick Sort (average case), Heap Sort.</p>
            </div>

            <div class="big-o-item on2">
                <h3>O(n²) - Quadratic Time</h3>
                <p>Waktu eksekusi tumbuh secara kuadratik. Mulai terasa lambat untuk N yang cukup besar (misal, N > 10,000).</p>
                <p><strong>Contoh:</strong> Bubble Sort, Insertion Sort, Selection Sort, nested loop sederhana yang memproses semua pasangan elemen.</p>
            </div>

            <div class="big-o-item o2n">
                <h3>O(2ⁿ) - Exponential Time</h3>
                <p>Waktu eksekusi tumbuh sangat cepat. Hanya praktis untuk N yang sangat kecil.</p>
                <p><strong>Contoh:</strong> Beberapa algoritma rekursif brute-force (misal, mencari semua subset).</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('big-o-chart');
            const ctx = canvas.getContext('2d');
            const slider = document.getElementById('n-slider');
            const nValueDisplay = document.getElementById('n-value');

            // Set initial N value
            let maxN = parseInt(slider.value);
            nValueDisplay.textContent = maxN;

            // Colors for different Big O notations
            const colors = {
                o1: '#4285f4',      // Blue
                ologn: '#34a853',   // Green
                on: '#fbbc05',      // Yellow
                onlogn: '#ea4335',  // Red
                on2: '#9c27b0',     // Purple
                o2n: '#ff5722'      // Orange
            };

            // Function to calculate values for different Big O notations
            function calculateBigOValues(n) {
                return {
                    o1: Array(n).fill(1),
                    ologn: Array.from({length: n}, (_, i) => Math.log2(i + 1)),
                    on: Array.from({length: n}, (_, i) => i + 1),
                    onlogn: Array.from({length: n}, (_, i) => (i + 1) * Math.log2(i + 1)),
                    on2: Array.from({length: n}, (_, i) => (i + 1) * (i + 1)),
                    o2n: Array.from({length: n}, (_, i) => Math.pow(2, i + 1))
                };
            }

            // Function to draw the graph
            function drawGraph() {
                // Clear canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Calculate values
                const values = calculateBigOValues(maxN);

                // Find maximum value for scaling (excluding O(2^n) which grows too fast)
                let maxValue = Math.max(
                    ...values.o1,
                    ...values.ologn,
                    ...values.on,
                    ...values.onlogn,
                    ...values.on2
                );

                // Include O(2^n) but cap it to avoid scaling issues
                const maxExponential = Math.min(maxValue * 2, Math.max(...values.o2n.slice(0, 10)));
                maxValue = Math.max(maxValue, maxExponential);

                // Padding
                const paddingLeft = 100; // More space for y-axis labels
                const paddingRight = 40;
                const paddingTop = 40;
                const paddingBottom = 40;
                const graphWidth = canvas.width - paddingLeft - paddingRight;
                const graphHeight = canvas.height - paddingTop - paddingBottom;

                // Draw axes
                ctx.beginPath();
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;

                // X-axis
                ctx.moveTo(paddingLeft, canvas.height - paddingBottom);
                ctx.lineTo(canvas.width - paddingRight, canvas.height - paddingBottom);

                // Y-axis
                ctx.moveTo(paddingLeft, canvas.height - paddingBottom);
                ctx.lineTo(paddingLeft, paddingTop);

                ctx.stroke();

                // Draw axis labels
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';

                // X-axis label
                ctx.fillText('n (ukuran input)', paddingLeft + graphWidth / 2, canvas.height - 5);

                // Y-axis label
                ctx.save();
                ctx.translate(50, canvas.height / 2);
                ctx.rotate(-Math.PI / 2);
                ctx.textAlign = 'center';
                ctx.fillText('Jumlah Operasi', 0, 0);
                ctx.restore();

                // Draw grid lines and labels
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 1;

                // X-axis grid and labels
                const xStep = graphWidth / 10;
                for (let i = 0; i <= 10; i++) {
                    const x = paddingLeft + i * xStep;

                    // Grid line
                    ctx.beginPath();
                    ctx.moveTo(x, canvas.height - paddingBottom);
                    ctx.lineTo(x, paddingTop);
                    ctx.stroke();

                    // Label
                    const value = Math.round(i * maxN / 10);
                    ctx.fillText(value.toString(), x, canvas.height - paddingBottom + 15);
                }

                // Y-axis grid and labels
                const yStep = graphHeight / 10;
                for (let i = 0; i <= 10; i++) {
                    const y = canvas.height - paddingBottom - i * yStep;

                    // Grid line
                    ctx.beginPath();
                    ctx.moveTo(paddingLeft, y);
                    ctx.lineTo(canvas.width - paddingRight, y);
                    ctx.stroke();

                    // Label
                    const value = Math.round(i * maxValue / 10);
                    ctx.textAlign = 'right';
                    // Format large numbers with commas for better readability
                    const formattedValue = value.toLocaleString();
                    ctx.fillText(formattedValue, paddingLeft - 15, y + 5);
                    ctx.textAlign = 'center';
                }

                // Function to draw a line for a specific Big O notation
                function drawLine(values, color) {
                    ctx.beginPath();
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 3;

                    // Start from the origin
                    ctx.moveTo(paddingLeft, canvas.height - paddingBottom);

                    // Draw the line
                    for (let i = 0; i < values.length; i++) {
                        // Cap extremely large values
                        const value = Math.min(values[i], maxValue);

                        const x = paddingLeft + (i / maxN) * graphWidth;
                        const y = canvas.height - paddingBottom - (value / maxValue) * graphHeight;

                        ctx.lineTo(x, y);
                    }

                    ctx.stroke();
                }

                // Draw lines for each Big O notation
                drawLine(values.o1, colors.o1);
                drawLine(values.ologn, colors.ologn);
                drawLine(values.on, colors.on);
                drawLine(values.onlogn, colors.onlogn);
                drawLine(values.on2, colors.on2);

                // Draw O(2^n) separately with a cap to avoid scaling issues
                ctx.beginPath();
                ctx.strokeStyle = colors.o2n;
                ctx.lineWidth = 3;

                ctx.moveTo(paddingLeft, canvas.height - paddingBottom);

                for (let i = 0; i < values.o2n.length; i++) {
                    // Cap extremely large values
                    const value = Math.min(values.o2n[i], maxValue);

                    const x = paddingLeft + (i / maxN) * graphWidth;
                    const y = canvas.height - paddingBottom - (value / maxValue) * graphHeight;

                    // Stop drawing if we reach the top of the graph
                    if (value >= maxValue && i > 0) {
                        // Draw a dashed line to indicate it goes off the chart
                        const lastX = paddingLeft + ((i - 1) / maxN) * graphWidth;
                        const lastY = canvas.height - paddingBottom - (Math.min(values.o2n[i - 1], maxValue) / maxValue) * graphHeight;

                        ctx.lineTo(lastX, lastY);

                        ctx.stroke();

                        // Draw dashed line
                        ctx.beginPath();
                        ctx.setLineDash([5, 5]);
                        ctx.moveTo(lastX, lastY);
                        ctx.lineTo(x, paddingTop);
                        ctx.stroke();
                        ctx.setLineDash([]);

                        // Add text to indicate it goes off the chart
                        ctx.fillStyle = colors.o2n;
                        ctx.fillText("Tumbuh sangat cepat!", x + 10, paddingTop + 15);

                        break;
                    }

                    ctx.lineTo(x, y);
                }

                ctx.stroke();
            }

            // Initial draw
            drawGraph();

            // Update graph when slider changes
            slider.addEventListener('input', function() {
                maxN = parseInt(this.value);
                nValueDisplay.textContent = maxN;
                drawGraph();
            });

            // Redraw on window resize
            window.addEventListener('resize', function() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
                drawGraph();
            });
        });
    </script>
</body>
</html>
