<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Binary Search</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .array-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            height: 300px;
            align-items: flex-end;
            border: 1px solid #ccc; /* Add border for clarity */
            position: relative;
        }
        .bar {
            width: 30px;
            margin: 0 2px;
            background-color: #d3d3d3; /* Default grey color */
            transition: background-color 0.3s ease, height 0.2s ease;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end; /* Align value to the bottom */
            align-items: center;
        }
        .bar-value {
            position: absolute;
            bottom: -25px; /* Position value below the bar */
            width: 100%;
            text-align: center;
            font-size: 12px;
        }
        .bar-index {
             position: absolute;
             top: -20px; /* Position index above the bar */
             width: 100%;
             text-align: center;
             font-size: 10px;
             color: #555;
        }
        .searching {
            background-color: #4285f4; /* Blue for the current search range */
        }
        .mid {
            background-color: #ea4335; /* Red for the middle element being checked */
            border: 2px solid black;
        }
        .found {
            background-color: #34a853; /* Green for the found element */
            border: 2px solid black;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }
        .controls > * {
             margin: 5px;
        }
        button {
            padding: 10px 15px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #3367d6;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        input[type="number"] {
            padding: 8px;
            font-size: 16px;
            width: 80px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .explanation {
            text-align: left;
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        .code-block {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            margin: 20px 0;
            overflow-x: auto;
            font-family: monospace;
            white-space: pre;
        }
        .speed-control {
            margin: 15px 0;
        }
        .status-message {
            margin-top: 15px;
            font-weight: bold;
            min-height: 20px; /* Reserve space */
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simulasi Binary Search</h1>

        <div class="controls">
            <button id="generate">Generate Sorted Array</button>
            <label for="target">Cari Angka:</label>
            <input type="number" id="target" value="50">
            <button id="search">Mulai Pencarian</button>
            <button id="reset">Reset</button>
        </div>

        <div class="speed-control">
            <label for="speed">Kecepatan Animasi: </label>
            <input type="range" id="speed" min="100" max="2000" value="800" step="100">
            <span id="speed-value">800 ms</span>
        </div>

        <div class="array-container" id="array-container"></div>
        <div class="status-message" id="status-message"></div>

        <div class="explanation">
            <h2>Penjelasan Binary Search</h2>
            <p>Binary Search (Pencarian Biner) adalah algoritma pencarian yang efisien untuk menemukan posisi suatu nilai (target) dalam <strong>array yang sudah terurut</strong>. Algoritma ini bekerja dengan cara berulang kali membagi separuh bagian array yang mungkin berisi target, hingga tersisa satu kemungkinan posisi.</p>

            <p><strong>Penting:</strong> Binary Search hanya bisa digunakan pada data yang sudah terurut.</p>

            <p>Langkah-langkah Binary Search:</p>
            <ol>
                <li>Tentukan batas awal (low) di indeks 0 dan batas akhir (high) di indeks terakhir array.</li>
                <li>Selama low lebih kecil atau sama dengan high:</li>
                <ul>
                    <li>Hitung indeks tengah (mid) = floor((low + high) / 2).</li>
                    <li>Bandingkan nilai di indeks tengah (array[mid]) dengan nilai target:</li>
                    <ul>
                        <li>Jika array[mid] sama dengan target, nilai ditemukan! Hentikan pencarian.</li>
                        <li>Jika array[mid] lebih kecil dari target, artinya target (jika ada) pasti berada di separuh kanan array. Ubah low menjadi mid + 1.</li>
                        <li>Jika array[mid] lebih besar dari target, artinya target (jika ada) pasti berada di separuh kiri array. Ubah high menjadi mid - 1.</li>
                    </ul>
                </ul>
                <li>Jika loop selesai (low > high) dan target belum ditemukan, berarti target tidak ada dalam array.</li>
            </ol>

            <h3>Kode Binary Search (Iteratif):</h3>
            <div class="code-block">
// Fungsi Binary Search (Iteratif)
// arr: Array yang sudah terurut
// target: Nilai yang dicari
function binarySearch(arr, target) {
    let low = 0;                // Indeks awal pencarian
    let high = arr.length - 1;  // Indeks akhir pencarian

    // Terus mencari selama rentang pencarian valid (low <= high)
    while (low <= high) {
        // Hitung indeks tengah, Math.floor untuk pembulatan ke bawah
        let mid = Math.floor((low + high) / 2);
        let guess = arr[mid]; // Nilai tebakan di posisi tengah

        // Bandingkan nilai tebakan dengan target
        if (guess === target) {
            return mid; // Target ditemukan, kembalikan indeksnya
        } else if (guess < target) {
            // Jika tebakan terlalu kecil, cari di separuh kanan
            low = mid + 1; // Geser batas bawah ke kanan tengah
        } else {
            // Jika tebakan terlalu besar, cari di separuh kiri
            high = mid - 1; // Geser batas atas ke kiri tengah
        }
    }

    return -1; // Target tidak ditemukan dalam array
}
            </div>

            <h3>Kompleksitas Waktu:</h3>
            <ul>
                <li>Semua Kasus (Terburuk, Terbaik, Rata-rata): O(log n)</li>
            </ul>
            <p>Kompleksitas O(log n) berarti waktu pencarian tumbuh sangat lambat seiring bertambahnya ukuran array. Ini membuat Binary Search jauh lebih cepat daripada Linear Search (O(n)) untuk array yang besar.</p>
        </div>
    </div>

    <script>
        // Mendapatkan elemen-elemen DOM
        const arrayContainer = document.getElementById('array-container');
        const generateButton = document.getElementById('generate');
        const searchButton = document.getElementById('search');
        const resetButton = document.getElementById('reset');
        const targetInput = document.getElementById('target');
        const speedControl = document.getElementById('speed');
        const speedValueSpan = document.getElementById('speed-value');
        const statusMessage = document.getElementById('status-message');

        // Variabel global
        let array = [];
        let animationInProgress = false;
        let animationTimeouts = [];
        let currentLow = -1;
        let currentHigh = -1;
        let currentMid = -1;
        let foundIndex = -1;

        // Fungsi untuk menghasilkan array acak yang sudah terurut
        function generateSortedArray(size = 15) {
            array = [];
            for (let i = 0; i < size; i++) {
                array.push(Math.floor(Math.random() * 100) + 1); // Angka 1-100
            }
            array.sort((a, b) => a - b); // Urutkan array
            resetState();
            displayArray(array);
            statusMessage.textContent = 'Array baru telah dibuat dan diurutkan.';
            searchButton.disabled = false;
            return array;
        }

        // Fungsi untuk menampilkan array sebagai batang
        function displayArray(arr, low = -1, high = -1, mid = -1, found = -1) {
            arrayContainer.innerHTML = '';
            const maxValue = Math.max(...arr, 1); // Use 1 if array is empty

            arr.forEach((value, index) => {
                const bar = document.createElement('div');
                bar.classList.add('bar');

                // Atur tinggi batang
                const height = (value / maxValue) * 250; // Max height 250px
                bar.style.height = `${Math.max(height, 5)}px`; // Minimum height 5px

                // Tambahkan label nilai
                const barValue = document.createElement('div');
                barValue.classList.add('bar-value');
                barValue.textContent = value;
                bar.appendChild(barValue);

                 // Tambahkan label index
                const barIndex = document.createElement('div');
                barIndex.classList.add('bar-index');
                barIndex.textContent = index;
                bar.appendChild(barIndex);

                // Tambahkan kelas untuk visualisasi
                if (index >= low && index <= high) {
                    bar.classList.add('searching'); // Rentang pencarian aktif
                }
                if (index === mid) {
                    bar.classList.add('mid'); // Elemen tengah yang diperiksa
                }
                if (index === found) {
                    bar.classList.add('found'); // Elemen yang ditemukan
                    bar.classList.remove('mid', 'searching'); // Hapus highlight lain jika ditemukan
                }

                arrayContainer.appendChild(bar);
            });
        }

        // Fungsi untuk menjalankan animasi Binary Search
        async function animateBinarySearch(arr, target) {
            if (animationInProgress) return;
            if (arr.length === 0) {
                statusMessage.textContent = 'Array kosong. Generate array terlebih dahulu.';
                return;
            }
            if (target === null || isNaN(target)) {
                 statusMessage.textContent = 'Masukkan angka target yang valid.';
                 return;
            }

            resetState(); // Reset visualisasi sebelum memulai
            animationInProgress = true;
            searchButton.disabled = true;
            generateButton.disabled = true;
            resetButton.disabled = true;

            const animations = []; // Menyimpan langkah-langkah animasi
            let low = 0;
            let high = arr.length - 1;
            let stepFoundIndex = -1;

            // ===== ALGORITMA BINARY SEARCH (dengan pencatatan animasi) =====
            while (low <= high) {
                let mid = Math.floor((low + high) / 2);
                let guess = arr[mid];

                // Catat state sebelum perbandingan
                animations.push({ low, high, mid, found: stepFoundIndex, message: `Mencari antara indeks ${low} dan ${high}. Tengah: ${mid} (nilai ${guess})` });

                if (guess === target) {
                    stepFoundIndex = mid; // Target ditemukan
                    animations.push({ low, high, mid, found: stepFoundIndex, message: `Nilai ${target} ditemukan di indeks ${mid}!` });
                    break; // Hentikan pencarian
                } else if (guess < target) {
                    // Catat keputusan: cari di kanan
                    animations.push({ low, high, mid, found: stepFoundIndex, message: `Nilai ${guess} < ${target}. Cari di separuh kanan.` });
                    low = mid + 1;
                } else {
                    // Catat keputusan: cari di kiri
                    animations.push({ low, high, mid, found: stepFoundIndex, message: `Nilai ${guess} > ${target}. Cari di separuh kiri.` });
                    high = mid - 1;
                }
            }
            // ===== AKHIR ALGORITMA BINARY SEARCH =====

            // Jika loop selesai dan target tidak ditemukan
            if (stepFoundIndex === -1) {
                animations.push({ low, high, mid: -1, found: -1, message: `Nilai ${target} tidak ditemukan dalam array.` });
            }

            // Jalankan animasi langkah demi langkah
            let delay = 0;
            const baseSpeed = parseInt(speedControl.value);

            for (let i = 0; i < animations.length; i++) {
                const step = animations[i];
                const timeoutId = setTimeout(() => {
                    currentLow = step.low;
                    currentHigh = step.high;
                    currentMid = step.mid;
                    foundIndex = step.found;
                    displayArray(arr, currentLow, currentHigh, currentMid, foundIndex);
                    statusMessage.textContent = step.message;

                    // Jika ini langkah terakhir
                    if (i === animations.length - 1) {
                        animationInProgress = false;
                        searchButton.disabled = false;
                        generateButton.disabled = false;
                        resetButton.disabled = false;
                        // Pertahankan highlight hijau jika ditemukan
                        if (foundIndex !== -1) {
                             displayArray(arr, -1, -1, -1, foundIndex);
                        } else {
                             // Hapus semua highlight jika tidak ditemukan
                             displayArray(arr);
                        }
                    }
                }, delay);
                animationTimeouts.push(timeoutId);
                delay += baseSpeed; // Tambah delay untuk langkah berikutnya
            }
        }

        // Fungsi untuk mereset state dan animasi
        function resetState() {
            // Hentikan semua timeout yang sedang berjalan
            animationTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            animationTimeouts = [];
            animationInProgress = false;

            // Reset variabel state visualisasi
            currentLow = -1;
            currentHigh = -1;
            currentMid = -1;
            foundIndex = -1;

            // Tampilkan kembali array tanpa highlight
            displayArray(array);
            statusMessage.textContent = 'Animasi direset.';
            searchButton.disabled = array.length === 0; // Disable search if array is empty
            generateButton.disabled = false;
            resetButton.disabled = false;
        }

        // Event listener untuk tombol
        generateButton.addEventListener('click', () => {
            generateSortedArray();
        });

        searchButton.addEventListener('click', () => {
            const targetValue = parseInt(targetInput.value);
            animateBinarySearch(array, targetValue);
        });

        resetButton.addEventListener('click', resetState);

        // Event listener untuk slider kecepatan
        speedControl.addEventListener('input', () => {
            speedValueSpan.textContent = `${speedControl.value} ms`;
        });

        // Inisialisasi dengan array acak saat halaman dimuat
        window.onload = () => {
            generateSortedArray();
            speedValueSpan.textContent = `${speedControl.value} ms`; // Set initial speed display
        };

    </script>
</body>
</html>