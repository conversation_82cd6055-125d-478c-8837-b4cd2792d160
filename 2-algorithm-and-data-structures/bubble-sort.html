<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Bubble Sort</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .array-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            height: 300px;
            align-items: flex-end;
        }
        .bar {
            width: 30px;
            margin: 0 2px;
            background-color: #4285f4;
            transition: height 0.2s ease;
            position: relative;
        }
        .bar-value {
            position: absolute;
            top: -25px;
            width: 100%;
            text-align: center;
        }
        .comparing {
            background-color: #ea4335;
        }
        .sorted {
            background-color: #34a853;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            margin: 0 5px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #3367d6;
        }
        .explanation {
            text-align: left;
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        .code-block {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            margin: 20px 0;
            overflow-x: auto;
            font-family: monospace;
            white-space: pre;
        }
        .speed-control {
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simulasi Bubble Sort</h1>
        
        <div class="controls">
            <button id="generate">Generate Array Baru</button>
            <button id="sort">Mulai Sorting</button>
            <button id="reset">Reset</button>
        </div>
        
        <div class="speed-control">
            <label for="speed">Kecepatan: </label>
            <input type="range" id="speed" min="10" max="1000" value="300">
        </div>
        
        <div class="array-container" id="array-container"></div>
        
        <div class="explanation">
            <h2>Penjelasan Bubble Sort</h2>
            <p>Bubble Sort adalah algoritma pengurutan sederhana yang bekerja dengan cara membandingkan dua elemen yang berdekatan dan menukarnya jika urutannya salah. Proses ini diulang sampai seluruh array terurut.</p>
            
            <p>Langkah-langkah Bubble Sort:</p>
            <ol>
                <li>Bandingkan elemen pertama dengan elemen kedua. Jika elemen pertama lebih besar dari elemen kedua, tukar posisinya.</li>
                <li>Lanjutkan ke pasangan berikutnya (elemen kedua dan ketiga) dan lakukan hal yang sama.</li>
                <li>Terus lakukan sampai akhir array. Pada titik ini, elemen terbesar akan berada di posisi terakhir.</li>
                <li>Ulangi proses untuk sisa array (kecuali elemen terakhir yang sudah terurut).</li>
                <li>Lanjutkan sampai tidak ada lagi pertukaran yang dilakukan, yang berarti array sudah terurut.</li>
            </ol>
            
            <h3>Kode Bubble Sort:</h3>
            <div class="code-block">
// Fungsi untuk melakukan bubble sort
function bubbleSort(arr) {
    let n = arr.length;
    
    // Loop luar - mengontrol jumlah iterasi
    for (let i = 0; i < n - 1; i++) {
        
        // Loop dalam - membandingkan elemen berdekatan
        for (let j = 0; j < n - i - 1; j++) {
            
            // Jika elemen saat ini lebih besar dari elemen berikutnya
            if (arr[j] > arr[j + 1]) {
                // Tukar posisi elemen
                let temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
    
    return arr;
}
            </div>
            
            <h3>Kompleksitas Waktu:</h3>
            <ul>
                <li>Kasus Terburuk: O(n²) - ketika array terurut terbalik</li>
                <li>Kasus Terbaik: O(n) - ketika array sudah terurut</li>
                <li>Kasus Rata-rata: O(n²)</li>
            </ul>
            
            <p>Meskipun Bubble Sort bukan algoritma pengurutan yang paling efisien, algoritma ini sangat mudah dipahami dan diimplementasikan, sehingga cocok untuk pembelajaran dasar algoritma pengurutan.</p>
        </div>
    </div>

    <script>
        // Mendapatkan elemen-elemen DOM
        const arrayContainer = document.getElementById('array-container');
        const generateButton = document.getElementById('generate');
        const sortButton = document.getElementById('sort');
        const resetButton = document.getElementById('reset');
        const speedControl = document.getElementById('speed');
        
        // Variabel untuk menyimpan array dan status animasi
        let array = [];
        let animationInProgress = false;
        let animationTimeouts = [];
        
        // Fungsi untuk menghasilkan array acak
        function generateRandomArray(size = 10) {
            array = [];
            for (let i = 0; i < size; i++) {
                array.push(Math.floor(Math.random() * 100) + 1); // Angka 1-100
            }
            displayArray(array);
            return array;
        }
        
        // Fungsi untuk menampilkan array sebagai batang
        function displayArray(arr, comparing = [], sorted = []) {
            arrayContainer.innerHTML = '';
            
            // Temukan nilai maksimum untuk penskalaan
            const maxValue = Math.max(...arr);
            
            // Buat batang untuk setiap elemen array
            arr.forEach((value, index) => {
                const bar = document.createElement('div');
                bar.classList.add('bar');
                
                // Atur tinggi batang berdasarkan nilai
                const height = (value / maxValue) * 250;
                bar.style.height = `${height}px`;
                
                // Tambahkan label nilai
                const barValue = document.createElement('div');
                barValue.classList.add('bar-value');
                barValue.textContent = value;
                bar.appendChild(barValue);
                
                // Tambahkan kelas untuk elemen yang sedang dibandingkan atau sudah terurut
                if (comparing.includes(index)) {
                    bar.classList.add('comparing');
                }
                if (sorted.includes(index)) {
                    bar.classList.add('sorted');
                }
                
                arrayContainer.appendChild(bar);
            });
        }
        
        // Fungsi untuk menjalankan animasi bubble sort
        async function animateBubbleSort(arr) {
            if (animationInProgress) return;
            animationInProgress = true;
            
            // Buat salinan array untuk dimanipulasi
            const animations = [];
            const arrayCopy = [...arr];
            const n = arrayCopy.length;
            const sortedIndices = [];
            
            // Implementasi Bubble Sort dengan pencatatan animasi
            // ===== ALGORITMA BUBBLE SORT =====
            // Loop luar - mengontrol jumlah iterasi
            for (let i = 0; i < n - 1; i++) {
                // Loop dalam - membandingkan elemen berdekatan
                for (let j = 0; j < n - i - 1; j++) {
                    // Catat indeks yang sedang dibandingkan
                    animations.push({
                        type: 'compare',
                        indices: [j, j + 1],
                        array: [...arrayCopy]
                    });
                    
                    // Jika elemen saat ini lebih besar dari elemen berikutnya
                    if (arrayCopy[j] > arrayCopy[j + 1]) {
                        // Tukar posisi elemen
                        let temp = arrayCopy[j];
                        arrayCopy[j] = arrayCopy[j + 1];
                        arrayCopy[j + 1] = temp;
                        
                        // Catat pertukaran yang terjadi
                        animations.push({
                            type: 'swap',
                            indices: [j, j + 1],
                            array: [...arrayCopy]
                        });
                    }
                }
                // Setelah satu iterasi loop luar, elemen terbesar sudah berada di posisi terakhir
                // Tandai elemen ini sebagai sudah terurut
                sortedIndices.push(n - 1 - i);
                animations.push({
                    type: 'sorted',
                    indices: [...sortedIndices],
                    array: [...arrayCopy]
                });
            }
            // Tandai elemen pertama sebagai terurut (karena semua elemen lain sudah terurut)
            sortedIndices.push(0);
            animations.push({
                type: 'sorted',
                indices: [...sortedIndices],
                array: [...arrayCopy]
            });
            // ===== AKHIR ALGORITMA BUBBLE SORT =====
            
            // Jalankan animasi
            let delay = 0;
            const speed = parseInt(speedControl.value);
            
            for (let i = 0; i < animations.length; i++) {
                const animation = animations[i];
                
                // Buat timeout untuk setiap langkah animasi
                const timeoutId = setTimeout(() => {
                    if (animation.type === 'compare' || animation.type === 'swap') {
                        displayArray(animation.array, animation.indices, sortedIndices.slice(0, sortedIndices.indexOf(animation.indices[1]) + 1));
                    } else if (animation.type === 'sorted') {
                        displayArray(animation.array, [], animation.indices);
                    }
                    
                    // Setelah animasi selesai
                    if (i === animations.length - 1) {
                        animationInProgress = false;
                    }
                }, delay);
                
                animationTimeouts.push(timeoutId);
                delay += speed;
            }
        }
        
        // Fungsi untuk mereset animasi
        function resetAnimation() {
            // Hentikan semua timeout yang sedang berjalan
            animationTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            animationTimeouts = [];
            animationInProgress = false;
            
            // Tampilkan kembali array awal
            displayArray(array);
        }
        
        // Event listener untuk tombol
        generateButton.addEventListener('click', () => {
            resetAnimation();
            generateRandomArray();
        });
        
        sortButton.addEventListener('click', () => {
            resetAnimation();
            animateBubbleSort(array);
        });
        
        resetButton.addEventListener('click', resetAnimation);
        
        // Inisialisasi dengan array acak saat halaman dimuat
        generateRandomArray();
    </script>
</body>
</html>