<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Struktur Data Dasar</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .container {
            max-width: 900px;
            width: 100%;
            margin: 20px auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .visualization-area {
            display: flex;
            justify-content: center;
            align-items: center; /* Center vertically for stack/queue */
            position: relative; /* Needed for absolute positioning of tree nodes/lines */
            margin: 30px 0;
            min-height: 150px; /* Ensure space for visualization */
            border: 1px dashed #ccc;
            padding: 15px;
            background-color: #fafafa;
            border-radius: 4px;
            flex-wrap: wrap; /* Allow items to wrap */
            gap: 10px; /* Spacing between elements */
        }
        .element {
            width: 50px;
            height: 50px;
            background-color: #4285f4;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            border-radius: 4px;
            margin: 0 5px;
            transition: all 0.3s ease;
            position: relative; /* For potential labels */
            z-index: 1; /* Ensure nodes are above lines */
        }
        .node-value {
             width: 50px;
            height: 50px;
            background-color: #34a853; /* Different color for list */
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            border-radius: 4px;
            border: 2px solid #2a8c4a;
        }
        .node-pointer {
            width: 30px;
            height: 2px;
            background-color: #333;
            margin: 0;
            position: absolute;
            right: -30px; /* Adjusted to match the spacing between nodes */
            top: 50%; /* Center vertically */
            transform: translateY(-50%); /* Ensure perfect vertical centering */
        }

        /* Styling for list nodes */
        .list-node {
            margin: 0 30px 0 0; /* Adjusted margin to be only on the right side */
            position: relative; /* Ensure relative positioning for absolute child positioning */
            display: flex;
            align-items: center;
        }

        /* Specific styling for Circular Linked List */
        #circular-list-viz .list-node {
            margin: 0 15px; /* Increase spacing between nodes */
            position: relative; /* Ensure relative positioning for absolute child positioning */
        }

        #circular-list-viz .node-pointer {
            width: 40px; /* Longer pointers for circular list */
            height: 3px; /* Thicker pointers for better visibility */
            position: absolute; /* Position absolutely to center between nodes */
            right: -40px; /* Position pointer to extend from the right edge of the node */
            top: 50%; /* Center vertically */
            transform: translateY(-50%); /* Ensure perfect vertical centering */
        }

        #circular-list-viz .node-value {
            background-color: #4CAF50; /* Different color for circular list nodes */
            border: 2px solid #388E3C; /* Darker border */
            box-shadow: 0 3px 5px rgba(0,0,0,0.2); /* Add shadow for depth */
        }

        /* Container for the entire circular list visualization */
        #circular-list-viz > div {
            padding: 20px 10px;
            background-color: #f5f5f5;
            border-radius: 8px;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        }

        /* Specific styling for Doubly Linked List */
        #doubly-list-viz .list-node {
            margin: 0 25px; /* Increase spacing between nodes */
            position: relative; /* Ensure relative positioning for absolute child positioning */
        }

        #doubly-list-viz .node-pointer {
            width: 50px; /* Longer pointers for doubly list */
            height: 3px; /* Thicker pointers for better visibility */
            position: absolute; /* Position absolutely to position pointers */
        }

        /* Position the next pointer (right) - at the top */
        #doubly-list-viz .node-pointer:nth-child(3) {
            right: -50px; /* Position pointer to extend from the right edge of the node */
            top: 20%; /* Position at the top fifth of the node */
            width: 40px; /* Shorter to account for the arrowhead */
        }

        /* Position the prev pointer (left) - at the bottom */
        #doubly-list-viz .node-pointer:nth-child(1) {
            left: -50px; /* Position pointer to extend from the left edge of the node */
            bottom: 20%; /* Position at the bottom fifth of the node */
            width: 40px; /* Shorter to account for the arrowhead */
            direction: rtl; /* Ensure the pointer is drawn from right to left */
        }

        #doubly-list-viz .node-value {
            background-color: #2196F3; /* Different color for doubly list nodes */
            border: 2px solid #1976D2; /* Darker border */
            box-shadow: 0 3px 5px rgba(0,0,0,0.2); /* Add shadow for depth */
        }

        /* Container for the entire doubly list visualization */
        #doubly-list-viz > div {
            padding: 20px 10px;
            background-color: #f5f5f5;
            border-radius: 8px;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        }
        .node-pointer::after { /* Arrowhead */
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-left: 8px solid #333;
            display: inline-block;
        }

        /* Special styling for circular list arrowheads */
        #circular-list-viz .node-pointer::after {
            right: -4px;
            top: -4px;
        }
        .stack-container, .queue-container {
            display: flex;
            flex-direction: column-reverse; /* Stack grows upwards */
            align-items: center;
            min-width: 80px; /* Ensure some width */
        }
        .queue-container {
            flex-direction: row; /* Queue is horizontal */
            align-items: center;
        }
        .stack-element, .queue-element {
            width: 60px;
            height: 40px;
            background-color: #fbbc05; /* Yellow for stack/queue */
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid #e6a800;
            border-radius: 4px;
            margin: 5px 0; /* Vertical margin for stack */
            transition: all 0.3s ease;
        }
         .queue-element {
             margin: 0 5px; /* Horizontal margin for queue */
         }

        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #3367d6;
        }
        button:disabled {
            background-color: #aaa;
            cursor: not-allowed;
        }
        input[type="text"], input[type="number"] {
            padding: 8px;
            margin: 0 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .explanation {
            text-align: left;
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        .code-block {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            margin: 15px 0;
            overflow-x: auto;
            font-family: monospace;
            white-space: pre;
            font-size: 13px;
            border: 1px solid #ddd;
        }
        .label {
            font-size: 12px;
            color: #555;
            margin-top: 5px;
            text-align: center;
        }
        .index-label {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #555;
        }
        .stack-label {
             margin-top: 10px;
             font-weight: bold;
        }
        .queue-label-front, .queue-label-rear {
            font-weight: bold;
            margin: 0 10px;
        }

        /* Styling untuk visualisasi step-by-step */
        .step-visualization {
            margin-top: 20px;
            padding: 20px;
            background-color: #fff8e1;
            border-radius: 12px;
            border-left: 5px solid #ff9800;
            display: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #e65100;
            font-size: 1.2em;
            text-align: center;
        }
        .step-content {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            align-items: center;
            min-height: 150px;
            position: relative;
            overflow-x: auto;
            padding: 10px;
        }
        .step-description {
            padding: 10px;
            background-color: #fff;
            border-radius: 8px;
            margin-top: 10px;
            text-align: center;
            font-size: 1.1em;
            color: #333;
            border: 1px solid #ffe0b2;
        }
        .step-arrow {
            color: #ff9800;
            font-size: 24px;
            margin: 0 10px;
        }
        .step-highlight {
            box-shadow: 0 0 8px 2px rgba(255, 152, 0, 0.7);
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 8px 2px rgba(255, 152, 0, 0.7); }
            50% { box-shadow: 0 0 12px 4px rgba(255, 152, 0, 0.9); }
            100% { box-shadow: 0 0 8px 2px rgba(255, 152, 0, 0.7); }
        }
        .step-controls {
            margin-top: 15px;
            text-align: center;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        .step-btn {
            padding: 10px 20px;
            margin: 0 5px;
            background-color: #ff9800;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
        }
        .step-btn:hover {
            background-color: #e65100;
            transform: translateY(-2px);
        }
        .step-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .pointer-label {
            font-size: 14px;
            color: #ff5722;
            font-weight: bold;
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid #ff9800;
            white-space: nowrap;
        }
        .node-pointer.highlight-pointer {
            background-color: #ff5722;
            height: 3px;
        }
        .node-pointer.highlight-pointer::after {
            border-left-color: #ff5722;
        }

        /* Ensure highlighted pointers maintain correct positioning */
        .node-pointer.highlight-pointer {
            background-color: #ff5722;
            height: 3px;
            /* Maintain absolute positioning */
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Ensure highlighted pointers in circular list maintain correct positioning */
        #circular-list-viz .node-pointer.highlight-pointer {
            background-color: #ff5722;
            height: 3px;
            /* Maintain absolute positioning */
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
        }
        .step-node {
            position: relative;
            margin: 0 5px;
        }
        .new-node-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            margin: 0 15px;
        }
        /* Tambahkan style untuk memperjelas node yang akan dimasukkan */
        .node-value.insert-target {
            border: 2px dashed #ff9800;
            animation: target-pulse 1.5s infinite;
        }
        @keyframes target-pulse {
            0% { border-color: #ff9800; }
            50% { border-color: #ff5722; }
            100% { border-color: #ff9800; }
        }

        /* Perbaiki tampilan langkah-langkah */
        .list-step-container {
            position: relative;
            min-height: 230px; /* Tambah height agar cukup untuk node dan panah */
        }

        /* Styling untuk visualisasi tree/bst/avl */
         #tree-viz .element, #bst-viz .element { /* Tree/BST node styles */
             position: absolute; /* Needed for drawTree/drawBST */
         }
         #tree-viz .tree-line, #bst-viz .tree-line { /* Tree/BST line styles */
             position: absolute;
             height: 2px;
             background-color: #555;
             transform-origin: 0 0;
             z-index: 0; /* Lines behind nodes */
         }
         #bst-viz .tree-label { /* BST line labels */
             position: absolute;
             font-size: 12px;
             color: #555;
             z-index: 1;
             background: #fafafa; /* Match background */
             padding: 0 2px;
         }
         /* Tambahkan style untuk menampilkan indeks node */
        .node-index {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #666;
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        /* Perbaiki styling untuk arrow dari node baru ke target */
        .new-node-arrow {
            position: absolute;
            width: 100px;
            height: 80px;
            z-index: 5;
            pointer-events: none;
        }

        /* Style untuk indikator target position */
        .target-position {
            position: absolute;
            bottom: -45px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            font-weight: bold;
            color: #e65100;
            background-color: #fff3e0;
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid #ff9800;
            white-space: nowrap;
         }

    </style>
</head>
<body>

    <h1>Simulasi Struktur Data Dasar</h1>

    <!-- Array Section -->
    <div class="container">
        <h2>Array</h2>
        <div class="controls">
            <input type="text" id="array-value" placeholder="Nilai">
            <input type="number" id="array-index" placeholder="Indeks (opsional)">
            <button id="array-add">Tambah/Set</button>
            <button id="array-remove">Hapus (by Indeks)</button>
            <button id="array-reset">Reset Array</button>
        </div>
        <div class="visualization-area" id="array-viz">
            <!-- Visualisasi Array akan muncul di sini -->
        </div>
        <div class="explanation">
            <h3>Penjelasan Array</h3>
            <p>Array adalah struktur data yang menyimpan kumpulan elemen (nilai) dengan tipe data yang sama dalam urutan tertentu. Setiap elemen dapat diakses langsung menggunakan <strong>indeks</strong> (nomor posisi), yang biasanya dimulai dari 0.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Ukuran Tetap (di beberapa bahasa):</strong> Dalam beberapa bahasa pemrograman, ukuran array harus ditentukan saat dibuat dan tidak bisa diubah. Di JavaScript, array lebih fleksibel dan ukurannya bisa dinamis.</li>
                <li><strong>Akses Cepat:</strong> Mengakses elemen berdasarkan indeks sangat cepat (O(1)) karena komputer bisa langsung menghitung lokasi memori elemen tersebut.</li>
                <li><strong>Penyisipan/Penghapusan Lambat:</strong> Menambah atau menghapus elemen di tengah array bisa lambat (O(n)) karena elemen-elemen lain mungkin perlu digeser.</li>
            </ul>
            <p><strong>Operasi Umum:</strong></p>
            <ul>
                <li><strong>Tambah/Set Elemen:</strong> Menambahkan elemen baru (biasanya di akhir) atau mengubah nilai elemen pada indeks tertentu.</li>
                <li><strong>Hapus Elemen:</strong> Menghapus elemen pada indeks tertentu.</li>
                <li><strong>Akses Elemen:</strong> Mendapatkan nilai elemen pada indeks tertentu.</li>
            </ul>
            <div class="code-block">
// Contoh Array di JavaScript
let contohArray = [10, 20, 30, 40];

// Mengakses elemen ke-2 (indeks 1)
console.log(contohArray[1]); // Output: 20

// Mengubah elemen ke-3 (indeks 2)
contohArray[2] = 35;
console.log(contohArray); // Output: [10, 20, 35, 40]

// Menambah elemen di akhir
contohArray.push(50);
console.log(contohArray); // Output: [10, 20, 35, 40, 50]

// Menghapus elemen terakhir
contohArray.pop();
console.log(contohArray); // Output: [10, 20, 35, 40]
            </div>
        </div>
    </div>

    <!-- Linked List Section -->
    <div class="container">
        <h2>Single Linked List</h2>
        <div class="controls">
            <input type="text" id="list-value" placeholder="Nilai">
            <button id="list-add-head">Tambah di Awal</button>
            <button id="list-add-tail">Tambah di Akhir</button>
            <button id="list-remove-head">Hapus Awal</button>
            <button id="list-remove-tail">Hapus Akhir</button>
            <input type="number" id="list-index" placeholder="Indeks">
            <button id="list-add-middle">Tambah di Tengah</button>
            <button id="list-remove-middle">Hapus di Tengah</button>
            <button id="list-reset">Reset List</button>
        </div>
        <div class="visualization-area" id="list-viz">
            <!-- Visualisasi Linked List akan muncul di sini -->
        </div>

        <!-- Area untuk visualisasi langkah-langkah -->
        <div class="step-visualization" id="list-steps">
            <div class="step-title">Visualisasi Langkah-langkah</div>
            <div class="step-content" id="list-step-content"></div>
            <div class="step-description" id="list-step-description"></div>
            <div class="step-controls">
                <button class="step-btn" id="list-prev-step">Langkah Sebelumnya</button>
                <button class="step-btn" id="list-next-step">Langkah Selanjutnya</button>
                <button class="step-btn" id="list-close-steps">Tutup</button>
            </div>
        </div>

        <div class="explanation">
            <h3>Penjelasan Single Linked List</h3>
            <p>Single Linked List adalah struktur data linier di mana elemen-elemen (disebut <strong>node</strong>) tidak disimpan di lokasi memori yang berdekatan. Setiap node berisi data dan <strong>pointer</strong> (penunjuk) ke node berikutnya dalam urutan.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Ukuran Dinamis:</strong> Ukuran linked list dapat dengan mudah bertambah atau berkurang saat runtime.</li>
                <li><strong>Penyisipan/Penghapusan Efisien:</strong> Menambah atau menghapus node (terutama di awal atau akhir) biasanya lebih cepat daripada array (O(1) jika memiliki pointer ke head/tail), karena tidak perlu menggeser elemen lain. Cukup mengubah pointer.</li>
                <li><strong>Akses Lambat:</strong> Mengakses elemen tertentu (misalnya elemen ke-5) memerlukan traversal (mengikuti pointer) dari awal list (O(n)).</li>
            </ul>
            <p><strong>Operasi Umum:</strong></p>
            <ul>
                <li><strong>Tambah Node:</strong> Menambahkan node baru di awal (head), akhir (tail), atau posisi tertentu.</li>
                <li><strong>Hapus Node:</strong> Menghapus node dari awal, akhir, atau posisi tertentu.</li>
                <li><strong>Traversal:</strong> Mengunjungi setiap node dalam list secara berurutan.</li>
            </ul>
             <div class="code-block">
// Konsep Node dalam Single Linked List (Pseudocode/JavaScript Class)
class Node {
    constructor(data) {
        this.data = data; // Nilai yang disimpan
        this.next = null; // Pointer ke node berikutnya (awalnya null)
    }
}

// Membuat beberapa node
let node1 = new Node(10);
let node2 = new Node(20);
let node3 = new Node(30);

// Menghubungkan node (membuat list: 10 -> 20 -> 30)
node1.next = node2;
node2.next = node3;

// Head (kepala) list adalah node1
let head = node1;

// Traversal (mengunjungi semua node)
let current = head;
while (current !== null) {
    console.log(current.data);
    current = current.next; // Pindah ke node berikutnya
}
// Output: 10, 20, 30
            </div>
        </div>
    </div>

    <!-- Doubly Linked List Section -->
    <div class="container">
        <h2>Doubly Linked List</h2>
        <div class="controls">
            <input type="text" id="doubly-list-value" placeholder="Nilai">
            <button id="doubly-list-add-head">Tambah di Awal</button>
            <button id="doubly-list-add-tail">Tambah di Akhir</button>
            <button id="doubly-list-remove-head">Hapus Awal</button>
            <button id="doubly-list-remove-tail">Hapus Akhir</button>
            <input type="number" id="doubly-list-index" placeholder="Indeks">
            <button id="doubly-list-add-middle">Tambah di Tengah</button>
            <button id="doubly-list-remove-middle">Hapus di Tengah</button>
            <button id="doubly-list-reset">Reset List</button>
        </div>
        <div class="visualization-area" id="doubly-list-viz">
            <!-- Visualisasi Doubly Linked List akan muncul di sini -->
        </div>

        <!-- Area untuk visualisasi langkah-langkah -->
        <div class="step-visualization" id="doubly-list-steps">
            <div class="step-title" id="doubly-step-title">Visualisasi Langkah-langkah</div>
            <div class="step-content" id="doubly-step-content"></div>
            <div class="step-description" id="doubly-step-description"></div>
            <div class="step-controls">
                <button class="step-btn" id="doubly-prev-step">Langkah Sebelumnya</button>
                <button class="step-btn" id="doubly-next-step">Langkah Selanjutnya</button>
                <button class="step-btn" id="doubly-close-steps">Tutup</button>
            </div>
        </div>

        <div class="explanation">
            <h3>Penjelasan Doubly Linked List</h3>
            <p>Doubly Linked List adalah variasi dari Linked List di mana setiap node memiliki <strong>dua pointer</strong>: satu ke node berikutnya (<strong>next</strong>) dan satu ke node sebelumnya (<strong>previous</strong>). Ini memungkinkan traversal (penjelajahan) list dalam dua arah.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Traversal Dua Arah:</strong> Dapat bergerak maju dan mundur dalam list.</li>
                <li><strong>Penyisipan/Penghapusan Efisien:</strong> Menambah atau menghapus node (terutama di dekat node yang diketahui) bisa lebih cepat daripada singly linked list karena dapat langsung mengakses node sebelumnya.</li>
                <li><strong>Memori Tambahan:</strong> Membutuhkan lebih banyak memori karena setiap node menyimpan dua pointer.</li>
            </ul>
            <p><strong>Operasi Umum:</strong></p>
            <ul>
                <li><strong>Tambah Node:</strong> Menambahkan node baru di awal (head), akhir (tail), atau posisi tertentu.</li>
                <li><strong>Hapus Node:</strong> Menghapus node dari awal, akhir, atau posisi tertentu.</li>
                <li><strong>Traversal:</strong> Mengunjungi setiap node dalam list secara berurutan (maju atau mundur).</li>
            </ul>
            <div class="code-block">
// Konsep Node dalam Doubly Linked List (Pseudocode/JavaScript Class)
class DoublyListNode {
    constructor(data) {
        this.data = data; // Nilai yang disimpan
        this.next = null; // Pointer ke node berikutnya (awalnya null)
        this.prev = null; // Pointer ke node sebelumnya (awalnya null)
    }
}

// Contoh Pembuatan dan Penghubungan Node
let node1_dll = new DoublyListNode(10); // Renamed to avoid conflict
let node2_dll = new DoublyListNode(20);
let node3_dll = new DoublyListNode(30);

// Menghubungkan node (membuat list: 10 <-> 20 <-> 30)
node1_dll.next = node2_dll;
node2_dll.prev = node1_dll;
node2_dll.next = node3_dll;
node3_dll.prev = node2_dll;

// Head (kepala) list adalah node1_dll
let head_dll = node1_dll; // Renamed

// Tail (ekor) list adalah node3_dll
let tail_dll = node3_dll; // Renamed
            </div>
        </div>
    </div>

    <!-- Circular Linked List Section -->
    <div class="container">
        <h2>Circular Linked List</h2>
        <div class="controls">
            <input type="text" id="circular-list-value" placeholder="Nilai">
            <button id="circular-list-add-head">Tambah di Awal</button>
            <button id="circular-list-add-tail">Tambah di Akhir</button>
            <button id="circular-list-remove-head">Hapus Awal</button>
            <button id="circular-list-remove-tail">Hapus Akhir</button>
            <input type="number" id="circular-list-index" placeholder="Indeks">
            <button id="circular-list-add-middle">Tambah di Tengah</button>
            <button id="circular-list-remove-middle">Hapus di Tengah</button>
            <button id="circular-list-reset">Reset List</button>
        </div>
        <div class="visualization-area" id="circular-list-viz">
            <!-- Visualisasi Circular Linked List akan muncul di sini -->
        </div>

        <!-- Area untuk visualisasi langkah-langkah -->
        <div class="step-visualization" id="circular-list-steps">
            <div class="step-title">Visualisasi Langkah-langkah</div>
            <div class="step-content" id="circular-step-content"></div>
            <div class="step-description" id="circular-step-description"></div>
            <div class="step-controls">
                <button class="step-btn" id="circular-prev-step">Langkah Sebelumnya</button>
                <button class="step-btn" id="circular-next-step">Langkah Selanjutnya</button>
                <button class="step-btn" id="circular-close-steps">Tutup</button>
            </div>
        </div>

        <div class="explanation">
            <h3>Penjelasan Circular Linked List</h3>
            <p>Circular Linked List adalah variasi dari linked list di mana node terakhir (tail) menunjuk kembali ke node pertama (head), membentuk sebuah lingkaran. Dengan struktur seperti ini, tidak ada 'akhir' dari list, dan kita dapat mengakses seluruh list dari node manapun.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Tidak Ada Akhir:</strong> Tidak ada node dengan pointer next bernilai null, semuanya terhubung dalam lingkaran.</li>
                <li><strong>Traversal Berkelanjutan:</strong> Dapat melakukan traversal terus-menerus tanpa berhenti (jika tidak diberi kondisi berhenti).</li>
                <li><strong>Efisien untuk Struktur Melingkar:</strong> Cocok untuk aplikasi yang memerlukan akses berulang seperti alokasi sumber daya dalam sistem operasi, penerapan antrian melingkar, dll.</li>
            </ul>
            <p><strong>Operasi Umum:</strong></p>
            <ul>
                <li><strong>Tambah Node:</strong> Menambahkan node baru di awal, akhir, atau posisi tertentu dalam lingkaran.</li>
                <li><strong>Hapus Node:</strong> Menghapus node dari awal, akhir, atau posisi tertentu.</li>
                <li><strong>Traversal:</strong> Mengunjungi setiap node dalam list secara berurutan dengan memastikan tidak terjebak dalam loop tak terbatas.</li>
            </ul>
            <div class="code-block">
// Konsep Node dalam Circular Linked List (Pseudocode/JavaScript Class)
class CircularListNode_Code { // Renamed class to avoid conflict
    constructor(data) {
        this.data = data; // Nilai yang disimpan
        this.next = null; // Pointer ke node berikutnya (awalnya null)
    }
}

// Contoh Pembuatan Circular Linked List
let node1_cll = new CircularListNode_Code(10); // Renamed vars
let node2_cll = new CircularListNode_Code(20);
let node3_cll = new CircularListNode_Code(30);

// Menghubungkan node membentuk lingkaran: 10 -> 20 -> 30 -> 10
node1_cll.next = node2_cll;
node2_cll.next = node3_cll;
node3_cll.next = node1_cll; // Node terakhir menunjuk kembali ke node pertama

// Head adalah node1_cll
let head_cll = node1_cll; // Renamed

// Traversal Circular Linked List
let current_cll = head_cll; // Renamed
let count_cll = 0;          // Renamed
const maxIterations_cll = 10; // Renamed

do {
    console.log(current_cll.data);
    current_cll = current_cll.next;
    count_cll++;
} while (current_cll !== head_cll && count_cll < maxIterations_cll);
// Berhenti setelah kembali ke head atau mencapai batas iterasi
            </div>
        </div>
    </div>

    <!-- Stack Section -->
    <div class="container">
        <h2>Stack (Tumpukan)</h2>
        <div class="controls">
            <input type="text" id="stack-value" placeholder="Nilai">
            <button id="stack-push">Push (Tambah)</button>
            <button id="stack-pop">Pop (Hapus)</button>
            <button id="stack-reset">Reset Stack</button>
        </div>
        <div class="visualization-area" id="stack-viz">
             <div class="stack-container">
                 <!-- Visualisasi Stack akan muncul di sini -->
             </div>
             <div class="stack-label">TOP</div>
        </div>
        <div class="explanation">
            <h3>Penjelasan Stack</h3>
            <p>Stack adalah struktur data linier yang mengikuti prinsip <strong>LIFO (Last-In, First-Out)</strong>. Artinya, elemen yang terakhir dimasukkan adalah elemen yang pertama kali dikeluarkan. Bayangkan tumpukan piring: Anda menambah piring baru di atas, dan saat mengambil, Anda mengambil piring paling atas.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Prinsip LIFO:</strong> Operasi utama terjadi di satu ujung yang disebut "top" (puncak).</li>
                <li><strong>Operasi Efisien:</strong> Operasi dasar (push, pop) sangat cepat (O(1)).</li>
            </ul>
            <p><strong>Operasi Umum:</strong></p>
            <ul>
                <li><strong>Push:</strong> Menambahkan elemen baru ke puncak (top) stack.</li>
                <li><strong>Pop:</strong> Menghapus dan mengembalikan elemen dari puncak (top) stack.</li>
                <li><strong>Peek/Top:</strong> Melihat elemen di puncak stack tanpa menghapusnya.</li>
            </ul>
            <p><strong>Penggunaan Umum:</strong> Manajemen pemanggilan fungsi (call stack), fitur Undo/Redo, pengecekan tanda kurung seimbang.</p>
            <div class="code-block">
// Implementasi Stack sederhana menggunakan Array di JavaScript
let stack_code = []; // Renamed

// Push (menambah elemen)
stack_code.push(10); // stack: [10]
stack_code.push(20); // stack: [10, 20]
stack_code.push(30); // stack: [10, 20, 30] -> 30 adalah TOP

console.log("Stack:", stack_code);

// Pop (menghapus elemen dari TOP)
let poppedElement_code = stack_code.pop(); // Renamed
console.log("Dikeluarkan (Pop):", poppedElement_code);
console.log("Stack setelah Pop:", stack_code);

// Peek (melihat elemen TOP tanpa menghapus)
let topElement_code = stack_code[stack_code.length - 1]; // Renamed
console.log("Elemen Puncak (Peek):", topElement_code); // Output: 20
console.log("Stack tetap:", stack_code); // Output: [10, 20]
            </div>
        </div>
    </div>

    <!-- Queue Section -->
    <div class="container">
        <h2>Queue (Antrian)</h2>
        <div class="controls">
            <input type="text" id="queue-value" placeholder="Nilai">
            <button id="queue-enqueue">Enqueue (Tambah)</button>
            <button id="queue-dequeue">Dequeue (Hapus)</button>
            <button id="queue-reset">Reset Queue</button>
        </div>
        <div class="visualization-area" id="queue-viz">
             <div class="queue-label-front">FRONT</div>
             <div class="queue-container">
                 <!-- Visualisasi Queue akan muncul di sini -->
             </div>
             <div class="queue-label-rear">REAR</div>
        </div>
        <div class="explanation">
            <h3>Penjelasan Queue</h3>
            <p>Queue adalah struktur data linier yang mengikuti prinsip <strong>FIFO (First-In, First-Out)</strong>. Artinya, elemen yang pertama kali dimasukkan adalah elemen yang pertama kali dikeluarkan. Seperti antrian di dunia nyata: orang yang datang pertama akan dilayani pertama.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Prinsip FIFO:</strong> Elemen ditambahkan di satu ujung (disebut "rear" atau "back") dan dihapus dari ujung lainnya (disebut "front" atau "head").</li>
                <li><strong>Operasi Efisien:</strong> Operasi dasar (enqueue, dequeue) biasanya cepat (O(1)), terutama jika diimplementasikan dengan benar (misalnya menggunakan linked list atau array dinamis yang dioptimalkan).</li>
            </ul>
            <p><strong>Operasi Umum:</strong></p>
            <ul>
                <li><strong>Enqueue:</strong> Menambahkan elemen baru ke belakang (rear) antrian.</li>
                <li><strong>Dequeue:</strong> Menghapus dan mengembalikan elemen dari depan (front) antrian.</li>
                <li><strong>Peek/Front:</strong> Melihat elemen di depan antrian tanpa menghapusnya.</li>
            </ul>
            <p><strong>Penggunaan Umum:</strong> Penjadwalan tugas (CPU scheduling), simulasi antrian, Breadth-First Search (BFS) pada graf.</p>
             <div class="code-block">
// Implementasi Queue sederhana menggunakan Array di JavaScript
let queue_code = []; // Renamed

// Enqueue (menambah elemen ke REAR)
queue_code.push(10); // queue: [10] (Front & Rear)
queue_code.push(20); // queue: [10, 20] (Front=10, Rear=20)
queue_code.push(30); // queue: [10, 20, 30] (Front=10, Rear=30)

console.log("Queue:", queue_code);

// Dequeue (menghapus elemen dari FRONT)
// Metode shift() pada array JavaScript efisien untuk ini
let dequeuedElement_code = queue_code.shift(); // Renamed
console.log("Dikeluarkan (Dequeue):", dequeuedElement_code);
console.log("Queue setelah Dequeue:", queue_code);

// Peek (melihat elemen FRONT tanpa menghapus)
let frontElement_code = queue_code[0]; // Renamed
console.log("Elemen Depan (Peek):", frontElement_code); // Output: 20
console.log("Queue tetap:", queue_code); // Output: [20, 30]
            </div>
        </div>
    </div>

    <!-- Tree Section -->
    <div class="container">
        <h2>Tree (Pohon)</h2>
        <div class="controls">
            <input type="text" id="tree-value" placeholder="Nilai">
            <button id="tree-add">Tambah Node</button>
            <button id="tree-remove">Hapus Node</button>
            <button id="tree-reset">Reset Tree</button>
        </div>
        <div class="visualization-area" id="tree-viz">
            <!-- Visualisasi Tree akan muncul di sini -->
        </div>

        <!-- Area untuk visualisasi langkah-langkah -->
        <div class="step-visualization" id="tree-steps">
            <div class="step-title">Visualisasi Langkah-langkah</div>
            <div class="step-content" id="tree-step-content"></div>
            <div class="step-description" id="tree-step-description"></div>
            <div class="step-controls">
                <button class="step-btn" id="tree-prev-step">Langkah Sebelumnya</button>
                <button class="step-btn" id="tree-next-step">Langkah Selanjutnya</button>
                <button class="step-btn" id="tree-close-steps">Tutup</button>
            </div>
        </div>

        <div class="explanation">
            <h3>Penjelasan Tree (Pohon)</h3>
            <p>Tree (Pohon) adalah struktur data non-linear yang terdiri dari node yang dihubungkan secara hierarkis. Setiap node memiliki node induk (kecuali node root) dan dapat memiliki nol atau lebih node anak.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Hierarkis:</strong> Data disusun dalam hierarki dengan satu node root di bagian atas.</li>
                <li><strong>Tidak Ada Siklus:</strong> Tidak ada jalur yang membentuk lingkaran.</li>
                <li><strong>Representasi Hubungan:</strong> Cocok untuk merepresentasikan hubungan hierarkis seperti file system, struktur organisasi, dll.</li>
            </ul>
            <p><strong>Jenis-jenis Tree:</strong></p>
            <ul>
                <li><strong>Binary Tree:</strong> Setiap node memiliki maksimal dua anak (kiri dan kanan).</li>
                <li><strong>Binary Search Tree (BST):</strong> Binary tree dengan properti pengurutan khusus (nilai di subtree kiri < nilai node, nilai di subtree kanan > nilai node).</li>
                <li><strong>AVL Tree:</strong> BST yang menyeimbangkan diri sendiri saat operasi penyisipan/penghapusan.</li>
                <li><strong>Red-Black Tree:</strong> Jenis lain dari BST yang menyeimbangkan diri dengan aturan pewarnaan.</li>
                <li><strong>B-tree dan B+ tree:</strong> Digunakan dalam database dan sistem file untuk menyimpan data dalam jumlah besar.</li>
                <li><strong>Trie (Prefix tree):</strong> Digunakan untuk menyimpan dan mencari string dengan efisien.</li>
            </ul>
            <p><strong>Operasi Umum:</strong></p>
            <ul>
                <li><strong>Penyisipan:</strong> Menambahkan node baru ke pohon.</li>
                <li><strong>Penghapusan:</strong> Menghapus node dari pohon.</li>
                <li><strong>Traversal:</strong> Mengunjungi semua node dalam pohon (in-order, pre-order, post-order, level-order).</li>
                <li><strong>Pencarian:</strong> Mencari node dengan nilai tertentu.</li>
            </ul>
            <div class="code-block">
// Konsep Node dalam Binary Tree
class TreeNode_Code { // Renamed class
    constructor(data) {
        this.data = data;
        this.left = null;  // Pointer ke anak kiri
        this.right = null; // Pointer ke anak kanan
    }
}

// Contoh pembuatan Binary Tree sederhana
let root_code = new TreeNode_Code(10); // Renamed vars
root_code.left = new TreeNode_Code(5);
root_code.right = new TreeNode_Code(15);
root_code.left.left = new TreeNode_Code(3);
root_code.left.right = new TreeNode_Code(7);

// Contoh traversal In-order (Kiri -> Root -> Kanan)
function inOrderTraversal(node) {
    if (node === null) return;

    inOrderTraversal(node.left);  // Kunjungi subtree kiri
    console.log(node.data);       // Kunjungi node saat ini
    inOrderTraversal(node.right); // Kunjungi subtree kanan
}

// Contoh traversal Pre-order (Root -> Kiri -> Kanan)
function preOrderTraversal(node) {
    if (node === null) return;

    console.log(node.data);       // Kunjungi node saat ini
    preOrderTraversal(node.left);  // Kunjungi subtree kiri
    preOrderTraversal(node.right); // Kunjungi subtree kanan
}

// Contoh traversal Post-order (Kiri -> Kanan -> Root)
function postOrderTraversal(node) {
    if (node === null) return;

    postOrderTraversal(node.left);  // Kunjungi subtree kiri
    postOrderTraversal(node.right); // Kunjungi subtree kanan
    console.log(node.data);         // Kunjungi node saat ini
}
            </div>
        </div>
    </div>

    <!-- Binary Search Tree Section -->
    <div class="container">
        <h2>Binary Search Tree (BST)</h2>
        <div class="controls">
            <input type="text" id="bst-value" placeholder="Nilai">
            <button id="bst-add">Tambah Node</button>
            <button id="bst-remove">Hapus Node</button>
            <button id="bst-search">Cari Node</button>
            <button id="bst-reset">Reset BST</button>
        </div>
        <div class="visualization-area" id="bst-viz">
            <!-- Visualisasi BST akan muncul di sini -->
        </div>

        <!-- Area untuk visualisasi langkah-langkah -->
        <div class="step-visualization" id="bst-steps">
            <div class="step-title">Visualisasi Langkah-langkah</div>
            <div class="step-content" id="bst-step-content"></div>
            <div class="step-description" id="bst-step-description"></div>
            <div class="step-controls">
                <button class="step-btn" id="bst-prev-step">Langkah Sebelumnya</button>
                <button class="step-btn" id="bst-next-step">Langkah Selanjutnya</button>
                <button class="step-btn" id="bst-close-steps">Tutup</button>
            </div>
        </div>

        <div class="explanation">
            <h3>Penjelasan Binary Search Tree (BST)</h3>
            <p>Binary Search Tree (BST) adalah jenis binary tree khusus dengan properti pengurutan: untuk setiap node, semua nilai di subtree kiri lebih kecil dari nilai node, dan semua nilai di subtree kanan lebih besar dari nilai node.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Properti Pengurutan:</strong> Memungkinkan pencarian efisien dengan membandingkan nilai node.</li>
                <li><strong>Kompleksitas Rata-rata:</strong> Operasi pencarian, penyisipan, dan penghapusan memiliki kompleksitas O(log n) pada BST yang seimbang.</li>
                <li><strong>Kasus Terburuk:</strong> Jika tree tidak seimbang (misalnya semua node hanya memiliki anak kanan), kompleksitas dapat mencapai O(n).</li>
            </ul>
            <p><strong>Operasi Umum:</strong></p>
            <ul>
                <li><strong>Pencarian:</strong> Mencari node dengan nilai tertentu dengan membandingkan nilai yang dicari dengan nilai node saat ini.</li>
                <li><strong>Penyisipan:</strong> Menambahkan node baru pada posisi yang tepat sesuai properti BST.</li>
                <li><strong>Penghapusan:</strong> Menghapus node dari BST sambil mempertahankan properti BST.</li>
                <li><strong>Traversal:</strong> Mengunjungi semua node dalam urutan tertentu (in-order traversal pada BST menghasilkan urutan yang diurutkan).</li>
            </ul>
            <div class="code-block">
// Implementasi Binary Search Tree
class BSTNode_Code { // Renamed class
    constructor(data) {
        this.data = data;
        this.left = null;
        this.right = null;
    }
}

class BinarySearchTree_Code { // Renamed class
    constructor() {
        this.root = null;
    }

    // Menambahkan nilai baru ke BST
    insert(data) {
        const newNode = new BSTNode_Code(data);

        // Jika tree kosong, node baru menjadi root
        if (this.root === null) {
            this.root = newNode;
            return;
        }

        // Fungsi helper untuk menyisipkan secara rekursif
        const insertNode = (node, newNode) => {
            // Jika nilai baru lebih kecil, sisipkan di subtree kiri
            if (newNode.data < node.data) {
                if (node.left === null) {
                    node.left = newNode;
                } else {
                    insertNode(node.left, newNode);
                }
            }
            // Jika nilai baru lebih besar, sisipkan di subtree kanan
            else {
                if (node.right === null) {
                    node.right = newNode;
                } else {
                    insertNode(node.right, newNode);
                }
            }
        };

        insertNode(this.root, newNode);
    }

    // Mencari nilai dalam BST
    search(data) {
        return this.searchNode(this.root, data);
    }

    searchNode(node, data) {
        // Basis: jika node null atau nilai ditemukan
        if (node === null) {
            return false;
        }

        if (data < node.data) {
            return this.searchNode(node.left, data);
        }
        else if (data > node.data) {
            return this.searchNode(node.right, data);
        }
        // Data ditemukan
        return true;
    }

    // In-order traversal (menghasilkan urutan terurut)
    inOrderTraversal(callback) {
        this.inOrderTraversalNode(this.root, callback);
    }

    inOrderTraversalNode(node, callback) {
        if (node !== null) {
            this.inOrderTraversalNode(node.left, callback);
            callback(node.data);
            this.inOrderTraversalNode(node.right, callback);
        }
    }
}

// Contoh penggunaan
const bst_code = new BinarySearchTree_Code(); // Renamed var
bst_code.insert(15);
bst_code.insert(10);
bst_code.insert(20);
bst_code.insert(8);
bst_code.insert(12);
bst_code.insert(18);
bst_code.insert(25);

// Output nilai dalam urutan terurut
bst_code.inOrderTraversal(console.log); // Output: 8, 10, 12, 15, 18, 20, 25
            </div>
        </div>
    </div>

    <!-- AVL Tree Section -->
    <div class="container">
        <h2>AVL Tree</h2>
        <div class="controls">
            <input type="text" id="avl-value" placeholder="Nilai">
            <button id="avl-add">Tambah Node</button>
            <button id="avl-remove">Hapus Node</button>
            <button id="avl-reset">Reset AVL</button>
        </div>
        <div class="visualization-area" id="avl-viz">
            <!-- Visualisasi AVL Tree akan muncul di sini -->
        </div>

        <div class="explanation">
            <h3>Penjelasan AVL Tree</h3>
            <p>AVL Tree adalah Binary Search Tree (BST) yang menyeimbangkan diri sendiri. Namanya berasal dari pencipta pertamanya, Adelson-Velsky dan Landis. Pada AVL Tree, tinggi kedua subtree untuk setiap node berbeda maksimal 1.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Self-Balancing:</strong> Secara otomatis menjaga keseimbangan untuk memastikan operasi tetap efisien.</li>
                <li><strong>Faktor Keseimbangan:</strong> Untuk setiap node, perbedaan tinggi antara subtree kiri dan kanan tidak lebih dari 1.</li>
                <li><strong>Kompleksitas:</strong> Semua operasi (pencarian, penyisipan, penghapusan) dijamin O(log n).</li>
            </ul>
            <p><strong>Mekanisme Penyeimbangan:</strong></p>
            <ul>
                <li><strong>Rotasi Kiri:</strong> Digunakan ketika subtree kanan terlalu tinggi.</li>
                <li><strong>Rotasi Kanan:</strong> Digunakan ketika subtree kiri terlalu tinggi.</li>
                <li><strong>Rotasi Kiri-Kanan:</strong> Rotasi kiri pada anak kiri, diikuti rotasi kanan pada node.</li>
                <li><strong>Rotasi Kanan-Kiri:</strong> Rotasi kanan pada anak kanan, diikuti rotasi kiri pada node.</li>
            </ul>
            <div class="code-block">
// Implementasi dasar AVL Tree
class AVLNode_Code { // Renamed class
    constructor(data) {
        this.data = data;
        this.left = null;
        this.right = null;
        this.height = 1; // Tinggi node (digunakan untuk menghitung faktor keseimbangan)
    }
}

class AVLTree_Code { // Renamed class
    constructor() {
        this.root = null;
    }

    // Mendapatkan tinggi node
    height(node) {
        if (node === null) {
            return 0;
        }
        return node.height;
    }

    // Mendapatkan faktor keseimbangan
    getBalanceFactor(node) {
        if (node === null) {
            return 0;
        }
        return this.height(node.left) - this.height(node.right);
    }

    // Rotasi kanan
    rightRotate(y) {
        let x = y.left;
        let T2 = x.right;

        // Lakukan rotasi
        x.right = y;
        y.left = T2;

        // Perbarui tinggi
        y.height = Math.max(this.height(y.left), this.height(y.right)) + 1;
        x.height = Math.max(this.height(x.left), this.height(x.right)) + 1;

        // Kembalikan root baru
        return x;
    }

    // Rotasi kiri
    leftRotate(x) {
        let y = x.right;
        let T2 = y.left;

        // Lakukan rotasi
        y.left = x;
        x.right = T2;

        // Perbarui tinggi
        x.height = Math.max(this.height(x.left), this.height(x.right)) + 1;
        y.height = Math.max(this.height(y.left), this.height(y.right)) + 1;

        // Kembalikan root baru
        return y;
    }

    // Menyisipkan node ke AVL Tree
    insert(data) {
        this.root = this.insertNode(this.root, data);
    }

    insertNode(node, data) {
        // Penyisipan BST reguler
        if (node === null) {
            return new AVLNode_Code(data); // Use renamed class
        }

        if (data < node.data) {
            node.left = this.insertNode(node.left, data);
        } else if (data > node.data) {
            node.right = this.insertNode(node.right, data);
        } else {
            // Nilai duplikat tidak diperbolehkan
            return node;
        }

        // Perbarui tinggi node saat ini
        node.height = 1 + Math.max(
            this.height(node.left),
            this.height(node.right)
        );

        // Dapatkan faktor keseimbangan
        let balance = this.getBalanceFactor(node);

        // Kasus-kasus ketidakseimbangan

        // Kasus Kiri-Kiri
        if (balance > 1 && data < node.left.data) {
            return this.rightRotate(node);
        }

        // Kasus Kanan-Kanan
        if (balance < -1 && data > node.right.data) {
            return this.leftRotate(node);
        }

        // Kasus Kiri-Kanan
        if (balance > 1 && data > node.left.data) {
            node.left = this.leftRotate(node.left);
            return this.rightRotate(node);
        }

        // Kasus Kanan-Kiri
        if (balance < -1 && data < node.right.data) {
            node.right = this.rightRotate(node.right);
            return this.leftRotate(node);
        }

        // Node sudah seimbang
        return node;
    }
}
            </div>
        </div>
    </div>

    <!-- Trie (Prefix Tree) Section -->
    <div class="container">
        <h2>Trie (Prefix Tree)</h2>
        <div class="controls">
            <input type="text" id="trie-word" placeholder="Kata">
            <button id="trie-add">Tambah Kata</button>
            <button id="trie-search">Cari Kata</button>
            <button id="trie-prefix">Cari Awalan</button>
            <button id="trie-reset">Reset Trie</button>
        </div>
        <div class="visualization-area" id="trie-viz">
            <!-- Visualisasi Trie akan muncul di sini -->
        </div>

        <div class="explanation">
            <h3>Penjelasan Trie (Prefix Tree)</h3>
            <p>Trie adalah struktur data khusus yang digunakan untuk menyimpan kumpulan string dengan efisien. Nama "trie" berasal dari kata retrieval. Trie memungkinkan pencarian prefix dengan cepat, ideal untuk operasi autocomplete dan pemeriksaan kata.</p>
            <p><strong>Karakteristik Utama:</strong></p>
            <ul>
                <li><strong>Pencarian Efisien:</strong> Kompleksitas pencarian, penyisipan, dan penghapusan adalah O(m) di mana m adalah panjang string.</li>
                <li><strong>Efisien dalam Ruang:</strong> Prefix umum disimpan hanya sekali.</li>
                <li><strong>Pencarian Prefix:</strong> Sangat efisien untuk mencari semua kata dengan prefix tertentu.</li>
            </ul>
            <p><strong>Aplikasi:</strong></p>
            <ul>
                <li><strong>Autocomplete:</strong> Dalam aplikasi pencarian dan keyboard prediktif.</li>
                <li><strong>Spell Checker:</strong> Untuk verifikasi ejaan.</li>
                <li><strong>Routing IP:</strong> Dalam networking untuk pencarian alamat IP.</li>
                <li><strong>Kompresi Data:</strong> Sebagai bagian dari algoritma kompresi tertentu.</li>
            </ul>
            <div class="code-block">
// Implementasi Trie (Prefix Tree)
class TrieNode_Code { // Renamed class
    constructor() {
        this.children = {}; // Map untuk menyimpan node anak berdasarkan karakter
        this.isEndOfWord = false; // Menandai apakah node ini adalah akhir dari kata
    }
}

class Trie_Code { // Renamed class
    constructor() {
        this.root = new TrieNode_Code(); // Use renamed class
    }

    // Menyisipkan kata ke dalam trie
    insert(word) {
        let current = this.root;

        for (let char of word) {
            // Jika tidak ada jalur untuk karakter ini, buat node baru
            if (!current.children[char]) {
                current.children[char] = new TrieNode_Code(); // Use renamed class
            }

            // Pindah ke node anak
            current = current.children[char];
        }

        // Tandai node terakhir sebagai akhir kata
        current.isEndOfWord = true;
    }

    // Mencari kata dalam trie
    search(word) {
        let current = this.root;

        for (let char of word) {
            // Jika tidak ada jalur untuk karakter ini, kata tidak ada
            if (!current.children[char]) {
                return false;
            }

            // Pindah ke node anak
            current = current.children[char];
        }

        // Periksa apakah ini akhir kata
        return current.isEndOfWord;
    }

    // Memeriksa apakah ada kata yang dimulai dengan prefix tertentu
    startsWith(prefix) {
        let current = this.root;

        for (let char of prefix) {
            // Jika tidak ada jalur untuk karakter ini, prefix tidak ada
            if (!current.children[char]) {
                return false;
            }

            // Pindah ke node anak
            current = current.children[char];
        }

        // Prefix ditemukan
        return true;
    }

    // Mendapatkan semua kata dengan prefix tertentu
    getWordsWithPrefix(prefix) {
        const result = [];
        let current = this.root;

        // Navigasi ke node yang sesuai dengan prefix
        for (let char of prefix) {
            if (!current.children[char]) {
                return result; // Prefix tidak ditemukan
            }
            current = current.children[char];
        }

        // Fungsi helper untuk mengumpulkan semua kata
        const findAllWords = (node, word) => {
            if (node.isEndOfWord) {
                result.push(word);
            }

            for (let char in node.children) {
                findAllWords(node.children[char], word + char);
            }
        };

        // Mulai pencarian dari node terakhir dari prefix
        findAllWords(current, prefix);
        return result;
    }
}

// Contoh penggunaan
const trie_code = new Trie_Code(); // Renamed var

// Sisipkan beberapa kata
trie_code.insert("apple");
trie_code.insert("app");
trie_code.insert("application");
trie_code.insert("banana");

console.log(trie_code.search("apple"));       // true
console.log(trie_code.search("app"));         // true
console.log(trie_code.search("orange"));      // false
console.log(trie_code.startsWith("app"));     // true
console.log(trie_code.startsWith("ban"));     // true
console.log(trie_code.startsWith("car"));     // false

// Mendapatkan semua kata dengan prefix "app"
console.log(trie_code.getWordsWithPrefix("app")); // ["app", "apple", "application"]
            </div>
        </div>
    </div>

    <!-- Hapus script yang terpisah -->
    <!-- <script> ... </script> -->
    <!-- <script> ... </script> -->

    <script>
        // --- Variabel Global & Struktur Data Utama ---
        let simArray = [];
        let simList = null; // { head: null, tail: null }
        let simDoublyList = null; // { head: null, tail: null }
        let simCircularList = null; // { head: null, tail: null }
        let simStack = [];
        let simQueue = [];
        let simBinaryTree = null; // root node
        let simBST = null; // root node
        let simAVL = null; // root node (placeholder)
        let simTrie = null; // root node (placeholder)

        // Variabel untuk visualisasi step-by-step (bisa diperluas)
        let currentStepIndex = 0;
        let visualizationSteps = [];
        let currentListStepIndex = 0;
        let listVisualizationSteps = [];
        let currentCircularStepIndex = 0;
        let circularVisualizationSteps = [];
        let currentDoublyStepIndex = 0; // Added for consistency
        let doublyVisualizationSteps = []; // Added for consistency

        // --- Class Definisi ---
        class ListNode {
            constructor(data) {
                this.data = data;
                this.next = null;
            }
        }

        class DoublyListNode {
            constructor(data) {
                this.data = data;
                this.next = null;
                this.prev = null;
            }
        }

        class CircularListNode {
            constructor(data) {
                this.data = data;
                this.next = null;
            }
        }

        class TreeNode {
             constructor(data) {
                 // Coba parse sebagai angka, jika gagal, simpan sebagai string
                 const numData = parseInt(data);
                 this.data = isNaN(numData) ? data : numData;
                 this.left = null;
                 this.right = null;
             }
         }

        class BSTNode { // Sama dengan TreeNode tapi khusus untuk BST
            constructor(data) {
                 const numData = parseInt(data);
                 this.data = isNaN(numData) ? data : numData;
                 this.left = null;
                 this.right = null;
             }
        }

        // Placeholder classes for AVL and Trie simulation logic
        class AVLNode {
             constructor(data) {
                 const numData = parseInt(data);
                 this.data = isNaN(numData) ? data : numData;
                 this.left = null;
                 this.right = null;
                 this.height = 1;
             }
        }

        class TrieNode {
             constructor() {
                 this.children = {};
                 this.isEndOfWord = false;
             }
        }


        // --- Fungsi Helper Visualisasi Umum ---
        function createVisualElement(value, type = 'element', index = -1) {
            const element = document.createElement('div');
            element.textContent = value;

            if (type === 'array') {
                element.classList.add('element');
                const indexLabel = document.createElement('div');
                indexLabel.classList.add('index-label');
                indexLabel.textContent = `[${index}]`;
                element.appendChild(indexLabel);
            } else if (type === 'stack') {
                element.classList.add('stack-element');
            } else if (type === 'queue') {
                 element.classList.add('queue-element');
            } else if (type === 'tree' || type === 'bst' || type === 'avl') {
                 element.classList.add('element'); // Use basic element style for tree nodes
                 element.style.position = 'absolute'; // Required for positioning
                 if (type === 'tree') element.style.backgroundColor = "#34a853"; // Green for Tree
                 if (type === 'bst') element.style.backgroundColor = "#4285f4"; // Blue for BST
                 if (type === 'avl') element.style.backgroundColor = "#ea4335"; // Red for AVL (placeholder)
            } else if (type === 'trie') {
                element.classList.add('element');
                element.style.position = 'absolute';
                element.style.backgroundColor = "#fbbc05"; // Yellow for Trie
                element.style.width = '30px'; // Smaller nodes for Trie
                element.style.height = '30px';
                element.style.fontSize = '12px';
                if (value === '*') element.style.backgroundColor = '#aaa'; // End of word marker
            }


            // Animasi muncul (kecuali tree nodes yang posisinya absolut)
            if (type !== 'tree' && type !== 'bst' && type !== 'avl' && type !== 'trie') {
                element.style.opacity = '0';
                element.style.transform = 'scale(0.5)';
                requestAnimationFrame(() => {
                     setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'scale(1)';
                     }, 50);
                });
            }
            return element;
        }

        function createListNodeVisual(value) {
            const listNode = document.createElement('div');
            listNode.classList.add('list-node'); // Applies flex display
            listNode.style.position = 'relative'; // Ensure relative positioning for absolute child positioning

            const nodeValue = document.createElement('div');
            nodeValue.classList.add('node-value');
            nodeValue.textContent = value;

            const nodePointer = document.createElement('div');
            nodePointer.classList.add('node-pointer');

            listNode.appendChild(nodeValue);
            listNode.appendChild(nodePointer);

            // Animasi
            listNode.style.opacity = '0';
            listNode.style.transform = 'translateX(-20px)';
             requestAnimationFrame(() => {
                 setTimeout(() => {
                    listNode.style.opacity = '1';
                    listNode.style.transform = 'translateX(0)';
                 }, 50);
             });
            return listNode;
        }

        function createDoublyListNodeVisual(value, index = null, isHead = false, isTail = false) {
            const nodeContainer = document.createElement('div');
            nodeContainer.classList.add('step-node');
            nodeContainer.style.position = 'relative';

            const listNode = document.createElement('div');
            listNode.classList.add('list-node');
            listNode.style.position = 'relative';

            const nodeValue = document.createElement('div');
            nodeValue.classList.add('node-value');
            nodeValue.textContent = value;

            // Create prev pointer (left, bottom position)
            const prevPointer = document.createElement('div');
            prevPointer.classList.add('node-pointer');
            prevPointer.style.backgroundColor = 'black'; // Set the entire pointer background to black

            // Create an arrowhead element for prev pointer - using a custom triangle instead of character
            const prevArrowhead = document.createElement('div');
            prevArrowhead.style.width = '0';
            prevArrowhead.style.height = '0';
            prevArrowhead.style.borderTop = '6px solid transparent';
            prevArrowhead.style.borderBottom = '6px solid transparent';
            prevArrowhead.style.borderRight = '10px solid black';
            prevArrowhead.style.display = 'inline-block';
            prevArrowhead.style.position = 'absolute';
            prevArrowhead.style.left = '-10px'; // Position the arrowhead at the left end
            prevArrowhead.style.top = '-4px'; // Adjust vertical position

            // Add arrowhead to prev pointer
            prevPointer.appendChild(prevArrowhead);

            // Create next pointer (right, top position)
            const nextPointer = document.createElement('div');
            nextPointer.classList.add('node-pointer');
            nextPointer.style.backgroundColor = 'black'; // Set the entire pointer background to black

            // Create an arrowhead element for next pointer - using a custom triangle instead of character
            const nextArrowhead = document.createElement('div');
            nextArrowhead.style.width = '0';
            nextArrowhead.style.height = '0';
            nextArrowhead.style.borderTop = '6px solid transparent';
            nextArrowhead.style.borderBottom = '6px solid transparent';
            nextArrowhead.style.borderLeft = '10px solid black';
            nextArrowhead.style.display = 'inline-block';
            nextArrowhead.style.position = 'absolute';
            nextArrowhead.style.right = '-10px'; // Position the arrowhead at the right end
            nextArrowhead.style.top = '-4px'; // Adjust vertical position

            // Add arrowhead to next pointer
            nextPointer.appendChild(nextArrowhead);

            // Add all elements to the list node
            listNode.appendChild(prevPointer);
            listNode.appendChild(nodeValue);
            listNode.appendChild(nextPointer);

            nodeContainer.appendChild(listNode);

            // Add index label if provided
            if (index !== null) {
                const indexLabel = document.createElement('div');
                indexLabel.classList.add('node-index');
                indexLabel.textContent = `[${index}]`;
                nodeContainer.appendChild(indexLabel);
            }

            // Add Head and Tail labels if needed
            if (isHead || isTail) {
                // When node is both head and tail (single node case)
                if (isHead && isTail) {
                    const combinedLabel = document.createElement('div');
                    combinedLabel.classList.add('node-label');
                    combinedLabel.textContent = 'Head/Tail';
                    combinedLabel.style.position = 'absolute';
                    combinedLabel.style.top = '-25px';
                    combinedLabel.style.left = '50%';
                    combinedLabel.style.transform = 'translateX(-50%)';
                    combinedLabel.style.fontSize = '12px';
                    combinedLabel.style.fontWeight = 'bold';
                    combinedLabel.style.color = '#9C27B0'; // Purple for combined label
                    nodeContainer.appendChild(combinedLabel);
                } else {
                    // Separate labels for head or tail
                    if (isHead) {
                        const headLabel = document.createElement('div');
                        headLabel.classList.add('node-label');
                        headLabel.textContent = 'Head';
                        headLabel.style.position = 'absolute';
                        headLabel.style.top = '-25px';
                        headLabel.style.left = '50%';
                        headLabel.style.transform = 'translateX(-50%)';
                        headLabel.style.fontSize = '12px';
                        headLabel.style.fontWeight = 'bold';
                        headLabel.style.color = '#2196F3';
                        nodeContainer.appendChild(headLabel);
                    }

                    if (isTail) {
                        const tailLabel = document.createElement('div');
                        tailLabel.classList.add('node-label');
                        tailLabel.textContent = 'Tail';
                        tailLabel.style.position = 'absolute';
                        tailLabel.style.top = '-25px';
                        tailLabel.style.left = '50%';
                        tailLabel.style.transform = 'translateX(-50%)';
                        tailLabel.style.fontSize = '12px';
                        tailLabel.style.fontWeight = 'bold';
                        tailLabel.style.color = '#FF5722';
                        nodeContainer.appendChild(tailLabel);
                    }
                }
            }

            // Animasi
            listNode.style.opacity = '0';
            listNode.style.transform = 'translateX(-20px)';
            requestAnimationFrame(() => {
                setTimeout(() => {
                    listNode.style.opacity = '1';
                    listNode.style.transform = 'translateX(0)';
                }, 50);
            });

            return nodeContainer;
        }

        function createCircularListNodeVisual(value, index = null, isHead = false, isTail = false) {
            const nodeContainer = document.createElement('div');
            nodeContainer.classList.add('step-node');
            nodeContainer.style.position = 'relative';

            const listNode = document.createElement('div');
            listNode.classList.add('list-node'); // Applies flex display
            listNode.style.position = 'relative'; // Ensure relative positioning

            const nodeValue = document.createElement('div');
            nodeValue.classList.add('node-value');
            nodeValue.textContent = value;

            const nodePointer = document.createElement('div');
            nodePointer.classList.add('node-pointer');

            // Initialize with a line and arrowhead
            nodePointer.style.display = 'flex';
            nodePointer.style.alignItems = 'center';

            // Create a line element
            const line = document.createElement('div');
            line.style.height = '0px';
            line.style.width = '35px';
            line.style.backgroundColor = 'black';
            line.style.display = 'inline-block';

            // Create an arrowhead element
            const arrowhead = document.createElement('div');
            arrowhead.innerHTML = '▶';
            arrowhead.style.fontSize = '14px';
            arrowhead.style.color = 'black';
            arrowhead.style.display = 'inline-block';
            // arrowhead.style.marginLeft = '8px';
            arrowhead.style.marginRight = '-2px';
            arrowhead.style.transform = 'scaleY(1)';

            // Add both to the pointer
            nodePointer.appendChild(line);
            nodePointer.appendChild(arrowhead);

            // Only add the node value to the list node
            listNode.appendChild(nodeValue);

            // Add the pointer directly to the list node (not inside the flex container)
            // This allows absolute positioning relative to the list node
            listNode.appendChild(nodePointer);

            nodeContainer.appendChild(listNode);

            // Add index label if provided
            if (index !== null) {
                const indexLabel = document.createElement('div');
                indexLabel.classList.add('node-index');
                indexLabel.textContent = `[${index}]`;
                nodeContainer.appendChild(indexLabel);
            }

            // Add Head and Tail labels with positioning logic to prevent overlap
            if (isHead || isTail) {
                // When node is both head and tail (single node case)
                if (isHead && isTail) {
                    const combinedLabel = document.createElement('div');
                    combinedLabel.classList.add('node-label');
                    combinedLabel.textContent = 'Head/Tail';
                    combinedLabel.style.position = 'absolute';
                    combinedLabel.style.top = '-25px';
                    combinedLabel.style.left = '50%';
                    combinedLabel.style.transform = 'translateX(-50%)';
                    combinedLabel.style.fontSize = '12px';
                    combinedLabel.style.fontWeight = 'bold';
                    combinedLabel.style.color = '#9C27B0'; // Purple for combined label
                    nodeContainer.appendChild(combinedLabel);
                } else {
                    // Separate labels for head or tail
                    if (isHead) {
                        const headLabel = document.createElement('div');
                        headLabel.classList.add('node-label');
                        headLabel.textContent = 'Head';
                        headLabel.style.position = 'absolute';
                        headLabel.style.top = '-25px';
                        headLabel.style.left = '50%';
                        headLabel.style.transform = 'translateX(-50%)';
                        headLabel.style.fontSize = '12px';
                        headLabel.style.fontWeight = 'bold';
                        headLabel.style.color = '#4CAF50';
                        nodeContainer.appendChild(headLabel);
                    }

                    if (isTail) {
                        const tailLabel = document.createElement('div');
                        tailLabel.classList.add('node-label');
                        tailLabel.textContent = 'Tail';
                        tailLabel.style.position = 'absolute';
                        tailLabel.style.top = '-25px';
                        tailLabel.style.left = '50%';
                        tailLabel.style.transform = 'translateX(-50%)';
                        tailLabel.style.fontSize = '12px';
                        tailLabel.style.fontWeight = 'bold';
                        tailLabel.style.color = '#FF5722';
                        nodeContainer.appendChild(tailLabel);
                    }
                }
            }

            return nodeContainer;
        }

        // --- Fungsi Render Spesifik Struktur Data ---

        // Render Array
        function renderArray(arrayViz) {
            if (!arrayViz) return;
            arrayViz.innerHTML = '';
            simArray.forEach((value, index) => {
                if (value !== undefined && value !== null) {
                    const element = createVisualElement(value, 'array', index);
                    arrayViz.appendChild(element);
                }
            });
        }

        // Render Linked List
        function renderList(listViz) {
            if (!listViz) return;
            listViz.innerHTML = '';
            let current = simList ? simList.head : null;
            let count = 0;
            const maxNodes = 10;

            if (!current) {
                 listViz.textContent = 'List Kosong';
                 listViz.style.justifyContent = 'center'; // Center text when empty
                 return;
            }
            listViz.style.justifyContent = 'flex-start'; // Align left when not empty

            while (current !== null && count < maxNodes) {
                const nodeVisual = createListNodeVisual(current.data);
                listViz.appendChild(nodeVisual);
                if (current.next === null) {
                    const pointer = nodeVisual.querySelector('.node-pointer');
                    if(pointer) pointer.style.display = 'none';
                }
                current = current.next;
                count++;
            }
             if (current !== null && count >= maxNodes) {
                 const ellipsis = document.createElement('div');
                 ellipsis.textContent = '...';
                 ellipsis.style.margin = '0 10px';
                 listViz.appendChild(ellipsis);
             }
        }

        // Render Doubly Linked List
        function renderDoublyList(doublyListViz) {
            if (!doublyListViz) return;
            doublyListViz.innerHTML = '';
            let current = simDoublyList ? simDoublyList.head : null;
            let count = 0;
            const maxNodes = 10;

            if (!current) {
                 doublyListViz.textContent = 'List Kosong';
                 doublyListViz.style.justifyContent = 'center';
                 return;
            }
            doublyListViz.style.justifyContent = 'flex-start';

            // Create a wrapper for the entire visualization
            const visualWrapper = document.createElement('div');
            visualWrapper.style.position = 'relative';
            visualWrapper.style.width = '100%';

            // Create a container for the nodes
            const circleContainer = document.createElement('div');
            circleContainer.style.display = 'flex';
            circleContainer.style.alignItems = 'center';
            circleContainer.style.justifyContent = 'center';
            circleContainer.style.padding = '30px 20px';
            circleContainer.style.borderRadius = '10px';

            // Add nodes to the container
            let index = 0;
            while (current !== null && count < maxNodes) {
                // Create node visual with index and head/tail labels
                const isHead = (current === simDoublyList.head);
                const isTail = (current === simDoublyList.tail);
                const nodeVisual = createDoublyListNodeVisual(current.data, index, isHead, isTail);

                // Get the actual list-node element inside the container
                const listNodeElement = nodeVisual.querySelector('.list-node');

                // Get the pointers
                const pointers = listNodeElement.querySelectorAll('.node-pointer');

                // Hide pointers at list ends
                if (current.prev === null) pointers[0].style.display = 'none'; // Hide prev pointer for head
                if (current.next === null) pointers[1].style.display = 'none'; // Hide next pointer for tail

                circleContainer.appendChild(nodeVisual);
                current = current.next;
                count++;
                index++;
            }

            if (current !== null && count >= maxNodes) {
                const ellipsis = document.createElement('div');
                ellipsis.textContent = '...';
                ellipsis.style.margin = '0 20px';
                ellipsis.style.fontSize = '20px';
                ellipsis.style.fontWeight = 'bold';
                circleContainer.appendChild(ellipsis);
            }

            visualWrapper.appendChild(circleContainer);

            // Add the wrapper to the visualization area
            doublyListViz.appendChild(visualWrapper);
        }

        // Render Circular Linked List
        function renderCircularList(circularListViz) {
            if (!circularListViz) return;
            circularListViz.innerHTML = '';

            if (!simCircularList || !simCircularList.head) {
                circularListViz.textContent = 'List Kosong';
                circularListViz.style.justifyContent = 'center';
                return;
            }
            circularListViz.style.justifyContent = 'flex-start';


            let current = simCircularList.head;
            let count = 0;
            const maxNodes = 10;
            const circleContainer = document.createElement('div');
            circleContainer.style.display = 'flex';
            circleContainer.style.alignItems = 'center';
            circleContainer.style.justifyContent = 'center';
            circleContainer.style.padding = '30px 20px';
            circleContainer.style.borderRadius = '10px';

            do {
                if (count >= maxNodes) {
                    const ellipsis = document.createElement('div');
                    ellipsis.textContent = '...';
                    ellipsis.style.margin = '0 20px';
                    ellipsis.style.fontSize = '20px';
                    ellipsis.style.fontWeight = 'bold';
                    circleContainer.appendChild(ellipsis);
                    break;
                }

                // Create node visual with index and head/tail labels
                const isHead = (current === simCircularList.head);
                const isTail = (current === simCircularList.tail);
                const nodeVisual = createCircularListNodeVisual(current.data, count, isHead, isTail);

                // Get the actual list-node element inside the container
                const listNodeElement = nodeVisual.querySelector('.list-node');

                // Hide pointer for the last node in the visualization (which points back to head)
                // We'll show this with the curved arrow instead
                if (current === simCircularList.tail && count < maxNodes) {
                    const pointer = listNodeElement.querySelector('.node-pointer');
                    if (pointer) pointer.style.display = 'none';
                }

                circleContainer.appendChild(nodeVisual);
                current = current.next;
                count++;
            } while (current !== simCircularList.head && count < maxNodes + 1); // Allow one extra to detect loop

            // Add return arrow if it's a loop and not truncated
            if (count > 0 && count <= maxNodes && simCircularList.tail.next === simCircularList.head) {
                // Create a wrapper for the entire visualization including the curved arrow
                const visualWrapper = document.createElement('div');
                visualWrapper.style.position = 'relative';
                visualWrapper.style.width = '100%';
                visualWrapper.style.paddingBottom = '60px'; // Make space for the curved arrow below

                // Move the existing nodes into this wrapper
                visualWrapper.appendChild(circleContainer);

                // Create a curved arrow using SVG
                const svgNS = "http://www.w3.org/2000/svg";
                const svg = document.createElementNS(svgNS, "svg");
                svg.setAttribute("width", "100%");
                svg.setAttribute("height", "60");
                svg.style.position = "absolute";
                svg.style.bottom = "0";
                svg.style.left = "0";
                svg.style.overflow = "visible";

                // Calculate the path for a curved arrow
                // Start from the last node (minus some offset to account for the node's width)
                // and curve down and back to the first node
                const path = document.createElementNS(svgNS, "path");

                // The path will be a curve from right to left, below the nodes
                // Start at the left side (head node), curve down and right, then up to the right side (tail node)
                // We're using absolute coordinates instead of percentages to avoid parsing errors
                const svgWidth = circleContainer.offsetWidth || 800; // Fallback width if not available yet

                // Calculate start and end positions based on the number of nodes
                const nodeCount = count;

                // Get the index labels for head and tail nodes to position the curve endpoints
                const indexLabels = circleContainer.querySelectorAll('.node-index');
                const headIndexLabel = indexLabels[0]; // First index label (head)
                const tailIndexLabel = indexLabels[nodeCount - 1]; // Last index label (tail)

                // Default positions for the curve endpoints
                let startX, endX;

                if (nodeCount === 1) {
                    // For a single node, create a wider curve centered under the node
                    startX = svgWidth / 2 - 100; // Left of the single node
                    endX = svgWidth / 2 + 100;   // Right of the single node
                } else {
                    // For multiple nodes, calculate positions based on the node count and container width
                    // Estimate the width of each node plus its margin
                    const nodeWidth = 70; // Node width + margin
                    const totalNodesWidth = nodeCount * nodeWidth;

                    // Calculate the starting position - center the nodes in the container
                    const leftMargin = (svgWidth - totalNodesWidth) / 2;

                    // If the list is too wide, adjust the margins
                    if (leftMargin < 20) {
                        // Not enough space to center, use minimum margins
                        startX = 20; // Minimum left margin
                        endX = svgWidth - 20; // Minimum right margin
                    } else {
                        // Enough space to center the list
                        startX = leftMargin + (nodeWidth / 2); // Center of first node
                        endX = leftMargin + totalNodesWidth - (nodeWidth / 2); // Center of last node
                    }
                }

                // Adjust the curve to point to the index labels instead of the nodes
                // The curve will now go from the tail index to the head index
                // We're swapping start and end points to make the arrow point to the head
                const curveY = 40; // How far down the curve goes - aligned with index labels

                // Create the path with the curve going from tail to head (reversed)
                // Adjust the starting point (tail end) to be more to the right by about one node width
                const nodeWidth = 70; // Approximate width of a node
                const adjustedEndX = endX + nodeWidth; // Move the tail end more to the right

                path.setAttribute("d", `M ${adjustedEndX} 20 C ${adjustedEndX} ${curveY + 20}, ${startX} ${curveY + 20}, ${startX} 20`);
                path.setAttribute("fill", "none");
                path.setAttribute("stroke", "#ff5722");
                path.setAttribute("stroke-width", "3");

                // Create a unique ID for the arrowhead to avoid conflicts
                const arrowheadId = "circular-arrowhead-" + Math.random().toString(36).substr(2, 9);

                // Add arrowhead marker definition
                const defs = document.createElementNS(svgNS, "defs");
                const marker = document.createElementNS(svgNS, "marker");
                marker.setAttribute("id", arrowheadId);
                marker.setAttribute("markerWidth", "10");
                marker.setAttribute("markerHeight", "7");
                marker.setAttribute("refX", "10");
                marker.setAttribute("refY", "3.5");
                marker.setAttribute("orient", "auto");

                const polygon = document.createElementNS(svgNS, "polygon");
                polygon.setAttribute("points", "10 0, 0 3.5, 10 7");
                polygon.setAttribute("fill", "#ff5722");

                marker.appendChild(polygon);
                defs.appendChild(marker);
                svg.appendChild(defs);

                // Apply the arrowhead to the path at the end (pointing to head)
                path.setAttribute("marker-end", "url(#" + arrowheadId + ")");

                // Add a text label for the curved arrow
                const text = document.createElementNS(svgNS, "text");
                text.setAttribute("x", "50%");
                text.setAttribute("y", "40");
                text.setAttribute("text-anchor", "middle");
                text.setAttribute("fill", "#666");
                text.setAttribute("font-size", "12px");
                text.setAttribute("font-weight", "bold");
                text.textContent = "kembali ke head";

                svg.appendChild(path);
                svg.appendChild(text);
                visualWrapper.appendChild(svg);

                // Replace the original circleContainer with our new wrapper
                circularListViz.appendChild(visualWrapper);
                return; // Exit early since we've already appended to the parent
            }

            circularListViz.appendChild(circleContainer);
        }


        // Render Stack
        function renderStack(stackVizContainer) {
            if (!stackVizContainer) return;
            stackVizContainer.innerHTML = '';
            simStack.forEach(value => {
                const element = createVisualElement(value, 'stack');
                stackVizContainer.appendChild(element);
            });
        }

         // Render Queue
        function renderQueue(queueVizContainer) {
             if (!queueVizContainer) return;
             queueVizContainer.innerHTML = '';
            simQueue.forEach(value => {
                const element = createVisualElement(value, 'queue');
                queueVizContainer.appendChild(element);
            });
        }

        // --- Tree/BST Drawing Functions ---
         function drawTreeNode(vizElement, treeNode, x, y, level = 0, parentX = null, parentY = null, nodeType = 'tree') {
             if (!treeNode || !vizElement) return;

             const nodeRadius = (nodeType === 'trie') ? 15 : 25;
             const horizontalSpacingBase = (nodeType === 'trie') ? 50 : 70;
             const verticalSpacing = (nodeType === 'trie') ? 60 : 80;
             const horizontalSpacing = horizontalSpacingBase - level * ((nodeType === 'trie') ? 4 : 8); // Adjust spacing based on level

             // Create node
             const nodeElement = createVisualElement(treeNode.data, nodeType);
             nodeElement.style.left = `${x - nodeRadius}px`;
             nodeElement.style.top = `${y - nodeRadius}px`;
             vizElement.appendChild(nodeElement);

             // Create line to parent if exists
             if (parentX !== null && parentY !== null) {
                 const line = document.createElement("div");
                 line.className = 'tree-line'; // Use class for styling
                 const angle = Math.atan2(y - parentY, x - parentX);
                 const length = Math.sqrt(Math.pow(x - parentX, 2) + Math.pow(y - parentY, 2)) - nodeRadius; // Adjust length slightly
                 const startX = parentX + nodeRadius * Math.cos(angle); // Start from edge of parent
                 const startY = parentY + nodeRadius * Math.sin(angle); // Start from edge of parent

                 line.style.width = `${length}px`;
                 line.style.transform = `rotate(${angle}rad)`;
                 line.style.left = `${startX}px`;
                 line.style.top = `${startY}px`;
                 vizElement.appendChild(line);

                 // Add L/R label for BST
                 if (nodeType === 'bst') {
                     const labelElement = document.createElement("div");
                     labelElement.className = 'tree-label';
                     labelElement.textContent = (x < parentX) ? "L" : "R";
                     labelElement.style.left = `${(startX + x - nodeRadius * Math.cos(angle)) / 2 - 5}px`; // Position label near midpoint
                     labelElement.style.top = `${(startY + y - nodeRadius * Math.sin(angle)) / 2 - 8}px`;
                     vizElement.appendChild(labelElement);
                 }
             }

             // Draw children recursively
             const children = nodeType === 'trie' ? Object.entries(treeNode.children) : [['left', treeNode.left], ['right', treeNode.right]];
             let childCount = children.filter(([key, child]) => child).length;
             let angleBetweenChildren = childCount > 1 ? Math.PI / (childCount + 1) : Math.PI / 2; // Spread children

             let currentAngle = (Math.PI / 2) - (angleBetweenChildren * (childCount -1) / 2) ; // Start angle centered below


             children.forEach(([key, child], index) => {
                 if (child) {
                     let childX, childY;
                     if (nodeType === 'trie') {
                         // Simple horizontal layout for Trie for now
                         const childIndex = Object.keys(treeNode.children).indexOf(key);
                         const numChildren = Object.keys(treeNode.children).length;
                         childX = x + (childIndex - (numChildren - 1) / 2) * horizontalSpacing;
                         childY = y + verticalSpacing;
                         drawTreeNode(vizElement, child, childX, childY, level + 1, x, y, nodeType);

                     } else {
                         // Binary Tree layout
                         const offsetMultiplier = (key === 'left') ? -1 : 1;
                          // Adjust horizontal spacing based on depth and potentially subtree size (simplified here)
                         const dynamicHorizontalSpacing = Math.max(20, horizontalSpacing * Math.pow(0.8, level));
                         childX = x + offsetMultiplier * dynamicHorizontalSpacing;
                         childY = y + verticalSpacing;
                         drawTreeNode(vizElement, child, childX, childY, level + 1, x, y, nodeType);
                     }


                 }
             });
         }


         // Render Binary Tree
         function renderBinaryTree(treeViz) {
             if (!treeViz) return;
             treeViz.innerHTML = "";
             if (simBinaryTree) {
                 const vizWidth = treeViz.clientWidth;
                 const rootX = vizWidth / 2;
                 const rootY = 50;
                 drawTreeNode(treeViz, simBinaryTree, rootX, rootY, 0, null, null, 'tree');
                 treeViz.style.minHeight = `350px`;
             } else {
                 const emptyMessage = document.createElement("div");
                 emptyMessage.textContent = "Tree Kosong";
                 emptyMessage.style.color = "#888";
                 emptyMessage.style.textAlign = "center";
                 emptyMessage.style.width = "100%";
                 emptyMessage.style.padding = "40px 0";
                 treeViz.appendChild(emptyMessage);
                 treeViz.style.minHeight = "150px";
             }
         }

         // Render BST
         function renderBST(bstViz) {
             if (!bstViz) return;
             bstViz.innerHTML = "";
             if (simBST) {
                 const vizWidth = bstViz.clientWidth;
                 const rootX = vizWidth / 2;
                 const rootY = 50;
                 drawTreeNode(bstViz, simBST, rootX, rootY, 0, null, null, 'bst');
                 bstViz.style.minHeight = `350px`;
             } else {
                 const emptyMessage = document.createElement("div");
                 emptyMessage.textContent = "BST Kosong";
                 emptyMessage.style.color = "#888";
                 emptyMessage.style.textAlign = "center";
                 emptyMessage.style.width = "100%";
                 emptyMessage.style.padding = "40px 0";
                 bstViz.appendChild(emptyMessage);
                 bstViz.style.minHeight = "150px";
             }
         }

        // --- Logika & Operasi Struktur Data ---

        // Tree helper functions
        function countNodes(node) {
            if (!node) return 0;
            return 1 + countNodes(node.left) + countNodes(node.right);
        }
        function searchInTree(root, value) {
            if (!root) return false;
            // Handle both string and number comparison loosely
            if (root.data == value) return true;
            return searchInTree(root.left, value) || searchInTree(root.right, value);
        }
        function findMinTreeValue(node) { // Renamed to avoid conflict
            if (!node.left) return node.data;
            return findMinTreeValue(node.left);
        }
        function insertNodeToTree(root, newNode) {
            if (!root) return newNode;
            // Simple insertion: prefer left, then right, then recurse on less populated subtree
            if (!root.left) {
                root.left = newNode;
            } else if (!root.right) {
                root.right = newNode;
            } else {
                if (countNodes(root.left) <= countNodes(root.right)) {
                    root.left = insertNodeToTree(root.left, newNode);
                } else {
                    root.right = insertNodeToTree(root.right, newNode);
                }
            }
            return root;
        }
        function removeNodeFromTree(root, value) {
            if (!root) return null;
             // Find the node AND its parent to modify parent's link
             let parent = null;
             let q = [[root, null]]; // Queue stores [node, parent]
             let nodeToRemove = null;

             while(q.length > 0) {
                 let [current, p] = q.shift();
                 if (current.data == value) {
                     nodeToRemove = current;
                     parent = p;
                     break;
                 }
                 if(current.left) q.push([current.left, current]);
                 if(current.right) q.push([current.right, current]);
             }

             if (!nodeToRemove) return root; // Not found

            // Case 1 & 2: Node is leaf or has one child (Find replacement)
            let replacement = null;
            if (nodeToRemove.left) replacement = nodeToRemove.left;
            else if (nodeToRemove.right) replacement = nodeToRemove.right;

             // Case 3: Node has two children (Find successor, replace data, then remove successor)
             if (nodeToRemove.left && nodeToRemove.right) {
                  let successorParent = nodeToRemove;
                  let successor = nodeToRemove.right;
                  while (successor.left) {
                      successorParent = successor;
                      successor = successor.left;
                  }
                  nodeToRemove.data = successor.data; // Replace data

                  // Now, remove the successor node (which has at most one right child)
                   if (successorParent === nodeToRemove) { // Successor was the right child
                       successorParent.right = successor.right;
                   } else { // Successor was deeper in the left subtree of the right child
                       successorParent.left = successor.right;
                   }
                   return root; // Root itself doesn't change, only data/structure below
             }

             // Linking logic for Case 1 & 2
              if (!parent) { // Removing the root node
                 return replacement; // The replacement (or null) becomes the new root
              } else { // Removing a non-root node
                 if (parent.left === nodeToRemove) {
                     parent.left = replacement;
                 } else {
                     parent.right = replacement;
                 }
                 return root; // Root remains the same
              }
        }


        // BST helper functions
         function insertToBST(root, value) {
            const numValue = parseInt(value); // Ensure comparison works numerically
             if (isNaN(numValue)) {
                  alert("Nilai BST harus numerik.");
                  return root; // Return original root if value invalid
              }

             if (!root) {
                 return new BSTNode(numValue);
             }

             if (numValue < root.data) {
                 root.left = insertToBST(root.left, numValue);
             } else if (numValue > root.data) {
                 root.right = insertToBST(root.right, numValue);
             } // Ignore duplicates
             return root;
         }
         function findMinNodeBST(node) {
            let current = node;
            while (current && current.left !== null) current = current.left;
            return current;
         }
         function removeFromBST(root, value) {
             const numValue = parseInt(value);
             if (isNaN(numValue)) return root; // Ignore if not a number

             if (!root) return null;

             if (numValue < root.data) {
                 root.left = removeFromBST(root.left, numValue);
             } else if (numValue > root.data) {
                 root.right = removeFromBST(root.right, numValue);
             } else { // Node found
                 if (!root.left && !root.right) return null; // Leaf
                 if (!root.left) return root.right;          // One child (right)
                 if (!root.right) return root.left;         // One child (left)

                 // Two children
                 let successor = findMinNodeBST(root.right);
                 root.data = successor.data; // Replace data
                 root.right = removeFromBST(root.right, successor.data); // Remove successor
             }
             return root;
         }
         function searchBST(root, value) {
              const numValue = parseInt(value);
             if (isNaN(numValue)) return false;

             if (!root) return false;
             if (root.data === numValue) return true;
             return (numValue < root.data) ? searchBST(root.left, numValue) : searchBST(root.right, numValue);
         }

        // --- Inisialisasi dan Event Listeners (dalam DOMContentLoaded) ---
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM Loaded, initializing simulations...");

            // Get ALL DOM elements
            // Array
            const arrayViz = document.getElementById('array-viz');
            const arrayValueInput = document.getElementById('array-value');
            const arrayIndexInput = document.getElementById('array-index');
            const arrayAddBtn = document.getElementById('array-add');
            const arrayRemoveBtn = document.getElementById('array-remove');
            const arrayResetBtn = document.getElementById('array-reset');

            // Linked List
            const listViz = document.getElementById('list-viz');
            const listValueInput = document.getElementById('list-value');
            const listAddHeadBtn = document.getElementById('list-add-head');
            const listAddTailBtn = document.getElementById('list-add-tail');
            const listRemoveHeadBtn = document.getElementById('list-remove-head');
            const listRemoveTailBtn = document.getElementById('list-remove-tail');
            const listIndexInput = document.getElementById('list-index');
            const listAddMiddleBtn = document.getElementById('list-add-middle');
            const listRemoveMiddleBtn = document.getElementById('list-remove-middle');
            const listResetBtn = document.getElementById('list-reset');
            const listStepsContainer = document.getElementById('list-steps');
            const listStepContent = document.getElementById('list-step-content');
            const listStepDescription = document.getElementById('list-step-description');
            const listPrevStepBtn = document.getElementById('list-prev-step');
            const listNextStepBtn = document.getElementById('list-next-step');
            const listCloseStepsBtn = document.getElementById('list-close-steps');

            // Doubly Linked List
            const doublyListViz = document.getElementById('doubly-list-viz');
            const doublyListValueInput = document.getElementById('doubly-list-value');
            const doublyListAddHeadBtn = document.getElementById('doubly-list-add-head');
            const doublyListAddTailBtn = document.getElementById('doubly-list-add-tail');
            const doublyListRemoveHeadBtn = document.getElementById('doubly-list-remove-head');
            const doublyListRemoveTailBtn = document.getElementById('doubly-list-remove-tail');
            const doublyListIndexInput = document.getElementById('doubly-list-index');
            const doublyListAddMiddleBtn = document.getElementById('doubly-list-add-middle');
            const doublyListRemoveMiddleBtn = document.getElementById('doubly-list-remove-middle');
            const doublyListResetBtn = document.getElementById('doubly-list-reset');
            // Corrected references for Doubly Linked List step visualization
            const doublyStepsContainer = document.getElementById('doubly-list-steps');
            const doublyStepTitle = document.getElementById('doubly-step-title'); // Added reference
            const doublyStepContent = document.getElementById('doubly-step-content'); // Corrected ID
            const doublyStepDescription = document.getElementById('doubly-step-description'); // Corrected ID
            const doublyPrevStepBtn = document.getElementById('doubly-prev-step'); // Corrected ID
            const doublyNextStepBtn = document.getElementById('doubly-next-step'); // Corrected ID
            const doublyCloseStepsBtn = document.getElementById('doubly-close-steps'); // Corrected ID


            // Circular Linked List
            const circularListViz = document.getElementById('circular-list-viz');
            const circularListValueInput = document.getElementById('circular-list-value');
            const circularListAddHeadBtn = document.getElementById('circular-list-add-head');
            const circularListAddTailBtn = document.getElementById('circular-list-add-tail');
            const circularListRemoveHeadBtn = document.getElementById('circular-list-remove-head');
            const circularListRemoveTailBtn = document.getElementById('circular-list-remove-tail');
            const circularListIndexInput = document.getElementById('circular-list-index');
            const circularListAddMiddleBtn = document.getElementById('circular-list-add-middle');
            const circularListRemoveMiddleBtn = document.getElementById('circular-list-remove-middle');
            const circularListResetBtn = document.getElementById('circular-list-reset');
            const circularStepsContainer = document.getElementById('circular-list-steps');
            const circularStepContent = document.getElementById('circular-step-content');
            const circularStepDescription = document.getElementById('circular-step-description');
            const circularPrevStepBtn = document.getElementById('circular-prev-step');
            const circularNextStepBtn = document.getElementById('circular-next-step');
            const circularCloseStepsBtn = document.getElementById('circular-close-steps');

            // Stack
            const stackVizContainer = document.querySelector('#stack-viz .stack-container');
            const stackValueInput = document.getElementById('stack-value');
            const stackPushBtn = document.getElementById('stack-push');
            const stackPopBtn = document.getElementById('stack-pop');
            const stackResetBtn = document.getElementById('stack-reset');

            // Queue
            const queueVizContainer = document.querySelector('#queue-viz .queue-container');
            const queueValueInput = document.getElementById('queue-value');
            const queueEnqueueBtn = document.getElementById('queue-enqueue');
            const queueDequeueBtn = document.getElementById('queue-dequeue');
            const queueResetBtn = document.getElementById('queue-reset');

             // Tree
             const treeViz = document.getElementById("tree-viz");
             const treeValueInput = document.getElementById("tree-value");
             const treeAddBtn = document.getElementById("tree-add");
             const treeRemoveBtn = document.getElementById("tree-remove");
             const treeResetBtn = document.getElementById("tree-reset");

             // BST
             const bstViz = document.getElementById("bst-viz");
             const bstValueInput = document.getElementById("bst-value");
             const bstAddBtn = document.getElementById("bst-add");
             const bstRemoveBtn = document.getElementById("bst-remove");
             const bstSearchBtn = document.getElementById("bst-search");
             const bstResetBtn = document.getElementById("bst-reset");

             // AVL
             const avlViz = document.getElementById("avl-viz");
             const avlAddBtn = document.getElementById("avl-add");
             const avlRemoveBtn = document.getElementById("avl-remove");
             const avlResetBtn = document.getElementById("avl-reset");

             // Trie
             const trieViz = document.getElementById("trie-viz");
             const trieAddBtn = document.getElementById("trie-add");
             const trieSearchBtn = document.getElementById("trie-search");
             const triePrefixBtn = document.getElementById("trie-prefix");
             const trieResetBtn = document.getElementById("trie-reset");


            // Initial render calls
            renderArray(arrayViz);
            renderList(listViz);
            renderDoublyList(doublyListViz);
            renderCircularList(circularListViz);
            renderStack(stackVizContainer);
            renderQueue(queueVizContainer);
            renderBinaryTree(treeViz);
            renderBST(bstViz);

            // --- Event Listener Assignments ---

            // Array Listeners
            if (arrayAddBtn) arrayAddBtn.addEventListener('click', () => {
                const value = arrayValueInput.value.trim();
                const indexStr = arrayIndexInput.value.trim();
                if (value === '') return;
                if (indexStr === '') {
                    simArray.push(value);
                } else {
                    const index = parseInt(indexStr);
                    if (!isNaN(index) && index >= 0) {
                        simArray[index] = value;
                    } else { alert("Indeks tidak valid."); return; }
                }
                renderArray(arrayViz);
                arrayValueInput.value = ''; arrayIndexInput.value = '';
            });
            if (arrayRemoveBtn) arrayRemoveBtn.addEventListener('click', () => {
                const indexStr = arrayIndexInput.value.trim();
                if (indexStr === '') { alert("Masukkan indeks."); return; }
                const index = parseInt(indexStr);
                if (!isNaN(index) && index >= 0 && index < simArray.length && simArray[index] !== undefined) {
                     simArray.splice(index, 1); // Or delete simArray[index];
                     renderArray(arrayViz);
                     arrayIndexInput.value = '';
                } else { alert("Indeks tidak valid atau elemen tidak ditemukan."); }
            });
            if (arrayResetBtn) arrayResetBtn.addEventListener('click', () => {
                simArray = []; renderArray(arrayViz);
            });

            // Linked List Listeners
            function initList() { simList = { head: null, tail: null }; renderList(listViz); }
            if (listAddHeadBtn) listAddHeadBtn.addEventListener('click', () => {
                const value = listValueInput.value.trim(); if (value === '') return;
                const newNode = new ListNode(value);
                if (!simList || !simList.head) simList = { head: newNode, tail: newNode };
                else { newNode.next = simList.head; simList.head = newNode; }
                renderList(listViz); listValueInput.value = '';
            });
             if (listAddTailBtn) listAddTailBtn.addEventListener('click', () => {
                const value = listValueInput.value.trim(); if (value === '') return;
                const newNode = new ListNode(value);
                if (!simList || !simList.tail) simList = { head: newNode, tail: newNode };
                else { simList.tail.next = newNode; simList.tail = newNode; }
                renderList(listViz); listValueInput.value = '';
             });
             if (listRemoveHeadBtn) listRemoveHeadBtn.addEventListener('click', () => {
                 if (!simList || !simList.head) return;
                 simList.head = simList.head.next;
                 if (simList.head === null) simList.tail = null;
                 renderList(listViz);
             });
             if (listRemoveTailBtn) listRemoveTailBtn.addEventListener('click', () => {
                 if (!simList || !simList.head) return;
                 if (simList.head === simList.tail) simList = { head: null, tail: null };
                 else {
                     let current = simList.head;
                     while (current.next !== simList.tail) current = current.next;
                     current.next = null; simList.tail = current;
                 }
                 renderList(listViz);
             });
             if (listResetBtn) listResetBtn.addEventListener('click', initList);
             // Add Middle/Remove Middle Listeners (Simplified for brevity, retain original complex logic if needed)
             if (listAddMiddleBtn) listAddMiddleBtn.addEventListener('click', () => {
                 const value = listValueInput.value.trim();
                 const indexStr = listIndexInput.value.trim();

                 if (value === '') {
                     alert("Masukkan nilai untuk ditambahkan!");
                     return;
                 }

                 if (indexStr === '') {
                     alert("Masukkan indeks posisi untuk menambahkan node!");
                     return;
                 }

                 const index = parseInt(indexStr);

                 if (isNaN(index) || index < 0) {
                     alert("Indeks tidak valid!");
                     return;
                 }

                 // Validasi list kosong
                 if (!simList || !simList.head) {
                     if (index === 0) {
                         // Jika indeks 0 dan list kosong, tambahkan di awal
                         const newNode = new ListNode(value);
                         simList = { head: newNode, tail: newNode };
                         renderList(listViz);
                         listValueInput.value = '';
                         listIndexInput.value = '';
                         return;
                     } else {
                         alert("List kosong! Indeks harus 0.");
                         return;
                     }
                 }

                 // Validasi tambah di awal (indeks 0)
                 if (index === 0) {
                     const newNode = new ListNode(value);
                     newNode.next = simList.head;
                     simList.head = newNode;
                     renderList(listViz);
                     listValueInput.value = '';
                     listIndexInput.value = '';
                     return;
                 }

                 // Hitung panjang list untuk validasi indeks
                 let length = 0;
                 let temp = simList.head;
                 while (temp) {
                     length++;
                     temp = temp.next;
                 }

                 // Validasi tambah di akhir
                 if (index >= length) {
                     alert(`List hanya memiliki ${length} node! Indeks maksimal yang valid adalah ${length}.`);
                     return;
                 }

                 // Persiapkan visualisasi langkah-langkah
                 const newNode = new ListNode(value);
                 listVisualizationSteps = [];

                 // Langkah 1: Siapkan node baru
                 const step1 = {
                     content: buildStepListVisual(simList, { newNode: newNode }),
                     description: "Langkah 1: Siapkan node baru dengan nilai " + value
                 };
                 listVisualizationSteps.push(step1);

                 // Langkah 2: Cari posisi indeks-1 (node sebelum posisi target)
                 let current = simList.head;
                 let position = 0;

                 // Visualisasi pencarian posisi
                 while (position < index - 1) {
                     position++;
                     current = current.next;
                 }

                 const step2 = {
                     content: buildStepListVisual(simList, {
                         highlight: current,
                         newNode: newNode,
                         pointerLabels: { [current.data]: "current" }
                     }),
                     description: `Langkah 2: Temukan node pada posisi ke-${position} (sebelum posisi target)`
                 };
                 listVisualizationSteps.push(step2);

                 // Langkah 3: Atur pointer next dari node baru ke node setelah posisi target
                 newNode.next = current.next;

                 const step3 = {
                     content: buildStepListVisual(simList, {
                         highlight: current,
                         preInsert: true,
                         newNode: newNode,
                         newNodeNext: current.next,
                         pointerLabels: {
                             [current.data]: "current",
                             [newNode.data]: "newNode"
                         }
                     }),
                     description: "Langkah 3: Atur newNode.next menunjuk ke node setelah current"
                 };
                 listVisualizationSteps.push(step3);

                 // Langkah 4: Atur pointer next dari node current ke node baru
                 const tempNext = current.next;
                 current.next = newNode;

                 const step4 = {
                     content: buildStepListVisual(simList, {
                         highlight: current,
                         highlightNext: newNode,
                         pointerLabels: {
                             [current.data]: "current",
                             [newNode.data]: "newNode"
                         }
                     }),
                     description: "Langkah 4: Atur current.next menunjuk ke newNode. Node berhasil disisipkan!"
                 };
                 listVisualizationSteps.push(step4);

                 // Reset indeks visualisasi dan tampilkan
                 currentListStepIndex = 0;
                 showListStep(currentListStepIndex);
                 listStepsContainer.style.display = 'block';

                 // Reset input fields
                 listValueInput.value = '';
                 listIndexInput.value = '';
             });

             if (listRemoveMiddleBtn) listRemoveMiddleBtn.addEventListener('click', () => {
                 const indexStr = listIndexInput.value.trim();

                 if (indexStr === '') {
                     alert("Masukkan indeks node yang akan dihapus!");
                     return;
                 }

                 const index = parseInt(indexStr);

                 if (isNaN(index) || index < 0) {
                     alert("Indeks tidak valid!");
                     return;
                 }

                 // Validasi list kosong
                 if (!simList || !simList.head) {
                     alert("List kosong! Tidak ada yang bisa dihapus.");
                     return;
                 }

                 // Hitung panjang list untuk validasi indeks
                 let length = 0;
                 let temp = simList.head;
                 while (temp) {
                     length++;
                     temp = temp.next;
                 }

                 if (index >= length) {
                     alert(`List hanya memiliki ${length} node! Indeks maksimal yang valid adalah ${length-1}.`);
                     return;
                 }

                 // Validasi hapus node pertama
                 if (index === 0) {
                     // Hapus node pertama (head)
                     const oldHead = simList.head;
                     simList.head = simList.head.next;

                     // Jika list sekarang kosong, set tail ke null
                     if (simList.head === null) {
                         simList.tail = null;
                     }

                     renderList(listViz);
                     listIndexInput.value = '';
                     return;
                 }

                 // Persiapkan visualisasi langkah-langkah
                 listVisualizationSteps = [];

                 // Langkah 1: Temukan node sebelum yang akan dihapus
                 let current = simList.head;
                 let position = 0;

                 const step1 = {
                     content: buildStepListVisual(simList, {}),
                     description: "Langkah 1: Mulai dari head untuk mencari node sebelum target"
                 };
                 listVisualizationSteps.push(step1);

                 // Mencari node sebelum target (posisi index-1)
                 while (position < index - 1) {
                     position++;
                     current = current.next;
                 }

                 // Node yang akan dihapus
                 const nodeToDelete = current.next;

                 const step2 = {
                     content: buildStepListVisual(simList, {
                         highlight: current,
                         highlightNext: nodeToDelete,
                         pointerLabels: {
                             [current.data]: "current",
                             [nodeToDelete.data]: "nodeToDelete"
                         }
                     }),
                     description: `Langkah 2: Temukan node pada posisi ke-${position} (sebelum node yang akan dihapus)`
                 };
                 listVisualizationSteps.push(step2);

                 // Langkah 3: Simpan referensi ke node setelah yang akan dihapus
                 const nextNode = nodeToDelete.next;

                 const step3 = {
                     content: buildStepListVisual(simList, {
                         highlight: current,
                         highlightNext: nodeToDelete,
                         pointerLabels: {
                             [current.data]: "current",
                             [nodeToDelete.data]: "nodeToDelete",
                             [nextNode ? nextNode.data : "null"]: nextNode ? "nextNode" : ""
                         },
                         deleteHighlight: nodeToDelete
                     }),
                     description: "Langkah 3: Identifikasi node yang akan dihapus dan node setelahnya"
                 };
                 listVisualizationSteps.push(step3);

                 // Langkah 4: Ubah pointer current.next untuk melewati nodeToDelete
                 current.next = nextNode;

                 // Jika nodeToDelete adalah tail, update tail
                 if (nodeToDelete === simList.tail) {
                     simList.tail = current;
                 }

                 const step4 = {
                     content: buildStepListVisual(simList, {
                         highlight: current,
                         pointerLabels: {
                             [current.data]: "current"
                         }
                     }),
                     description: "Langkah 4: Atur current.next ke nextNode, melewati nodeToDelete. Node berhasil dihapus!"
                 };
                 listVisualizationSteps.push(step4);

                 // Reset indeks visualisasi dan tampilkan
                 currentListStepIndex = 0;
                 showListStep(currentListStepIndex);
                 listStepsContainer.style.display = 'block';

                 // Reset input field
                 listIndexInput.value = '';
             });

             // Fungsi helper untuk visualisasi langkah-langkah linked list
             function buildStepListVisual(list, options = {}) {
                 const container = document.createElement('div');
                 container.style.display = 'flex';
                 container.style.alignItems = 'center';
                 container.style.justifyContent = 'center';
                 container.style.width = '100%';
                 container.style.padding = '10px 0';
                 container.classList.add('list-step-container');

                 if (!list || !list.head) {
                     container.textContent = 'List Kosong';
                     return container;
                 }

                 let current = list.head;

                 // Tambahkan node baru (jika ada) tapi belum dimasukkan ke linked list
                 if (options.newNode && options.preInsert) {
                     // Simpan pointer posisi untuk penambahan node baru
                     let insertAfter = options.highlight;
                     let insertPosition = 0;
                     let insertIndex = -1;

                     // Hitung posisi current dalam list
                     let tempNode = list.head;
                     while (tempNode && tempNode !== insertAfter) {
                         insertPosition++;
                         tempNode = tempNode.next;
                     }
                     // Indeks target adalah posisi setelah current
                     insertIndex = insertPosition + 1;

                     // Buat container untuk node baru agar bisa diposisikan absolute
                     const newNodeContainer = document.createElement('div');
                     newNodeContainer.classList.add('new-node-container');

                     // Node visual
                     const nodeVisual = createListNodeVisual(options.newNode.data);
                     nodeVisual.classList.add('step-highlight');

                     // Tambahkan label "newNode" pada node baru
                     if (options.pointerLabels && options.pointerLabels[options.newNode.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[options.newNode.data];
                         nodeVisual.appendChild(label);
                     }

                     // Container positioning untuk node baru
                     const newNodePositioner = document.createElement('div');
                     newNodePositioner.style.position = 'absolute';
                     newNodePositioner.style.top = '-120px';
                     newNodePositioner.style.left = '50%';
                     newNodePositioner.style.transform = 'translateX(-50%)';
                     newNodePositioner.style.zIndex = '10';

                     newNodeContainer.appendChild(nodeVisual);
                     newNodePositioner.appendChild(newNodeContainer);

                     // Buat panah dari node baru ke target
                     if (options.highlight && options.highlight.next) {
                         // SVG untuk membuat panah yang lebih fleksibel
                         const svgNS = "http://www.w3.org/2000/svg";
                         const svg = document.createElementNS(svgNS, "svg");
                         svg.setAttribute("width", "150");
                         svg.setAttribute("height", "100");
                         svg.style.position = "absolute";
                         svg.style.top = "20px";
                         svg.style.left = "25px";
                         svg.style.zIndex = "5";
                         svg.style.pointerEvents = "none";

                         // Path untuk panah
                         const path = document.createElementNS(svgNS, "path");
                         path.setAttribute("d", "M 10,10 L 120,90");
                         path.setAttribute("stroke", "#ff5722");
                         path.setAttribute("stroke-width", "2");
                         path.setAttribute("fill", "none");
                         path.setAttribute("marker-end", "url(#arrowhead)");

                         // Arrow head marker
                         const defs = document.createElementNS(svgNS, "defs");
                         const marker = document.createElementNS(svgNS, "marker");
                         marker.setAttribute("id", "arrowhead");
                         marker.setAttribute("markerWidth", "10");
                         marker.setAttribute("markerHeight", "7");
                         marker.setAttribute("refX", "9");
                         marker.setAttribute("refY", "3.5");
                         marker.setAttribute("orient", "auto");

                         const polygon = document.createElementNS(svgNS, "polygon");
                         polygon.setAttribute("points", "0 0, 10 3.5, 0 7");
                         polygon.setAttribute("fill", "#ff5722");

                         marker.appendChild(polygon);
                         defs.appendChild(marker);
                         svg.appendChild(defs);
                         svg.appendChild(path);

                         newNodePositioner.appendChild(svg);

                         // Tambahkan teks keterangan target: "Target Indeks: X"
                         const targetText = document.createElement('div');
                         targetText.style.position = 'absolute';
                         targetText.style.bottom = '-30px';
                         targetText.style.left = '50%';
                         targetText.style.transform = 'translateX(-50%)';
                         targetText.style.fontSize = '13px';
                         targetText.style.fontWeight = 'bold';
                         targetText.style.color = '#e65100';
                         targetText.textContent = `Target: indeks ${insertIndex}`;

                         newNodeContainer.appendChild(targetText);
                     }

                     container.appendChild(newNodePositioner);
                 } else if (options.newNode) {
                     // Jika newNode ada tapi tidak preInsert, tampilkan jauh di kiri sebagai persiapan
                     const newNodeContainer = document.createElement('div');
                     newNodeContainer.classList.add('step-node');
                     newNodeContainer.style.marginRight = '30px';

                     const nodeVisual = createListNodeVisual(options.newNode.data);
                     nodeVisual.classList.add('step-highlight');

                     // Tambahkan label ke node ini
                     if (options.pointerLabels && options.pointerLabels[options.newNode.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[options.newNode.data];
                         nodeVisual.appendChild(label);
                     }

                     // Hilangkan pointer next untuk node persiapan
                     const nodePointer = nodeVisual.querySelector('.node-pointer');
                     if (nodePointer) nodePointer.style.display = 'none';

                     newNodeContainer.appendChild(nodeVisual);
                     container.appendChild(newNodeContainer);
                 }

                 // Tambahkan node-node yang sudah ada dalam linked list
                 const mainListContainer = document.createElement('div');
                 mainListContainer.style.display = 'flex';
                 mainListContainer.style.alignItems = 'center';
                 mainListContainer.style.position = 'relative';

                 // Hitung posisi target untuk tampilan
                 let targetPosition = -1;
                 if (options.highlight && options.preInsert) {
                     let tempNode = list.head;
                     let pos = 0;
                     while (tempNode && tempNode !== options.highlight) {
                         pos++;
                         tempNode = tempNode.next;
                     }
                     targetPosition = pos + 1; // Posisi target adalah setelah current
                 }

                 // Render list utama dengan indeks
                 let index = 0;
                 current = list.head;
                 while (current) {
                     const nodeContainer = document.createElement('div');
                     nodeContainer.classList.add('step-node');
                     nodeContainer.style.position = 'relative';

                     const nodeVisual = createListNodeVisual(current.data);

                     // Tambahkan indeks ke node
                     const indexLabel = document.createElement('div');
                     indexLabel.classList.add('node-index');
                     indexLabel.textContent = `[${index}]`;
                     nodeContainer.appendChild(indexLabel);

                     // Highlight node jika diperlukan
                     if (options.highlight === current) {
                         nodeVisual.classList.add('step-highlight');
                     }

                     // Highlight node berikutnya jika diperlukan
                     if (options.highlightNext === current) {
                         nodeVisual.classList.add('step-highlight');
                     }

                     // Highlight pointer jika diperlukan
                     if ((options.highlight === current && current.next === options.highlightNext) ||
                         (options.highlight === current && options.newNode && current.next === options.newNode)) {
                         const pointer = nodeVisual.querySelector('.node-pointer');
                         if (pointer) pointer.classList.add('highlight-pointer');
                     }

                     // Highlight node yang akan menjadi target penyisipan pada step 3
                     if (options.preInsert && options.highlight === current) {
                         // Tandai juga pada next node sebagai target setelah current
                         if (current.next) {
                             const nodeValue = nodeVisual.querySelector('.node-value');
                             if (nodeValue) nodeValue.classList.add('insert-target');

                             // Tambahkan indikator untuk tempat penyisipan
                             const insertHere = document.createElement('div');
                             insertHere.style.position = 'absolute';
                             insertHere.style.top = '-50px';
                             insertHere.style.right = '-20px';
                             insertHere.style.fontSize = '24px';
                             insertHere.style.color = '#ff5722';
                             insertHere.textContent = '↓';
                             insertHere.style.fontWeight = 'bold';
                             nodeContainer.appendChild(insertHere);

                             // Tambahkan keterangan target position di bawah node
                             const targetPosLabel = document.createElement('div');
                             targetPosLabel.classList.add('target-position');
                             targetPosLabel.textContent = `Target: indeks ${index+1}`;
                             nodeContainer.appendChild(targetPosLabel);
                         }
                     }

                     // Tambahkan label pointer jika ada
                     if (options.pointerLabels && options.pointerLabels[current.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[current.data];
                         nodeVisual.appendChild(label);
                     }

                     // Tandai node yang akan dihapus
                     if (options.deleteHighlight === current) {
                         nodeVisual.style.opacity = '0.5';
                         nodeVisual.style.border = '2px dashed #ff5722';

                         // Tambahkan efek "slash" di atas node yang akan dihapus
                         const slash = document.createElement('div');
                         slash.style.position = 'absolute';
                         slash.style.width = '80px';
                         slash.style.height = '2px';
                         slash.style.backgroundColor = '#ff5722';
                         slash.style.top = '50%';
                         slash.style.left = '50%';
                         slash.style.transform = 'translate(-50%, -50%) rotate(45deg)';
                         slash.style.zIndex = '2';
                         nodeVisual.appendChild(slash);

                         // Tambahkan keterangan indeks yang dihapus
                         const deleteLabel = document.createElement('div');
                         deleteLabel.classList.add('target-position');
                         deleteLabel.style.backgroundColor = '#ffebee';
                         deleteLabel.style.borderColor = '#f44336';
                         deleteLabel.style.color = '#d32f2f';
                         deleteLabel.textContent = `Hapus: indeks ${index}`;
                         nodeContainer.appendChild(deleteLabel);

                         // Jika node ini memiliki pointer, hilangkan pointer tersebut
                         const pointer = nodeVisual.querySelector('.node-pointer');
                         if (pointer) pointer.style.opacity = '0.3';
                     }

                     // Sembunyikan pointer untuk node terakhir
                     if (current.next === null) {
                         const pointer = nodeVisual.querySelector('.node-pointer');
                         if (pointer) pointer.style.display = 'none';
                     }

                     nodeContainer.appendChild(nodeVisual);
                     mainListContainer.appendChild(nodeContainer);

                     current = current.next;
                     index++;
                 }

                 container.appendChild(mainListContainer);
                 return container;
             }

             // Fungsi untuk menampilkan langkah visualisasi list
             function showListStep(index) {
                 if (!listVisualizationSteps || listVisualizationSteps.length === 0) return;

                 if (index < 0) index = 0;
                 if (index >= listVisualizationSteps.length) index = listVisualizationSteps.length - 1;

                 const step = listVisualizationSteps[index];

                 // Update judul visualisasi dengan langkah saat ini
                 const stepTitle = document.querySelector('#list-steps .step-title');
                 if (stepTitle) {
                     stepTitle.textContent = `Visualisasi Langkah-langkah (${index + 1}/${listVisualizationSteps.length})`;
                 }

                 // Update konten
                 listStepContent.innerHTML = '';
                 listStepContent.appendChild(step.content);

                 // Update deskripsi dengan format yang lebih baik
                 listStepDescription.innerHTML = '';
                 const descriptionText = document.createElement('strong');
                 descriptionText.textContent = step.description;
                 listStepDescription.appendChild(descriptionText);

                 // Update tombol
                 listPrevStepBtn.disabled = (index <= 0);
                 listNextStepBtn.disabled = (index >= listVisualizationSteps.length - 1);

                 // Buat tombol terlihat lebih mencolok jika tersedia langkah
                 listPrevStepBtn.textContent = index > 0 ? `« Langkah Sebelumnya (${index})` : "« Langkah Sebelumnya";
                 listNextStepBtn.textContent = index < listVisualizationSteps.length - 1 ? `Langkah Selanjutnya (${index + 2}) »` : "Langkah Selanjutnya »";

                 currentListStepIndex = index;
             }

             // Step visualization listeners
             if (listPrevStepBtn) listPrevStepBtn.addEventListener('click', () => {
                 showListStep(currentListStepIndex - 1);
             });

             if (listNextStepBtn) listNextStepBtn.addEventListener('click', () => {
                 showListStep(currentListStepIndex + 1);
             });

             if (listCloseStepsBtn) listCloseStepsBtn.addEventListener('click', () => {
                 listStepsContainer.style.display = 'none';
                 renderList(listViz); // Re-render list to show final state
             });


             // --- Doubly Linked List Step Visualization Functions ---
             function buildStepDoublyListVisual(list, options = {}) {
                 const container = document.createElement('div');
                 container.style.display = 'flex';
                 container.style.flexDirection = 'column';
                 container.style.alignItems = 'center';
                 container.style.justifyContent = 'center';
                 container.style.width = '100%';
                 container.style.padding = '10px 0';
                 container.classList.add('list-step-container'); // Reuse some styling

                 if (!list || !list.head) {
                     container.textContent = 'List Kosong';
                     return container;
                 }

                 // --- Handle newNode display (similar to SLL) ---
                 if (options.newNode && options.preInsert) {
                     let insertAfter = options.highlightPrev; // Node before insertion point
                     let insertPosition = 0;
                     let insertIndex = -1;

                     let tempNode = list.head;
                     while (tempNode && tempNode !== insertAfter) {
                         insertPosition++;
                         tempNode = tempNode.next;
                     }
                     insertIndex = insertPosition + 1; // Target index

                     const newNodeContainer = document.createElement('div');
                     newNodeContainer.classList.add('new-node-container');

                     // Create node visual with index
                     const nodeVisual = createDoublyListNodeVisual(options.newNode.data, null);

                     // Get the actual list-node element inside the container
                     const listNodeElement = nodeVisual.querySelector('.list-node');
                     listNodeElement.classList.add('step-highlight');

                     // Hide pointers initially for the floating new node
                     const pointers = listNodeElement.querySelectorAll('.node-pointer');
                     if (pointers[0]) pointers[0].style.display = 'none'; // Hide prev
                     if (pointers[1]) pointers[1].style.display = 'none'; // Hide next

                     if (options.pointerLabels && options.pointerLabels[options.newNode.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[options.newNode.data];
                         listNodeElement.appendChild(label);
                     }

                     const newNodePositioner = document.createElement('div');
                     newNodePositioner.style.position = 'absolute';
                     newNodePositioner.style.top = '-120px'; // Position above the list
                     newNodePositioner.style.left = '50%';
                     newNodePositioner.style.transform = 'translateX(-50%)';
                     newNodePositioner.style.zIndex = '10';

                     newNodeContainer.appendChild(nodeVisual);
                     newNodePositioner.appendChild(newNodeContainer);

                     // Add target index label below the floating node
                     const targetText = document.createElement('div');
                     targetText.classList.add('target-position');
                     targetText.style.bottom = '-30px'; // Adjust position
                     targetText.textContent = `Target: indeks ${insertIndex}`;
                     newNodeContainer.appendChild(targetText);

                     container.appendChild(newNodePositioner);

                 } else if (options.newNode) {
                     // newNode exists but not in preInsert phase (e.g., step 1)
                     const newNodeContainer = document.createElement('div');
                     newNodeContainer.classList.add('step-node');
                     newNodeContainer.style.marginRight = '30px';

                     // Create node visual with index
                     const nodeVisual = createDoublyListNodeVisual(options.newNode.data, null);

                     // Get the actual list-node element inside the container
                     const listNodeElement = nodeVisual.querySelector('.list-node');
                     listNodeElement.classList.add('step-highlight');

                     // Hide pointers for preparation node
                     const pointers = listNodeElement.querySelectorAll('.node-pointer');
                     if (pointers[0]) pointers[0].style.display = 'none';
                     if (pointers[1]) pointers[1].style.display = 'none';

                     if (options.pointerLabels && options.pointerLabels[options.newNode.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[options.newNode.data];
                         listNodeElement.appendChild(label);
                     }

                     newNodeContainer.appendChild(nodeVisual);
                     container.appendChild(newNodeContainer);
                 }

                 // --- Render the main list ---
                 const mainListContainer = document.createElement('div');
                 mainListContainer.style.display = 'flex';
                 mainListContainer.style.alignItems = 'center';
                 mainListContainer.style.position = 'relative'; // Needed for absolute positioned labels/indicators

                 let current = list.head;
                 let index = 0;
                 while (current) {
                     // Create node visual with index and head/tail labels
                     const isHead = (current === list.head);
                     const isTail = (current === list.tail);
                     const nodeVisual = createDoublyListNodeVisual(current.data, index, isHead, isTail);

                     // Get the actual list-node element inside the container
                     const listNodeElement = nodeVisual.querySelector('.list-node');

                     // Get the pointers
                     const pointers = listNodeElement.querySelectorAll('.node-pointer'); // [prev, next]

                     // --- Highlighting Nodes ---
                     if (options.highlight === current || options.highlightPrev === current || options.highlightNext === current) {
                         listNodeElement.classList.add('step-highlight');
                     }
                     if (options.deleteHighlight === current) {
                         listNodeElement.style.opacity = '0.5';
                         listNodeElement.style.border = '2px dashed #ff5722';
                         // Add slash effect
                         const slash = document.createElement('div');
                         slash.style.position = 'absolute';
                         slash.style.width = '80px'; // Adjust size as needed
                         slash.style.height = '2px';
                         slash.style.backgroundColor = '#ff5722';
                         slash.style.top = '50%';
                         slash.style.left = '50%';
                         slash.style.transform = 'translate(-50%, -50%) rotate(45deg)';
                         slash.style.zIndex = '2';
                         listNodeElement.appendChild(slash);
                         // Add delete index label
                         const deleteLabel = document.createElement('div');
                         deleteLabel.classList.add('target-position');
                         deleteLabel.style.backgroundColor = '#ffebee';
                         deleteLabel.style.borderColor = '#f44336';
                         deleteLabel.style.color = '#d32f2f';
                         deleteLabel.textContent = `Hapus: indeks ${index}`;
                         nodeVisual.appendChild(deleteLabel);
                     }

                     // --- Highlighting Pointers ---
                     // Highlight prev pointer of current node
                     if (options.highlightPointerPrev === current && pointers[0]) {
                         pointers[0].classList.add('highlight-pointer');
                     }
                     // Highlight next pointer of current node
                     if (options.highlightPointerNext === current && pointers[1]) {
                         pointers[1].classList.add('highlight-pointer');
                     }

                     // --- Pointer Labels ---
                     if (options.pointerLabels && options.pointerLabels[current.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[current.data];
                         // Adjust label position slightly for DLL to avoid overlap
                         label.style.top = '-30px';
                         listNodeElement.appendChild(label);
                     }

                     // --- Insertion Target Indicator ---
                     if (options.preInsert && options.highlightPrev === current) {
                         // Add indicator between highlightPrev and highlightNext
                         const insertHere = document.createElement('div');
                         insertHere.style.position = 'absolute';
                         insertHere.style.top = '-50px'; // Position above the gap
                         insertHere.style.right = '-20px'; // Position between nodes
                         insertHere.style.fontSize = '24px';
                         insertHere.style.color = '#ff5722';
                         insertHere.textContent = '↓';
                         insertHere.style.fontWeight = 'bold';
                         nodeVisual.appendChild(insertHere);

                         // Add target index label below the gap
                         const targetPosLabel = document.createElement('div');
                         targetPosLabel.classList.add('target-position');
                         targetPosLabel.textContent = `Target: indeks ${index + 1}`;
                         nodeVisual.appendChild(targetPosLabel);
                     }

                     // Hide pointers at list ends
                     if (current.prev === null && pointers[0]) pointers[0].style.display = 'none';
                     if (current.next === null && pointers[1]) pointers[1].style.display = 'none';

                     // Dim pointers for deleted node
                     if (options.deleteHighlight === current) {
                         if (pointers[0]) pointers[0].style.opacity = '0.3';
                         if (pointers[1]) pointers[1].style.opacity = '0.3';
                     }

                     mainListContainer.appendChild(nodeVisual);

                     current = current.next;
                     index++;
                 }

                 container.appendChild(mainListContainer);

                 return container;
             }

             function showDoublyStep(index) {
                 if (!doublyVisualizationSteps || doublyVisualizationSteps.length === 0) return;

                 if (index < 0) index = 0;
                 if (index >= doublyVisualizationSteps.length) index = doublyVisualizationSteps.length - 1;

                 const step = doublyVisualizationSteps[index];

                 // Update title
                 if (doublyStepTitle) {
                     doublyStepTitle.textContent = `Visualisasi Langkah-langkah (${index + 1}/${doublyVisualizationSteps.length})`;
                 }

                 // Update content
                 doublyStepContent.innerHTML = '';
                 doublyStepContent.appendChild(step.content); // Assumes step.content is a DOM element

                 // Update description
                 doublyStepDescription.innerHTML = '';
                 const descriptionText = document.createElement('strong');
                 descriptionText.textContent = step.description;
                 doublyStepDescription.appendChild(descriptionText);

                 // Update buttons
                 doublyPrevStepBtn.disabled = (index <= 0);
                 doublyNextStepBtn.disabled = (index >= doublyVisualizationSteps.length - 1);
                 doublyPrevStepBtn.textContent = index > 0 ? `« Langkah Sebelumnya (${index})` : "« Langkah Sebelumnya";
                 doublyNextStepBtn.textContent = index < doublyVisualizationSteps.length - 1 ? `Langkah Selanjutnya (${index + 2}) »` : "Langkah Selanjutnya »";

                 currentDoublyStepIndex = index;
             }


             // Doubly Linked List Listeners
             function initDoublyList() { simDoublyList = { head: null, tail: null }; renderDoublyList(doublyListViz); }
             if (doublyListAddHeadBtn) doublyListAddHeadBtn.addEventListener('click', () => {
                 const value = doublyListValueInput.value.trim(); if (value === '') return;
                 const newNode = new DoublyListNode(value);
                 if (!simDoublyList || !simDoublyList.head) simDoublyList = { head: newNode, tail: newNode };
                 else { newNode.next = simDoublyList.head; simDoublyList.head.prev = newNode; simDoublyList.head = newNode; }
                 renderDoublyList(doublyListViz); doublyListValueInput.value = '';
             });
             if (doublyListAddTailBtn) doublyListAddTailBtn.addEventListener('click', () => {
                 const value = doublyListValueInput.value.trim(); if (value === '') return;
                 const newNode = new DoublyListNode(value);
                 if (!simDoublyList || !simDoublyList.tail) simDoublyList = { head: newNode, tail: newNode };
                 else { newNode.prev = simDoublyList.tail; simDoublyList.tail.next = newNode; simDoublyList.tail = newNode; }
                 renderDoublyList(doublyListViz); doublyListValueInput.value = '';
             });
             if (doublyListRemoveHeadBtn) doublyListRemoveHeadBtn.addEventListener('click', () => {
                 if (!simDoublyList || !simDoublyList.head) return;
                 simDoublyList.head = simDoublyList.head.next;
                 if (simDoublyList.head === null) simDoublyList.tail = null;
                 else simDoublyList.head.prev = null;
                 renderDoublyList(doublyListViz);
             });
             if (doublyListRemoveTailBtn) doublyListRemoveTailBtn.addEventListener('click', () => {
                  if (!simDoublyList || !simDoublyList.head) return;
                  if (simDoublyList.head === simDoublyList.tail) simDoublyList = { head: null, tail: null };
                  else { simDoublyList.tail = simDoublyList.tail.prev; simDoublyList.tail.next = null; }
                  renderDoublyList(doublyListViz);
             });
             if (doublyListResetBtn) doublyListResetBtn.addEventListener('click', initDoublyList);
              // Add Middle/Remove Middle Listeners (Hooked up to step visualization)
             if (doublyListAddMiddleBtn) doublyListAddMiddleBtn.addEventListener('click', () => {
                 const value = doublyListValueInput.value.trim();
                 const indexStr = doublyListIndexInput.value.trim();

                 if (value === '') { alert("Masukkan nilai untuk ditambahkan!"); return; }
                 if (indexStr === '') { alert("Masukkan indeks posisi untuk menambahkan node!"); return; }

                 const index = parseInt(indexStr);
                 if (isNaN(index) || index < 0) { alert("Indeks tidak valid!"); return; }

                 // --- Edge Case: Add to Empty List ---
                 if (!simDoublyList || !simDoublyList.head) {
                     if (index === 0) {
                         // Same as Add Head
                         const newNode = new DoublyListNode(value);
                         simDoublyList = { head: newNode, tail: newNode };
                         renderDoublyList(doublyListViz);
                         doublyListValueInput.value = ''; doublyListIndexInput.value = '';
                         return;
                     } else {
                         alert("List kosong! Indeks harus 0."); return;
                     }
                 }

                 // --- Edge Case: Add at Head (Index 0) ---
                 if (index === 0) {
                     // Same as Add Head logic
                     const newNode = new DoublyListNode(value);
                     newNode.next = simDoublyList.head;
                     simDoublyList.head.prev = newNode;
                     simDoublyList.head = newNode;
                     renderDoublyList(doublyListViz);
                     doublyListValueInput.value = ''; doublyListIndexInput.value = '';
                     return;
                 }

                 // --- Find Insertion Point & Validate Index ---
                 let prevNode = simDoublyList.head;
                 let count = 0;
                 while (prevNode !== null && count < index - 1) {
                     prevNode = prevNode.next;
                     count++;
                 }

                 // Check if index is valid (must be <= length)
                 // If prevNode is null or prevNode.next is null and index > count+1, index is out of bounds
                 if (!prevNode || (index > count + 1)) {
                      // Calculate actual length to give a better error message
                      let length = 0;
                      let temp = simDoublyList.head;
                      while(temp) { length++; temp = temp.next; }
                      alert(`Indeks ${index} tidak valid. Indeks harus antara 0 dan ${length}.`);
                      return;
                 }

                 // --- Prepare Visualization Steps ---
                 const newNode = new DoublyListNode(value);
                 const nextNode = prevNode.next; // Node currently at the target index
                 doublyVisualizationSteps = [];

                 // Step 1: Prepare newNode
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, { newNode: newNode, pointerLabels: { [newNode.data]: "newNode" } }),
                     description: `Langkah 1: Siapkan node baru ('${value}')`
                 });

                 // Step 2: Find node before target index (prevNode)
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, {
                         newNode: newNode,
                         highlightPrev: prevNode,
                         pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode" }
                     }),
                     description: `Langkah 2: Temukan node sebelum target (indeks ${index - 1}) -> '${prevNode.data}'`
                 });

                 // Step 3: Set newNode.next = prevNode.next (nextNode)
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, {
                         newNode: newNode,
                         highlightPrev: prevNode,
                         highlightNext: nextNode, // Highlight the node that newNode.next will point to
                         preInsert: true, // Show newNode floating above
                         pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode", ...(nextNode && { [nextNode.data]: "nextNode" }) }
                     }),
                     description: `Langkah 3: Atur newNode.next menunjuk ke node setelah prevNode (${nextNode ? "'" + nextNode.data + "'" : 'null'})`
                 });

                 // Step 4: Set newNode.prev = prevNode
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, {
                         newNode: newNode,
                         highlightPrev: prevNode,
                         highlightNext: nextNode,
                         preInsert: true,
                         highlightPointerPrev: newNode, // Highlight the conceptual prev pointer of newNode
                         pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode", ...(nextNode && { [nextNode.data]: "nextNode" }) }
                     }),
                     description: `Langkah 4: Atur newNode.prev menunjuk ke prevNode ('${prevNode.data}')`
                 });

                 // Step 5: Set nextNode.prev = newNode (if nextNode exists)
                 if (nextNode) {
                     doublyVisualizationSteps.push({
                         content: buildStepDoublyListVisual(simDoublyList, {
                             newNode: newNode,
                             highlightPrev: prevNode,
                             highlightNext: nextNode,
                             preInsert: true,
                             highlightPointerPrev: nextNode, // Highlight the prev pointer of nextNode
                             pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode", [nextNode.data]: "nextNode" }
                         }),
                         description: `Langkah 5: Atur nextNode.prev ('${nextNode.data}'.prev) menunjuk ke newNode`
                     });
                 } else {
                      doublyVisualizationSteps.push({
                         content: buildStepDoublyListVisual(simDoublyList, {
                             newNode: newNode,
                             highlightPrev: prevNode,
                             highlightNext: nextNode, // null
                             preInsert: true,
                             pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode" }
                         }),
                         description: `Langkah 5: Tidak ada nextNode, tidak perlu set nextNode.prev (menambahkan di akhir)`
                     });
                 }


                 // Step 6: Set prevNode.next = newNode
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, {
                         newNode: newNode,
                         highlightPrev: prevNode,
                         highlightNext: nextNode,
                         preInsert: true,
                         highlightPointerNext: prevNode, // Highlight the next pointer of prevNode
                         pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode", ...(nextNode && { [nextNode.data]: "nextNode" }) }
                     }),
                     description: `Langkah 6: Atur prevNode.next ('${prevNode.data}'.next) menunjuk ke newNode`
                 });

                 // --- Perform Actual Insertion ---
                 newNode.next = nextNode;
                 newNode.prev = prevNode;
                 prevNode.next = newNode;
                 if (nextNode) {
                     nextNode.prev = newNode;
                 } else {
                     // If nextNode was null, newNode is the new tail
                     simDoublyList.tail = newNode;
                 }

                 // Step 7: Final state
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, { // Show the list *after* insertion
                         highlight: newNode, // Highlight the newly inserted node
                         pointerLabels: { [newNode.data]: "newNode" }
                     }),
                     description: `Langkah 7: Node '${value}' berhasil disisipkan pada indeks ${index}!`
                 });


                 // --- Show Visualization ---
                 currentDoublyStepIndex = 0;
                 showDoublyStep(currentDoublyStepIndex);
                 doublyStepsContainer.style.display = 'block';

                 // --- Clear Inputs ---
                 doublyListValueInput.value = '';
                 doublyListIndexInput.value = '';
             });
             if (doublyListRemoveMiddleBtn) doublyListRemoveMiddleBtn.addEventListener('click', () => {
                 const indexStr = doublyListIndexInput.value.trim();
                 if (indexStr === '') { alert("Masukkan indeks node yang akan dihapus!"); return; }

                 const index = parseInt(indexStr);
                 if (isNaN(index) || index < 0) { alert("Indeks tidak valid!"); return; }

                 // --- Validate List Not Empty ---
                 if (!simDoublyList || !simDoublyList.head) {
                     alert("List kosong! Tidak ada yang bisa dihapus."); return;
                 }

                 // --- Handle Remove Head (Index 0) ---
                 if (index === 0) {
                     // Same as Remove Head logic
                     simDoublyList.head = simDoublyList.head.next;
                     if (simDoublyList.head === null) simDoublyList.tail = null; // List became empty
                     else simDoublyList.head.prev = null;
                     renderDoublyList(doublyListViz);
                     doublyListIndexInput.value = '';
                     return;
                 }

                 // --- Find Node to Delete and Validate Index ---
                 let prevNode = simDoublyList.head;
                 let count = 0;
                 while (prevNode !== null && count < index - 1) {
                     prevNode = prevNode.next;
                     count++;
                 }

                 // Check if prevNode exists and if the node *at* the index exists
                 if (!prevNode || !prevNode.next) {
                     // Calculate actual length for error message
                     let length = 0;
                     let temp = simDoublyList.head;
                     while(temp) { length++; temp = temp.next; }
                     alert(`Indeks ${index} tidak valid. Indeks harus antara 0 dan ${length - 1}.`);
                     return;
                 }

                 // --- Prepare Visualization Steps ---
                 const nodeToDelete = prevNode.next;
                 const nextNode = nodeToDelete.next; // Node after the one to delete
                 doublyVisualizationSteps = [];

                 // Step 1: Find node before target (prevNode)
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, {
                         highlightPrev: prevNode,
                         pointerLabels: { [prevNode.data]: "prevNode" }
                     }),
                     description: `Langkah 1: Temukan node sebelum target (indeks ${index - 1}) -> '${prevNode.data}'`
                 });

                 // Step 2: Identify node to delete and the node after it
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, {
                         highlightPrev: prevNode,
                         highlight: nodeToDelete, // Highlight the node to be deleted
                         highlightNext: nextNode,
                         deleteHighlight: nodeToDelete, // Visually mark for deletion
                         pointerLabels: {
                             [prevNode.data]: "prevNode",
                             [nodeToDelete.data]: "nodeToDelete",
                             ...(nextNode && { [nextNode.data]: "nextNode" })
                         }
                     }),
                     description: `Langkah 2: Identifikasi node yang akan dihapus ('${nodeToDelete.data}') dan node setelahnya (${nextNode ? "'" + nextNode.data + "'" : 'null'})`
                 });

                 // Step 3: Set prevNode.next = nodeToDelete.next (nextNode)
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, {
                         highlightPrev: prevNode,
                         highlightNext: nextNode, // Show the target of the new pointer
                         deleteHighlight: nodeToDelete,
                         highlightPointerNext: prevNode, // Highlight the pointer being changed
                         pointerLabels: {
                             [prevNode.data]: "prevNode",
                             [nodeToDelete.data]: "nodeToDelete",
                             ...(nextNode && { [nextNode.data]: "nextNode" })
                         }
                     }),
                     description: `Langkah 3: Atur prevNode.next menunjuk ke nextNode (${nextNode ? "'" + nextNode.data + "'" : 'null'}), melewati nodeToDelete`
                 });

                 // Step 4: Set nextNode.prev = prevNode (if nextNode exists)
                 if (nextNode) {
                     doublyVisualizationSteps.push({
                         content: buildStepDoublyListVisual(simDoublyList, {
                             highlightPrev: prevNode,
                             highlightNext: nextNode,
                             deleteHighlight: nodeToDelete,
                             highlightPointerPrev: nextNode, // Highlight the pointer being changed
                             pointerLabels: {
                                 [prevNode.data]: "prevNode",
                                 [nodeToDelete.data]: "nodeToDelete",
                                 [nextNode.data]: "nextNode"
                             }
                         }),
                         description: `Langkah 4: Atur nextNode.prev ('${nextNode.data}'.prev) menunjuk ke prevNode ('${prevNode.data}')`
                     });
                 } else {
                      doublyVisualizationSteps.push({
                         content: buildStepDoublyListVisual(simDoublyList, {
                             highlightPrev: prevNode,
                             highlightNext: null, // No next node
                             deleteHighlight: nodeToDelete,
                             pointerLabels: {
                                 [prevNode.data]: "prevNode",
                                 [nodeToDelete.data]: "nodeToDelete"
                             }
                         }),
                         description: `Langkah 4: Tidak ada nextNode, tidak perlu set nextNode.prev (menghapus node terakhir)`
                     });
                 }

                 // --- Perform Actual Deletion ---
                 prevNode.next = nextNode;
                 if (nextNode) {
                     nextNode.prev = prevNode;
                 } else {
                     // If nodeToDelete was the tail, update tail
                     simDoublyList.tail = prevNode;
                 }

                 // Step 5: Final state
                 doublyVisualizationSteps.push({
                     content: buildStepDoublyListVisual(simDoublyList, { // Show the list *after* deletion
                         highlightPrev: prevNode, // Highlight surrounding nodes
                         highlightNext: nextNode
                     }),
                     description: `Langkah 5: Node pada indeks ${index} ('${nodeToDelete.data}') berhasil dihapus!`
                 });

                 // --- Show Visualization ---
                 currentDoublyStepIndex = 0;
                 showDoublyStep(currentDoublyStepIndex);
                 doublyStepsContainer.style.display = 'block';

                 // --- Clear Input ---
                 doublyListIndexInput.value = '';
             });

             // Doubly List Step visualization listeners
             if (doublyPrevStepBtn) doublyPrevStepBtn.addEventListener('click', () => {
                 showDoublyStep(currentDoublyStepIndex - 1);
             });
             if (doublyNextStepBtn) doublyNextStepBtn.addEventListener('click', () => {
                 showDoublyStep(currentDoublyStepIndex + 1);
             });
             if (doublyCloseStepsBtn) doublyCloseStepsBtn.addEventListener('click', () => {
                 doublyStepsContainer.style.display = 'none';
                 renderDoublyList(doublyListViz); // Re-render list to show final state
             });


             // --- Circular Linked List Step Visualization Functions ---
             function buildStepCircularListVisual(list, options = {}) {
                 const container = document.createElement('div');
                 container.style.display = 'flex';
                 container.style.alignItems = 'center';
                 container.style.justifyContent = 'center';
                 container.style.width = '100%';
                 container.style.padding = '10px 0';
                 container.classList.add('list-step-container'); // Reuse styling

                 if (!list || !list.head) {
                     container.textContent = 'List Kosong';
                     return container;
                 }

                 // --- Handle newNode display (similar to SLL/DLL) ---
                 if (options.newNode && options.preInsert) {
                     let insertAfter = options.highlightPrev; // Node before insertion point
                     let insertPosition = 0;
                     let insertIndex = -1;

                     let tempNode = list.head;
                     let i = 0;
                     do {
                         if (tempNode === insertAfter) {
                             insertPosition = i;
                             break;
                         }
                         tempNode = tempNode.next;
                         i++;
                     } while (tempNode !== list.head && i < 100); // Safety break
                     insertIndex = insertPosition + 1;

                     const newNodeContainer = document.createElement('div');
                     newNodeContainer.classList.add('new-node-container');

                     const nodeVisual = createCircularListNodeVisual(options.newNode.data); // Use CLL visual creator
                     nodeVisual.classList.add('step-highlight');
                     // Hide pointer initially
                     const pointer = nodeVisual.querySelector('.node-pointer');
                     if (pointer) pointer.style.display = 'none';


                     if (options.pointerLabels && options.pointerLabels[options.newNode.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[options.newNode.data];
                         nodeVisual.appendChild(label);
                     }

                     const newNodePositioner = document.createElement('div');
                     newNodePositioner.style.position = 'absolute';
                     newNodePositioner.style.top = '-120px';
                     newNodePositioner.style.left = '50%';
                     newNodePositioner.style.transform = 'translateX(-50%)';
                     newNodePositioner.style.zIndex = '10';

                     newNodeContainer.appendChild(nodeVisual);
                     newNodePositioner.appendChild(newNodeContainer);

                     const targetText = document.createElement('div');
                     targetText.classList.add('target-position');
                     targetText.style.bottom = '-30px';
                     targetText.textContent = `Target: indeks ${insertIndex}`;
                     newNodeContainer.appendChild(targetText);

                     container.appendChild(newNodePositioner);

                 } else if (options.newNode) {
                     const newNodeContainer = document.createElement('div');
                     newNodeContainer.classList.add('step-node');
                     newNodeContainer.style.marginRight = '30px';

                     const nodeVisual = createCircularListNodeVisual(options.newNode.data);
                     nodeVisual.classList.add('step-highlight');
                     const pointer = nodeVisual.querySelector('.node-pointer');
                     if (pointer) pointer.style.display = 'none';

                     if (options.pointerLabels && options.pointerLabels[options.newNode.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[options.newNode.data];
                         nodeVisual.appendChild(label);
                     }
                     newNodeContainer.appendChild(nodeVisual);
                     container.appendChild(newNodeContainer);
                 }

                 // --- Render the main list ---
                 const mainListContainer = document.createElement('div');
                 mainListContainer.style.display = 'flex';
                 mainListContainer.style.alignItems = 'center';
                 mainListContainer.style.position = 'relative';

                 let current = list.head;
                 let index = 0;
                 const maxDisplayNodes = 10; // Limit display for large lists
                 let displayedNodes = 0;

                 do {
                     if (displayedNodes >= maxDisplayNodes) {
                         const ellipsis = document.createElement('div');
                         ellipsis.textContent = '...';
                         ellipsis.style.margin = '0 10px';
                         mainListContainer.appendChild(ellipsis);
                         break;
                     }

                     const nodeContainer = document.createElement('div');
                     nodeContainer.classList.add('step-node');
                     nodeContainer.style.position = 'relative';

                     const nodeVisual = createCircularListNodeVisual(current.data, index);
                     const pointer = nodeVisual.querySelector('.node-pointer');

                     // Adjust pointer position to be centered between nodes
                     if (pointer) {
                         pointer.style.position = 'absolute';
                         pointer.style.right = '-30px'; // Position pointer to extend from the right edge
                         pointer.style.top = '50%'; // Center vertically
                         pointer.style.transform = 'translateY(-50%)';
                     }

                     // Add index label
                     const indexLabel = document.createElement('div');
                     indexLabel.classList.add('node-index');
                     indexLabel.textContent = `[${index}]`;
                     nodeContainer.appendChild(indexLabel);

                     // --- Highlighting Nodes ---
                     if (options.highlight === current || options.highlightPrev === current || options.highlightNext === current) {
                         nodeVisual.classList.add('step-highlight');
                     }
                     if (options.deleteHighlight === current) {
                         nodeVisual.style.opacity = '0.5';
                         nodeVisual.style.border = '2px dashed #ff5722';
                         // Add slash effect
                         const slash = document.createElement('div');
                         slash.style.position = 'absolute'; slash.style.width = '80px'; slash.style.height = '2px';
                         slash.style.backgroundColor = '#ff5722'; slash.style.top = '50%'; slash.style.left = '50%';
                         slash.style.transform = 'translate(-50%, -50%) rotate(45deg)'; slash.style.zIndex = '2';
                         nodeVisual.appendChild(slash);
                         // Add delete index label
                         const deleteLabel = document.createElement('div');
                         deleteLabel.classList.add('target-position');
                         deleteLabel.style.backgroundColor = '#ffebee'; deleteLabel.style.borderColor = '#f44336'; deleteLabel.style.color = '#d32f2f';
                         deleteLabel.textContent = `Hapus: indeks ${index}`;
                         nodeContainer.appendChild(deleteLabel);
                         if (pointer) pointer.style.opacity = '0.3'; // Dim pointer too
                     }

                     // --- Highlighting Pointers ---
                     if (options.highlightPointerNext === current && pointer) {
                         pointer.classList.add('highlight-pointer');
                     }
                     // Highlight the tail's pointer if it's being changed to point to a new head
                     if (options.highlightTailPointer && current === list.tail && pointer) {
                          pointer.classList.add('highlight-pointer');
                     }


                     // --- Pointer Labels ---
                     if (options.pointerLabels && options.pointerLabels[current.data]) {
                         const label = document.createElement('div');
                         label.classList.add('pointer-label');
                         label.textContent = options.pointerLabels[current.data];
                         nodeVisual.appendChild(label);
                     }

                      // --- Insertion Target Indicator ---
                     if (options.preInsert && options.highlightPrev === current) {
                         const insertHere = document.createElement('div');
                         insertHere.style.position = 'absolute'; insertHere.style.top = '-50px'; insertHere.style.right = '-20px';
                         insertHere.style.fontSize = '24px'; insertHere.style.color = '#ff5722'; insertHere.textContent = '↓'; insertHere.style.fontWeight = 'bold';
                         nodeContainer.appendChild(insertHere);

                         const targetPosLabel = document.createElement('div');
                         targetPosLabel.classList.add('target-position');
                         targetPosLabel.textContent = `Target: indeks ${index + 1}`;
                         nodeContainer.appendChild(targetPosLabel);
                     }

                     // Hide pointer for the *last displayed node* if it points back to head (unless highlighted)
                     // We'll show this with the curved arrow instead
                     if (current === list.tail && displayedNodes < maxDisplayNodes && pointer && !options.highlightTailPointer) {
                         pointer.style.display = 'none';
                     }


                     nodeContainer.appendChild(nodeVisual);
                     mainListContainer.appendChild(nodeContainer);

                     current = current.next;
                     index++;
                     displayedNodes++;
                 } while (current !== list.head && displayedNodes < maxDisplayNodes + 1); // Loop until back to head or max display

                 // Removed the return arrow as requested


                 container.appendChild(mainListContainer);
                 return container;
             }

             function showCircularStep(index) {
                 if (!circularVisualizationSteps || circularVisualizationSteps.length === 0) return;

                 if (index < 0) index = 0;
                 if (index >= circularVisualizationSteps.length) index = circularVisualizationSteps.length - 1;

                 const step = circularVisualizationSteps[index];
                 const stepTitle = document.querySelector('#circular-list-steps .step-title'); // Target correct title

                 // Update title
                 if (stepTitle) {
                     stepTitle.textContent = `Visualisasi Langkah-langkah (${index + 1}/${circularVisualizationSteps.length})`;
                 }

                 // Update content
                 circularStepContent.innerHTML = ''; // Target correct content area
                 circularStepContent.appendChild(step.content);

                 // Update description
                 circularStepDescription.innerHTML = ''; // Target correct description area
                 const descriptionText = document.createElement('strong');
                 descriptionText.textContent = step.description;
                 circularStepDescription.appendChild(descriptionText);

                 // Update buttons
                 circularPrevStepBtn.disabled = (index <= 0); // Target correct buttons
                 circularNextStepBtn.disabled = (index >= circularVisualizationSteps.length - 1);
                 circularPrevStepBtn.textContent = index > 0 ? `« Langkah Sebelumnya (${index})` : "« Langkah Sebelumnya";
                 circularNextStepBtn.textContent = index < circularVisualizationSteps.length - 1 ? `Langkah Selanjutnya (${index + 2}) »` : "Langkah Selanjutnya »";

                 currentCircularStepIndex = index;
             }


             // Circular Linked List Listeners
             function initCircularList() { simCircularList = { head: null, tail: null }; renderCircularList(circularListViz); }
             if (circularListAddHeadBtn) circularListAddHeadBtn.addEventListener('click', () => {
                 const value = circularListValueInput.value.trim(); if (value === '') return;
                 const newNode = new CircularListNode(value);
                 if (!simCircularList || !simCircularList.head) { newNode.next = newNode; simCircularList = { head: newNode, tail: newNode }; }
                 else { newNode.next = simCircularList.head; simCircularList.tail.next = newNode; simCircularList.head = newNode; }
                 renderCircularList(circularListViz); circularListValueInput.value = '';
             });
             if (circularListAddTailBtn) circularListAddTailBtn.addEventListener('click', () => {
                 const value = circularListValueInput.value.trim(); if (value === '') return;
                 const newNode = new CircularListNode(value);
                 if (!simCircularList || !simCircularList.head) { newNode.next = newNode; simCircularList = { head: newNode, tail: newNode }; }
                 else { newNode.next = simCircularList.head; simCircularList.tail.next = newNode; simCircularList.tail = newNode; }
                 renderCircularList(circularListViz); circularListValueInput.value = '';
             });
             if (circularListRemoveHeadBtn) circularListRemoveHeadBtn.addEventListener('click', () => {
                 if (!simCircularList || !simCircularList.head) return;
                 if (simCircularList.head === simCircularList.tail) simCircularList = { head: null, tail: null };
                 else { simCircularList.tail.next = simCircularList.head.next; simCircularList.head = simCircularList.head.next; }
                 renderCircularList(circularListViz);
             });
             if (circularListRemoveTailBtn) circularListRemoveTailBtn.addEventListener('click', () => {
                 if (!simCircularList || !simCircularList.head) return;
                 if (simCircularList.head === simCircularList.tail) simCircularList = { head: null, tail: null };
                 else {
                     let current = simCircularList.head;
                     while (current.next !== simCircularList.tail) current = current.next;
                     current.next = simCircularList.head; simCircularList.tail = current;
                 }
                 renderCircularList(circularListViz);
             });
             if (circularListResetBtn) circularListResetBtn.addEventListener('click', initCircularList);
             // Add Middle/Remove Middle Listeners (Hooked up to step visualization)
             if (circularListAddMiddleBtn) circularListAddMiddleBtn.addEventListener('click', () => {
                 const value = circularListValueInput.value.trim();
                 const indexStr = circularListIndexInput.value.trim();

                 if (value === '') { alert("Masukkan nilai untuk ditambahkan!"); return; }
                 if (indexStr === '') { alert("Masukkan indeks posisi untuk menambahkan node!"); return; }

                 const index = parseInt(indexStr);
                 if (isNaN(index) || index < 0) { alert("Indeks tidak valid!"); return; }

                 // --- Edge Case: Add to Empty List ---
                 if (!simCircularList || !simCircularList.head) {
                     if (index === 0) {
                         // Same as Add Head
                         const newNode = new CircularListNode(value);
                         newNode.next = newNode; // Point to itself
                         simCircularList = { head: newNode, tail: newNode };
                         renderCircularList(circularListViz);
                         circularListValueInput.value = ''; circularListIndexInput.value = '';
                         return;
                     } else {
                         alert("List kosong! Indeks harus 0."); return;
                     }
                 }

                 // --- Edge Case: Add at Head (Index 0) ---
                 if (index === 0) {
                     // Same as Add Head logic
                     const newNode = new CircularListNode(value);
                     newNode.next = simCircularList.head;
                     simCircularList.tail.next = newNode; // Tail points to new head
                     simCircularList.head = newNode;     // Update head
                     renderCircularList(circularListViz);
                     circularListValueInput.value = ''; circularListIndexInput.value = '';
                     return;
                 }

                 // --- Find Insertion Point & Validate Index ---
                 let prevNode = simCircularList.head;
                 let count = 0;
                 // Traverse up to index - 1, but don't go past the tail initially
                 while (prevNode !== simCircularList.tail && count < index - 1) {
                     prevNode = prevNode.next;
                     count++;
                 }

                 // Check if index is valid.
                 // We need to allow insertion *after* the tail (which means index == length)
                 // Calculate length carefully for circular list
                 let length = 0;
                 let temp = simCircularList.head;
                 do {
                     length++;
                     temp = temp.next;
                 } while (temp !== simCircularList.head);

                 if (index > length) { // Cannot insert beyond the position after the tail
                     alert(`Indeks ${index} tidak valid. Indeks harus antara 0 dan ${length}.`);
                     return;
                 }
                 // If index === length, prevNode should be the tail
                 if (index === length && prevNode !== simCircularList.tail) {
                     // This case should ideally not happen if the loop above is correct, but as a safeguard:
                     prevNode = simCircularList.tail;
                     count = length -1; // Adjust count accordingly
                 } else if (index <= count) {
                     // This means the loop stopped early, index is invalid (e.g., negative or too small handled earlier)
                     // Or trying to insert before head using middle function
                      alert(`Indeks ${index} tidak valid.`);
                      return;
                 }


                 // --- Prepare Visualization Steps ---
                 const newNode = new CircularListNode(value);
                 const nextNode = prevNode.next; // Node currently at the target index (could be head)
                 circularVisualizationSteps = [];

                 // Step 1: Prepare newNode
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, { newNode: newNode, pointerLabels: { [newNode.data]: "newNode" } }),
                     description: `Langkah 1: Siapkan node baru ('${value}')`
                 });

                 // Step 2: Find node before target index (prevNode)
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, {
                         newNode: newNode,
                         highlightPrev: prevNode,
                         pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode" }
                     }),
                     description: `Langkah 2: Temukan node sebelum target (indeks ${index - 1}) -> '${prevNode.data}'`
                 });

                 // Step 3: Set newNode.next = prevNode.next (nextNode)
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, {
                         newNode: newNode,
                         highlightPrev: prevNode,
                         highlightNext: nextNode, // Highlight the node that newNode.next will point to
                         preInsert: true, // Show newNode floating above
                         pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode", [nextNode.data]: "nextNode" }
                     }),
                     description: `Langkah 3: Atur newNode.next menunjuk ke node setelah prevNode ('${nextNode.data}')`
                 });

                 // Step 4: Set prevNode.next = newNode
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, {
                         newNode: newNode,
                         highlightPrev: prevNode,
                         highlightNext: nextNode,
                         preInsert: true,
                         highlightPointerNext: prevNode, // Highlight the pointer being changed
                         pointerLabels: { [newNode.data]: "newNode", [prevNode.data]: "prevNode", [nextNode.data]: "nextNode" }
                     }),
                     description: `Langkah 4: Atur prevNode.next ('${prevNode.data}'.next) menunjuk ke newNode`
                 });

                 // --- Perform Actual Insertion ---
                 newNode.next = nextNode;
                 prevNode.next = newNode;

                 // --- Update Tail if newNode is inserted after the old tail ---
                 let newTail = null;
                 if (prevNode === simCircularList.tail) {
                     newTail = newNode; // New node becomes the tail
                     // Step 5 (Optional but good): Show tail update
                     circularVisualizationSteps.push({
                         content: buildStepCircularListVisual({ head: simCircularList.head, tail: newTail }, { // Temporarily show new tail
                             highlight: newNode,
                             pointerLabels: { [newNode.data]: "newNode (new Tail)" }
                         }),
                         description: `Langkah 5: Karena node ditambahkan setelah tail lama, node baru menjadi tail baru.`
                     });
                     simCircularList.tail = newTail; // Update the actual tail reference
                 }

                 // --- Final Step: Show final state ---
                 const finalStepIndex = newTail ? 6 : 5;
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, { // Show the list *after* insertion
                         highlight: newNode, // Highlight the newly inserted node
                         pointerLabels: { [newNode.data]: "newNode" }
                     }),
                     description: `Langkah ${finalStepIndex}: Node '${value}' berhasil disisipkan pada indeks ${index}!`
                 });


                 // --- Show Visualization ---
                 currentCircularStepIndex = 0;
                 showCircularStep(currentCircularStepIndex);
                 circularStepsContainer.style.display = 'block';

                 // --- Clear Inputs ---
                 circularListValueInput.value = '';
                 circularListIndexInput.value = '';
             });
             if (circularListRemoveMiddleBtn) circularListRemoveMiddleBtn.addEventListener('click', () => {
                 const indexStr = circularListIndexInput.value.trim();
                 if (indexStr === '') { alert("Masukkan indeks node yang akan dihapus!"); return; }

                 const index = parseInt(indexStr);
                 if (isNaN(index) || index < 0) { alert("Indeks tidak valid!"); return; }

                 // --- Validate List Not Empty ---
                 if (!simCircularList || !simCircularList.head) {
                     alert("List kosong! Tidak ada yang bisa dihapus."); return;
                 }

                 // --- Calculate Length ---
                 let length = 0;
                 let temp = simCircularList.head;
                 do {
                     length++;
                     temp = temp.next;
                 } while (temp !== simCircularList.head);

                 if (index >= length) {
                      alert(`Indeks ${index} tidak valid. Indeks harus antara 0 dan ${length - 1}.`);
                      return;
                 }

                 // --- Handle Remove Head (Index 0) ---
                 if (index === 0) {
                     // Same as Remove Head logic
                     if (simCircularList.head === simCircularList.tail) { // Single node list
                         simCircularList = { head: null, tail: null };
                     } else {
                         simCircularList.tail.next = simCircularList.head.next; // Tail points to new head
                         simCircularList.head = simCircularList.head.next;     // Update head
                     }
                     renderCircularList(circularListViz);
                     circularListIndexInput.value = '';
                     return;
                 }

                 // --- Find Node Before Deletion Target ---
                 let prevNode = simCircularList.head;
                 let count = 0;
                 while (count < index - 1) { // Go to node at index - 1
                     prevNode = prevNode.next;
                     count++;
                 }

                 // --- Prepare Visualization Steps ---
                 const nodeToDelete = prevNode.next;
                 const nextNode = nodeToDelete.next; // Node after the one to delete (could be head)
                 circularVisualizationSteps = [];
                 let wasTailDeleted = (nodeToDelete === simCircularList.tail);

                 // Step 1: Find node before target (prevNode)
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, {
                         highlightPrev: prevNode,
                         pointerLabels: { [prevNode.data]: "prevNode" }
                     }),
                     description: `Langkah 1: Temukan node sebelum target (indeks ${index - 1}) -> '${prevNode.data}'`
                 });

                 // Step 2: Identify node to delete and the node after it
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, {
                         highlightPrev: prevNode,
                         highlight: nodeToDelete, // Highlight the node to be deleted
                         highlightNext: nextNode,
                         deleteHighlight: nodeToDelete, // Visually mark for deletion
                         pointerLabels: {
                             [prevNode.data]: "prevNode",
                             [nodeToDelete.data]: "nodeToDelete",
                             [nextNode.data]: "nextNode" // nextNode always exists in CLL (points to head if tail's next)
                         }
                     }),
                     description: `Langkah 2: Identifikasi node yang akan dihapus ('${nodeToDelete.data}') dan node setelahnya ('${nextNode.data}')`
                 });

                 // Step 3: Set prevNode.next = nodeToDelete.next (nextNode)
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, {
                         highlightPrev: prevNode,
                         highlightNext: nextNode, // Show the target of the new pointer
                         deleteHighlight: nodeToDelete,
                         highlightPointerNext: prevNode, // Highlight the pointer being changed
                         pointerLabels: {
                             [prevNode.data]: "prevNode",
                             [nodeToDelete.data]: "nodeToDelete",
                             [nextNode.data]: "nextNode"
                         }
                     }),
                     description: `Langkah 3: Atur prevNode.next menunjuk ke nextNode ('${nextNode.data}'), melewati nodeToDelete`
                 });


                 // --- Perform Actual Deletion ---
                 prevNode.next = nextNode;

                 // --- Update Tail if necessary ---
                 let newTail = null;
                 if (wasTailDeleted) {
                     newTail = prevNode; // The node before the deleted tail becomes the new tail
                     // Step 4: Show tail update
                     circularVisualizationSteps.push({
                         content: buildStepCircularListVisual({ head: simCircularList.head, tail: newTail }, { // Show list with new tail
                             highlightPrev: prevNode,
                             highlightNext: nextNode,
                             pointerLabels: { [prevNode.data]: "prevNode (new Tail)", [nextNode.data]: "nextNode" }
                         }),
                         description: `Langkah 4: Karena node tail dihapus, prevNode ('${prevNode.data}') menjadi tail baru.`
                     });
                     simCircularList.tail = newTail; // Update actual tail reference
                 }

                 // --- Final Step: Show final state ---
                 const finalStepIndex = wasTailDeleted ? 5 : 4;
                 circularVisualizationSteps.push({
                     content: buildStepCircularListVisual(simCircularList, { // Show the list *after* deletion
                         highlightPrev: prevNode, // Highlight surrounding nodes
                         highlightNext: nextNode
                     }),
                     description: `Langkah ${finalStepIndex}: Node pada indeks ${index} ('${nodeToDelete.data}') berhasil dihapus!`
                 });

                 // --- Show Visualization ---
                 currentCircularStepIndex = 0;
                 showCircularStep(currentCircularStepIndex);
                 circularStepsContainer.style.display = 'block';

                 // --- Clear Input ---
                 circularListIndexInput.value = '';
             });

             // Circular List Step visualization listeners
             if (circularPrevStepBtn) circularPrevStepBtn.addEventListener('click', () => {
                 showCircularStep(currentCircularStepIndex - 1);
             });
             if (circularNextStepBtn) circularNextStepBtn.addEventListener('click', () => {
                 showCircularStep(currentCircularStepIndex + 1);
             });
             if (circularCloseStepsBtn) circularCloseStepsBtn.addEventListener('click', () => {
                 circularStepsContainer.style.display = 'none';
                 renderCircularList(circularListViz); // Re-render list to show final state
             });


             // Stack Listeners
            if (stackPushBtn) stackPushBtn.addEventListener('click', () => {
                const value = stackValueInput.value.trim(); if (value === '') return;
                simStack.push(value); renderStack(stackVizContainer); stackValueInput.value = '';
            });
            if (stackPopBtn) stackPopBtn.addEventListener('click', () => {
                if (simStack.length === 0) return;
                simStack.pop(); renderStack(stackVizContainer);
            });
            if (stackResetBtn) stackResetBtn.addEventListener('click', () => {
                simStack = []; renderStack(stackVizContainer);
            });

             // Queue Listeners
            if (queueEnqueueBtn) queueEnqueueBtn.addEventListener('click', () => {
                const value = queueValueInput.value.trim(); if (value === '') return;
                simQueue.push(value); renderQueue(queueVizContainer); queueValueInput.value = '';
            });
            if (queueDequeueBtn) queueDequeueBtn.addEventListener('click', () => {
                if (simQueue.length === 0) return;
                simQueue.shift(); renderQueue(queueVizContainer);
            });
            if (queueResetBtn) queueResetBtn.addEventListener('click', () => {
                simQueue = []; renderQueue(queueVizContainer);
            });

            // Tree Listeners
             if (treeAddBtn) treeAddBtn.addEventListener("click", () => {
                 console.log("Tree Add button clicked");
                 const value = treeValueInput.value.trim();
                 if (!value) { alert("Masukkan nilai untuk ditambahkan ke tree"); return; }
                 const newNode = new TreeNode(value);
                 simBinaryTree = insertNodeToTree(simBinaryTree, newNode);
                 renderBinaryTree(treeViz);
                 treeValueInput.value = "";
             });
             if (treeRemoveBtn) treeRemoveBtn.addEventListener("click", () => {
                 console.log("Tree Remove button clicked");
                 const value = treeValueInput.value.trim();
                 if (!value) { alert("Masukkan nilai untuk dihapus"); return; }
                 if (!simBinaryTree) { alert("Tree kosong"); return; }
                 if (!searchInTree(simBinaryTree, value)) { alert("Nilai tidak ditemukan"); return; }
                 simBinaryTree = removeNodeFromTree(simBinaryTree, value);
                 renderBinaryTree(treeViz);
                 treeValueInput.value = "";
             });
             if (treeResetBtn) treeResetBtn.addEventListener("click", () => {
                 console.log("Tree Reset button clicked");
                 simBinaryTree = null;
                 renderBinaryTree(treeViz);
             });

             // BST Listeners
             if (bstAddBtn) bstAddBtn.addEventListener("click", () => {
                 console.log("BST Add button clicked");
                 const value = bstValueInput.value.trim();
                 if (!value) { alert("Masukkan nilai numerik untuk BST"); return; }
                 simBST = insertToBST(simBST, value); // insertToBST handles parsing
                 renderBST(bstViz);
                 bstValueInput.value = "";
             });
             if (bstRemoveBtn) bstRemoveBtn.addEventListener("click", () => {
                  console.log("BST Remove button clicked");
                 const value = bstValueInput.value.trim();
                 if (!value) { alert("Masukkan nilai numerik untuk dihapus"); return; }
                  const numValue = parseInt(value);
                 if (isNaN(numValue)) { alert("Nilai harus numerik."); return; }
                 if (!simBST) { alert("BST kosong"); return; }
                 if (!searchBST(simBST, numValue)) { alert("Nilai tidak ditemukan"); return; }
                 simBST = removeFromBST(simBST, numValue);
                 renderBST(bstViz);
                 bstValueInput.value = "";
             });
             if (bstSearchBtn) bstSearchBtn.addEventListener("click", () => {
                 console.log("BST Search button clicked");
                 const value = bstValueInput.value.trim();
                 if (!value) { alert("Masukkan nilai numerik untuk dicari"); return; }
                 const numValue = parseInt(value);
                 if (isNaN(numValue)) { alert("Nilai harus numerik."); return; }
                 if (!simBST) { alert("BST kosong"); return; }
                 const found = searchBST(simBST, numValue);
                 alert(found ? `Nilai ${numValue} ditemukan` : `Nilai ${numValue} tidak ditemukan`);
             });
             if (bstResetBtn) bstResetBtn.addEventListener("click", () => {
                 console.log("BST Reset button clicked");
                 simBST = null;
                 renderBST(bstViz);
             });

             // Placeholder listeners for AVL and Trie
             function placeholderAlert(structureName) { alert(`Fungsi ${structureName} belum diimplementasikan.`); }
             if (avlAddBtn) avlAddBtn.addEventListener('click', () => placeholderAlert('AVL Tree'));
             if (avlRemoveBtn) avlRemoveBtn.addEventListener('click', () => placeholderAlert('AVL Tree'));
             if (avlResetBtn) avlResetBtn.addEventListener('click', () => { simAVL = null; if(avlViz) avlViz.innerHTML = 'AVL Tree Direset (Placeholder)'; });

             if (trieAddBtn) trieAddBtn.addEventListener('click', () => placeholderAlert('Trie'));
             if (trieSearchBtn) trieSearchBtn.addEventListener('click', () => placeholderAlert('Trie'));
             if (triePrefixBtn) triePrefixBtn.addEventListener('click', () => placeholderAlert('Trie'));
             if (trieResetBtn) trieResetBtn.addEventListener('click', () => { simTrie = null; if(trieViz) trieViz.innerHTML = 'Trie Direset (Placeholder)'; });

             console.log("Event listeners attached.");
         });

    </script>

</body>
</html>
