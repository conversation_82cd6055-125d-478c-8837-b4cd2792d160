<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Linear Search</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .array-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            height: 300px;
            align-items: flex-end;
        }
        .bar {
            width: 30px;
            margin: 0 2px;
            background-color: #4285f4;
            transition: height 0.2s ease;
            position: relative;
        }
        .bar-value {
            position: absolute;
            top: -25px;
            width: 100%;
            text-align: center;
        }
        .current {
            background-color: #ea4335;
        }
        .found {
            background-color: #34a853;
        }
        .not-found {
            background-color: #fbbc05;
        }
        .controls {
            margin: 20px 0;
        }
        .search-input {
            padding: 10px;
            font-size: 16px;
            width: 100px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            padding: 10px 15px;
            margin: 0 5px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #3367d6;
        }
        .explanation {
            text-align: left;
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        .code-block {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            margin: 20px 0;
            overflow-x: auto;
            font-family: monospace;
            white-space: pre;
        }
        .speed-control {
            margin: 15px 0;
        }
        .result {
            margin-top: 20px;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simulasi Linear Search</h1>
        
        <div class="controls">
            <button id="generate">Generate Array Baru</button>
            <input type="number" id="search-value" class="search-input" placeholder="Nilai">
            <button id="search">Mulai Pencarian</button>
            <button id="reset">Reset</button>
        </div>
        
        <div class="speed-control">
            <label for="speed">Kecepatan: </label>
            <input type="range" id="speed" min="10" max="1000" value="300">
        </div>
        
        <div class="array-container" id="array-container"></div>
        
        <div class="result" id="result"></div>
        
        <div class="explanation">
            <h2>Penjelasan Linear Search</h2>
            <p>Linear search (juga disebut sequential search) adalah algoritma pencarian paling sederhana yang memeriksa setiap elemen dalam sebuah daftar secara berurutan sampai nilai yang dicari ditemukan atau seluruh daftar sudah diperiksa.</p>
            
            <p>Langkah-langkah Linear Search:</p>
            <ol>
                <li>Mulai dari elemen pertama dalam array.</li>
                <li>Bandingkan elemen saat ini dengan nilai yang dicari.</li>
                <li>Jika elemen saat ini sama dengan nilai yang dicari, pencarian selesai dan kembalikan indeks elemen tersebut.</li>
                <li>Jika elemen saat ini tidak sama, pindah ke elemen berikutnya.</li>
                <li>Ulangi langkah 2-4 sampai nilai ditemukan atau seluruh array sudah diperiksa.</li>
                <li>Jika seluruh array sudah diperiksa dan nilai tidak ditemukan, kembalikan indikasi bahwa nilai tidak ada dalam array.</li>
            </ol>
            
            <h3>Kode Linear Search:</h3>
            <div class="code-block">
// Fungsi untuk melakukan linear search
function linearSearch(arr, target) {
    // Target adalah nilai yang ingin kita cari
    
    // Loop untuk memeriksa setiap elemen dalam array
    for (let i = 0; i < arr.length; i++) {
        // Bandingkan elemen saat ini dengan target
        if (arr[i] === target) {
            // Jika ditemukan, kembalikan indeks elemen
            return i;
        }
    }
    
    // Jika tidak ditemukan setelah memeriksa semua elemen,
    // kembalikan -1 untuk menunjukkan bahwa target tidak ada dalam array
    return -1;
}
            </div>
            
            <h3>Kompleksitas Waktu:</h3>
            <ul>
                <li>Kasus Terburuk: O(n) - ketika target berada di akhir array atau tidak ada dalam array</li>
                <li>Kasus Terbaik: O(1) - ketika target berada di elemen pertama</li>
                <li>Kasus Rata-rata: O(n/2) = O(n)</li>
            </ul>
            
            <p>Keuntungan Linear Search:</p>
            <ul>
                <li>Sangat sederhana untuk diimplementasikan</li>
                <li>Bekerja pada array yang belum diurutkan</li>
                <li>Tidak memerlukan array untuk disortir terlebih dahulu</li>
                <li>Efisien untuk array kecil</li>
            </ul>
            
            <p>Kekurangan Linear Search:</p>
            <ul>
                <li>Tidak efisien untuk array besar, karena waktu pencarian meningkat seiring dengan ukuran array</li>
                <li>Lebih lambat dibandingkan algoritma pencarian lain seperti Binary Search (pada array yang sudah diurutkan)</li>
            </ul>
        </div>
    </div>

    <script>
        // Mendapatkan elemen-elemen DOM
        const arrayContainer = document.getElementById('array-container');
        const generateButton = document.getElementById('generate');
        const searchButton = document.getElementById('search');
        const resetButton = document.getElementById('reset');
        const speedControl = document.getElementById('speed');
        const searchInput = document.getElementById('search-value');
        const resultDiv = document.getElementById('result');
        
        // Variabel untuk menyimpan array dan status animasi
        let array = [];
        let animationInProgress = false;
        let animationTimeouts = [];
        
        // Fungsi untuk menghasilkan array acak
        function generateRandomArray(size = 10) {
            array = [];
            for (let i = 0; i < size; i++) {
                array.push(Math.floor(Math.random() * 100) + 1); // Angka 1-100
            }
            displayArray(array);
            resultDiv.textContent = '';
            return array;
        }
        
        // Fungsi untuk menampilkan array sebagai batang
        function displayArray(arr, current = -1, found = -1) {
            arrayContainer.innerHTML = '';
            
            // Temukan nilai maksimum untuk penskalaan
            const maxValue = Math.max(...arr);
            
            // Buat batang untuk setiap elemen array
            arr.forEach((value, index) => {
                const bar = document.createElement('div');
                bar.classList.add('bar');
                
                // Atur tinggi batang berdasarkan nilai
                const height = (value / maxValue) * 250;
                bar.style.height = `${height}px`;
                
                // Tambahkan label nilai
                const barValue = document.createElement('div');
                barValue.classList.add('bar-value');
                barValue.textContent = value;
                bar.appendChild(barValue);
                
                // Tambahkan kelas berdasarkan status pencarian
                if (index === current) {
                    bar.classList.add('current');
                }
                if (index === found) {
                    bar.classList.add('found');
                }
                if (current >= arr.length && found === -1) {
                    bar.classList.add('not-found');
                }
                
                arrayContainer.appendChild(bar);
            });
        }
        
        // Fungsi untuk menjalankan animasi linear search
        async function animateLinearSearch(arr, target) {
            if (animationInProgress) return;
            animationInProgress = true;
            
            const speed = parseInt(speedControl.value);
            resultDiv.textContent = `Mencari nilai: ${target}`;
            
            // ===== ALGORITMA LINEAR SEARCH =====
            // Memeriksa setiap elemen dari array satu per satu
            for (let i = 0; i < arr.length; i++) {
                // Buat timeout untuk visualisasi
                const timeoutId = setTimeout(() => {
                    // Tampilkan elemen yang sedang diperiksa
                    displayArray(arr, i);
                    
                    // Jika elemen saat ini sama dengan target
                    if (arr[i] === target) {
                        // Target ditemukan
                        setTimeout(() => {
                            displayArray(arr, i, i);
                            resultDiv.textContent = `Nilai ${target} ditemukan pada indeks ke-${i}`;
                            animationInProgress = false;
                        }, speed);
                    }
                    
                    // Jika ini adalah elemen terakhir dan target tidak ditemukan
                    if (i === arr.length - 1 && arr[i] !== target) {
                        setTimeout(() => {
                            displayArray(arr, arr.length);
                            resultDiv.textContent = `Nilai ${target} tidak ditemukan dalam array`;
                            animationInProgress = false;
                        }, speed);
                    }
                }, i * speed);
                
                animationTimeouts.push(timeoutId);
                
                // Simulasi algoritma linear search
                // Jika elemen saat ini sama dengan target, hentikan pencarian
                if (arr[i] === target) {
                    break;
                }
            }
            // ===== AKHIR ALGORITMA LINEAR SEARCH =====
        }
        
        // Fungsi untuk mereset animasi
        function resetAnimation() {
            // Hentikan semua timeout yang sedang berjalan
            animationTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            animationTimeouts = [];
            animationInProgress = false;
            
            // Tampilkan kembali array awal
            displayArray(array);
            resultDiv.textContent = '';
        }
        
        // Event listener untuk tombol
        generateButton.addEventListener('click', () => {
            resetAnimation();
            generateRandomArray();
        });
        
        searchButton.addEventListener('click', () => {
            const target = parseInt(searchInput.value);
            if (isNaN(target)) {
                resultDiv.textContent = 'Masukkan nilai yang valid';
                return;
            }
            
            resetAnimation();
            animateLinearSearch(array, target);
        });
        
        resetButton.addEventListener('click', resetAnimation);
        
        // Inisialisasi dengan array acak saat halaman dimuat
        generateRandomArray();
    </script>
</body>
</html> 