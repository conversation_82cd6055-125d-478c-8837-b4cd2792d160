<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Selection Sort</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .array-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            height: 300px;
            align-items: flex-end;
        }
        .bar {
            width: 30px;
            margin: 0 2px;
            background-color: #4285f4;
            transition: height 0.2s ease;
            position: relative;
        }
        .bar-value {
            position: absolute;
            top: -25px;
            width: 100%;
            text-align: center;
        }
        .comparing {
            background-color: #ea4335;
        }
        .min {
            background-color: #fbbc05;
        }
        .sorted {
            background-color: #34a853;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            margin: 0 5px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #3367d6;
        }
        .explanation {
            text-align: left;
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        .code-block {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            margin: 20px 0;
            overflow-x: auto;
            font-family: monospace;
            white-space: pre;
        }
        .speed-control {
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simulasi Selection Sort</h1>
        
        <div class="controls">
            <button id="generate">Generate Array Baru</button>
            <button id="sort">Mulai Sorting</button>
            <button id="reset">Reset</button>
        </div>
        
        <div class="speed-control">
            <label for="speed">Kecepatan: </label>
            <input type="range" id="speed" min="10" max="1000" value="300">
        </div>
        
        <div class="array-container" id="array-container"></div>
        
        <div class="explanation">
            <h2>Penjelasan Selection Sort</h2>
            <p>Selection Sort adalah algoritma pengurutan sederhana yang bekerja dengan cara mencari elemen terkecil dari bagian array yang belum terurut dan menempatkannya di posisi yang tepat. Proses ini diulang sampai seluruh array terurut.</p>
            
            <p>Langkah-langkah Selection Sort:</p>
            <ol>
                <li>Temukan elemen terkecil dari array yang belum terurut.</li>
                <li>Tukar elemen terkecil tersebut dengan elemen pertama dari array yang belum terurut.</li>
                <li>Pindahkan batas array yang belum terurut satu posisi ke kanan.</li>
                <li>Ulangi langkah 1-3 sampai seluruh array terurut.</li>
            </ol>
            
            <h3>Kode Selection Sort:</h3>
            <div class="code-block">
// Fungsi untuk melakukan selection sort
function selectionSort(arr) {
    let n = arr.length;
    
    // Loop luar - mengontrol batas array yang belum terurut
    for (let i = 0; i < n - 1; i++) {
        // Temukan indeks elemen terkecil
        let minIndex = i;
        
        // Loop dalam - mencari elemen terkecil
        for (let j = i + 1; j < n; j++) {
            if (arr[j] < arr[minIndex]) {
                minIndex = j;
            }
        }
        
        // Tukar elemen terkecil dengan elemen pertama yang belum terurut
        if (minIndex !== i) {
            let temp = arr[i];
            arr[i] = arr[minIndex];
            arr[minIndex] = temp;
        }
    }
    
    return arr;
}
            </div>
            
            <h3>Kompleksitas Waktu:</h3>
            <ul>
                <li>Kasus Terburuk: O(n²)</li>
                <li>Kasus Terbaik: O(n²)</li>
                <li>Kasus Rata-rata: O(n²)</li>
            </ul>
            
            <p>Meskipun Selection Sort memiliki kompleksitas waktu yang sama dengan Bubble Sort, Selection Sort biasanya lebih efisien karena melakukan lebih sedikit pertukaran elemen. Selection Sort melakukan maksimal n-1 pertukaran, sedangkan Bubble Sort bisa melakukan hingga n(n-1)/2 pertukaran dalam kasus terburuk.</p>
        </div>
    </div>

    <script>
        // Mendapatkan elemen-elemen DOM
        const arrayContainer = document.getElementById('array-container');
        const generateButton = document.getElementById('generate');
        const sortButton = document.getElementById('sort');
        const resetButton = document.getElementById('reset');
        const speedControl = document.getElementById('speed');
        
        // Variabel untuk menyimpan array dan status animasi
        let array = [];
        let animationInProgress = false;
        let animationTimeouts = [];
        
        // Fungsi untuk menghasilkan array acak
        function generateRandomArray(size = 10) {
            array = [];
            for (let i = 0; i < size; i++) {
                array.push(Math.floor(Math.random() * 100) + 1); // Angka 1-100
            }
            displayArray(array);
            return array;
        }
        
        // Fungsi untuk menampilkan array sebagai batang
        function displayArray(arr, comparing = [], minIndex = null, sorted = []) {
            arrayContainer.innerHTML = '';
            
            // Temukan nilai maksimum untuk penskalaan
            const maxValue = Math.max(...arr);
            
            // Buat batang untuk setiap elemen array
            arr.forEach((value, index) => {
                const bar = document.createElement('div');
                bar.classList.add('bar');
                
                // Atur tinggi batang berdasarkan nilai
                const height = (value / maxValue) * 250;
                bar.style.height = `${height}px`;
                
                // Tambahkan label nilai
                const barValue = document.createElement('div');
                barValue.classList.add('bar-value');
                barValue.textContent = value;
                bar.appendChild(barValue);
                
                // Tambahkan kelas untuk elemen yang sedang dibandingkan, minimum, atau sudah terurut
                if (comparing.includes(index)) {
                    bar.classList.add('comparing');
                }
                if (index === minIndex) {
                    bar.classList.add('min');
                }
                if (sorted.includes(index)) {
                    bar.classList.add('sorted');
                }
                
                arrayContainer.appendChild(bar);
            });
        }
        
        // Fungsi untuk menjalankan animasi selection sort
        async function animateSelectionSort(arr) {
            if (animationInProgress) return;
            animationInProgress = true;
            
            // Buat salinan array untuk dimanipulasi
            const animations = [];
            const arrayCopy = [...arr];
            const n = arrayCopy.length;
            const sortedIndices = [];
            
            // Implementasi Selection Sort dengan pencatatan animasi
            // ===== ALGORITMA SELECTION SORT =====
            for (let i = 0; i < n - 1; i++) {
                let minIndex = i;
                
                // Catat awal pencarian minimum baru
                animations.push({
                    type: 'start-search',
                    currentIndex: i,
                    minIndex: minIndex,
                    array: [...arrayCopy],
                    sorted: [...sortedIndices]
                });
                
                // Cari elemen terkecil
                for (let j = i + 1; j < n; j++) {
                    // Catat perbandingan
                    animations.push({
                        type: 'compare',
                        indices: [j],
                        currentIndex: i,
                        minIndex: minIndex,
                        array: [...arrayCopy],
                        sorted: [...sortedIndices]
                    });
                    
                    if (arrayCopy[j] < arrayCopy[minIndex]) {
                        minIndex = j;
                        // Catat update minimum baru
                        animations.push({
                            type: 'new-min',
                            currentIndex: i,
                            minIndex: minIndex,
                            array: [...arrayCopy],
                            sorted: [...sortedIndices]
                        });
                    }
                }
                
                // Tukar elemen jika perlu
                if (minIndex !== i) {
                    // Catat sebelum pertukaran
                    animations.push({
                        type: 'before-swap',
                        indices: [i, minIndex],
                        currentIndex: i,
                        minIndex: minIndex,
                        array: [...arrayCopy],
                        sorted: [...sortedIndices]
                    });
                    
                    // Lakukan pertukaran
                    let temp = arrayCopy[i];
                    arrayCopy[i] = arrayCopy[minIndex];
                    arrayCopy[minIndex] = temp;
                    
                    // Catat setelah pertukaran
                    animations.push({
                        type: 'after-swap',
                        indices: [i, minIndex],
                        currentIndex: i,
                        minIndex: null,
                        array: [...arrayCopy],
                        sorted: [...sortedIndices]
                    });
                }
                
                // Tandai elemen sebagai terurut
                sortedIndices.push(i);
                animations.push({
                    type: 'sorted',
                    currentIndex: i + 1,
                    minIndex: null,
                    array: [...arrayCopy],
                    sorted: [...sortedIndices]
                });
            }
            
            // Tandai elemen terakhir sebagai terurut
            sortedIndices.push(n - 1);
            animations.push({
                type: 'sorted',
                currentIndex: null,
                minIndex: null,
                array: [...arrayCopy],
                sorted: [...sortedIndices]
            });
            // ===== AKHIR ALGORITMA SELECTION SORT =====
            
            // Jalankan animasi
            let delay = 0;
            const speed = parseInt(speedControl.value);
            
            for (let i = 0; i < animations.length; i++) {
                const animation = animations[i];
                
                // Buat timeout untuk setiap langkah animasi
                const timeoutId = setTimeout(() => {
                    const comparing = animation.indices || [];
                    
                    displayArray(
                        animation.array, 
                        comparing, 
                        animation.minIndex, 
                        animation.sorted
                    );
                    
                    // Setelah animasi selesai
                    if (i === animations.length - 1) {
                        animationInProgress = false;
                    }
                }, delay);
                
                animationTimeouts.push(timeoutId);
                delay += speed;
            }
        }
        
        // Fungsi untuk mereset animasi
        function resetAnimation() {
            // Hentikan semua timeout yang sedang berjalan
            animationTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            animationTimeouts = [];
            animationInProgress = false;
            
            // Tampilkan kembali array awal
            displayArray(array);
        }
        
        // Event listener untuk tombol
        generateButton.addEventListener('click', () => {
            resetAnimation();
            generateRandomArray();
        });
        
        sortButton.addEventListener('click', () => {
            resetAnimation();
            animateSelectionSort(array);
        });
        
        resetButton.addEventListener('click', resetAnimation);
        
        // Inisialisasi dengan array acak saat halaman dimuat
        generateRandomArray();
    </script>
</body>
</html>