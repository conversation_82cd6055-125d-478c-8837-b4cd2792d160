<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Selection Sort</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            max-width: 900px;
            width: 100%;
            margin: 20px auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .array-container {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .array-element {
            width: 40px;
            height: 40px;
            margin: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .current {
            background-color: #f44336;
        }
        .min {
            background-color: #2196F3;
        }
        .sorted {
            background-color: #9C27B0;
        }
        .controls {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            margin: 0 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .step-info {
            margin: 20px 0;
            padding: 15px;
            background-color: #f1f1f1;
            border-radius: 4px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>Simulasi Selection Sort</h1>
    
    <div class="container">
        <div class="controls">
            <button id="reset">Reset Array</button>
            <button id="step">Langkah Berikutnya</button>
            <button id="auto">Otomatis</button>
        </div>
        
        <div class="step-info" id="step-info">
            Klik "Langkah Berikutnya" untuk memulai simulasi
        </div>
        
        <div class="array-container" id="array-container">
            <!-- Array elements will be generated here -->
        </div>
    </div>

    <script>
        // Inisialisasi variabel
        let array = [];
        let currentStep = 0;
        let currentIndex = 0;
        let minIndex = 0;
        let sortedIndex = 0;
        let autoSortInterval = null;
        
        // Elemen DOM
        const arrayContainer = document.getElementById('array-container');
        const stepInfo = document.getElementById('step-info');
        const stepButton = document.getElementById('step');
        const resetButton = document.getElementById('reset');
        const autoButton = document.getElementById('auto');
        
        // Inisialisasi array dengan 20 elemen acak
        function initArray() {
            array = [];
            for (let i = 0; i < 20; i++) {
                array.push(Math.floor(Math.random() * 100) + 1);
            }
            currentStep = 0;
            currentIndex = 0;
            minIndex = 0;
            sortedIndex = 0;
            renderArray();
            stepInfo.textContent = "Klik \"Langkah Berikutnya\" untuk memulai simulasi";
            stepButton.disabled = false;
            autoButton.textContent = "Otomatis";
        }
        
        // Render array ke DOM
        function renderArray() {
            arrayContainer.innerHTML = '';
            array.forEach((value, index) => {
                const element = document.createElement('div');
                element.className = 'array-element';
                element.textContent = value;
                
                // Tambahkan kelas sesuai status elemen
                if (index === currentIndex) {
                    element.classList.add('current');
                }
                if (index === minIndex) {
                    element.classList.add('min');
                }
                if (index < sortedIndex) {
                    element.classList.add('sorted');
                }
                
                arrayContainer.appendChild(element);
            });
        }
        
        // Implementasi algoritma Selection Sort (langkah demi langkah)
        function selectionSortStep() {
            // Jika pengurutan sudah selesai
            if (sortedIndex >= array.length - 1) {
                stepInfo.textContent = "Pengurutan selesai!";
                stepButton.disabled = true;
                stopAutoSort();
                sortedIndex = array.length; // Tandai semua elemen sebagai terurut
                renderArray();
                return false;
            }
            
            // Langkah 1: Inisialisasi pencarian minimum baru
            if (currentIndex === sortedIndex) {
                minIndex = sortedIndex;
                stepInfo.textContent = `Langkah ${currentStep + 1}: Mulai pencarian nilai minimum dari indeks ${sortedIndex}`;
                currentStep++;
                currentIndex++;
                renderArray();
                return true;
            }
            
            // Langkah 2: Mencari nilai minimum
            if (currentIndex < array.length) {
                stepInfo.textContent = `Langkah ${currentStep + 1}: Membandingkan ${array[currentIndex]} dengan nilai minimum saat ini ${array[minIndex]}`;
                
                // Jika elemen saat ini lebih kecil dari minimum yang ditemukan sebelumnya
                if (array[currentIndex] < array[minIndex]) {
                    minIndex = currentIndex;
                    stepInfo.textContent += ` → ${array[currentIndex]} lebih kecil, update nilai minimum`;
                }
                
                currentStep++;
                currentIndex++;
                renderArray();
                return true;
            }
            
            // Langkah 3: Tukar elemen jika diperlukan
            if (minIndex !== sortedIndex) {
                stepInfo.textContent = `Langkah ${currentStep + 1}: Tukar ${array[sortedIndex]} dengan nilai minimum ${array[minIndex]}`;
                
                // Tukar elemen
                [array[sortedIndex], array[minIndex]] = [array[minIndex], array[sortedIndex]];
            } else {
                stepInfo.textContent = `Langkah ${currentStep + 1}: ${array[sortedIndex]} sudah berada di posisi yang benar`;
            }
            
            currentStep++;
            sortedIndex++;
            currentIndex = sortedIndex;
            renderArray();
            return true;
        }
        
        // Fungsi untuk otomatis menjalankan algoritma
        function toggleAutoSort() {
            if (autoSortInterval) {
                stopAutoSort();
            } else {
                autoButton.textContent = "Berhenti";
                autoSortInterval = setInterval(() => {
                    const continueSort = selectionSortStep();
                    if (!continueSort) {
                        stopAutoSort();
                    }
                }, 500); // Interval 500ms antar langkah
            }
        }
        
        // Fungsi untuk menghentikan otomatisasi
        function stopAutoSort() {
            clearInterval(autoSortInterval);
            autoSortInterval = null;
            autoButton.textContent = "Otomatis";
        }
        
        // Event listeners
        stepButton.addEventListener('click', selectionSortStep);
        resetButton.addEventListener('click', initArray);
        autoButton.addEventListener('click', toggleAutoSort);
        
        // Inisialisasi array saat halaman dimuat
        initArray();
    </script>
</body>
</html>