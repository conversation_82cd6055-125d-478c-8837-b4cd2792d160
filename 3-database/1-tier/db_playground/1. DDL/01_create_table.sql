-- 01_create_table.sql
-- DDL: CREATE TABLE
-- Perintah ini digunakan untuk membuat tabel baru dalam database.
-- <PERSON><PERSON> contoh ini, kita akan membuat tabel 'kelas_tahfidz' yang berisi informasi tentang kelas tahfidz.

-- Membuat tabel baru bernama 'kelas_tahfidz'
CREATE TABLE kelas_tahfidz (
    id SERIAL PRIMARY KEY,                -- Kolom id sebagai primary key dengan auto-increment
    nama VARCHAR(100) NOT NULL,           -- <PERSON><PERSON> kelas (wajib diisi)
    tingkat VARCHAR(50) NOT NULL,         -- Tingkat kelas (wajib diisi)
    tahun_ajaran VARCHAR(20) NOT NULL,    -- Ta<PERSON> ajaran (wajib diisi)
    kurikulum TEXT,                       -- <PERSON><PERSON><PERSON><PERSON> kurikulum (opsional)
    kapasitas INTEGER DEFAULT 30,         -- Kapasitas kelas dengan nilai default 30
    tanggal_mulai DATE,                   -- <PERSON>gal mulai kelas
    tanggal_selesai DATE,                 -- Tanggal selesai kelas
    aktif BOOLEAN DEFAULT TRUE,           -- Status aktif dengan nilai default TRUE
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- Waktu pembuatan record
);

-- Menambahkan komentar pada tabel
COMMENT ON TABLE kelas_tahfidz IS 'Tabel untuk menyimpan informasi kelas tahfidz';

-- Menambahkan komentar pada kolom
COMMENT ON COLUMN kelas_tahfidz.id IS 'ID unik untuk setiap kelas';
COMMENT ON COLUMN kelas_tahfidz.nama IS 'Nama kelas tahfidz';
COMMENT ON COLUMN kelas_tahfidz.tingkat IS 'Tingkat kelas (pemula, menengah, lanjutan)';
COMMENT ON COLUMN kelas_tahfidz.tahun_ajaran IS 'Tahun ajaran kelas (contoh: 2024/2025)';
COMMENT ON COLUMN kelas_tahfidz.kurikulum IS 'Deskripsi kurikulum yang digunakan';
COMMENT ON COLUMN kelas_tahfidz.kapasitas IS 'Kapasitas maksimum siswa dalam kelas';
COMMENT ON COLUMN kelas_tahfidz.tanggal_mulai IS 'Tanggal mulai kelas';
COMMENT ON COLUMN kelas_tahfidz.tanggal_selesai IS 'Tanggal selesai kelas';
COMMENT ON COLUMN kelas_tahfidz.aktif IS 'Status aktif kelas (true = aktif, false = tidak aktif)';
COMMENT ON COLUMN kelas_tahfidz.created_at IS 'Waktu pembuatan record';
