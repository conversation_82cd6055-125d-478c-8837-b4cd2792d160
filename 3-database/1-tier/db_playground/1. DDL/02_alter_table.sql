-- 02_alter_table.sql
-- DDL: ALTER TABLE
-- Perintah ini digunakan untuk mengubah struktur tabel yang sudah ada.
-- <PERSON><PERSON> contoh ini, kita akan memodifikasi tabel 'kelas_tahfidz' yang telah dibuat sebelumnya.

-- Menambahkan kolom baru 'lokasi' ke tabel kelas_tahfidz
ALTER TABLE kelas_tahfidz
ADD COLUMN lokasi VARCHAR(100);

-- Menambahkan kolom baru 'pengajar_id' dengan foreign key constraint
ALTER TABLE kelas_tahfidz
ADD COLUMN pengajar_id INTEGER REFERENCES pengajar(id);

-- Mengubah tipe data kolom 'kapasitas' dari INTEGER menjadi SMALLINT
ALTER TABLE kelas_tahfidz
ALTER COLUMN kapasitas TYPE SMALLINT;

-- Mengubah nilai default kolom 'kapasitas' menjadi 25
ALTER TABLE kelas_tahfidz
ALTER COLUMN kapasitas SET DEFAULT 25;

-- Menambahkan constraint NOT NULL pada kolom 'lokasi'
ALTER TABLE kelas_tahfidz
ALTER COLUMN lokasi SET NOT NULL;

-- Menambahkan constraint CHECK untuk memastikan kapasitas > 0
ALTER TABLE kelas_tahfidz
ADD CONSTRAINT check_kapasitas_positive CHECK (kapasitas > 0);

-- Menambahkan constraint UNIQUE pada kolom nama dan tahun_ajaran
ALTER TABLE kelas_tahfidz
ADD CONSTRAINT unique_kelas_tahun UNIQUE (nama, tahun_ajaran);

-- Menambahkan komentar pada kolom baru
COMMENT ON COLUMN kelas_tahfidz.lokasi IS 'Lokasi pelaksanaan kelas';
COMMENT ON COLUMN kelas_tahfidz.pengajar_id IS 'ID pengajar yang mengajar kelas ini';
