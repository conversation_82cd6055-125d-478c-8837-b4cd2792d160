-- 03_rename_table.sql
-- DDL: RENAME TABLE
-- Perintah ini digunakan untuk mengubah nama tabel yang sudah ada.
-- <PERSON><PERSON> contoh ini, kita akan mengubah nama tabel 'kelas_tahfidz' menjadi 'program_tahfidz'.

-- Mengubah nama tabel dari 'kelas_tahfidz' menjadi 'program_tahfidz'
ALTER TABLE kelas_tahfidz RENAME TO program_tahfidz;

-- Mengubah nama constraint pada tabel yang telah diubah namanya
ALTER TABLE program_tahfidz RENAME CONSTRAINT check_kapasitas_positive TO check_program_kapasitas_positive;
ALTER TABLE program_tahfidz RENAME CONSTRAINT unique_kelas_tahun TO unique_program_tahun;

-- <PERSON><PERSON><PERSON> nama kolom 'nama' menjadi 'nama_program'
ALTER TABLE program_tahfidz RENAME COLUMN nama TO nama_program;

-- <PERSON><PERSON><PERSON> nama sequence yang digunakan untuk kolom id (jika menggunakan SERIAL)
ALTER SEQUENCE kelas_tahfidz_id_seq RENAME TO program_tahfidz_id_seq;
