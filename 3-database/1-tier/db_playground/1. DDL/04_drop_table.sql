-- 04_drop_table.sql
-- DDL: DROP TABLE
-- <PERSON>intah ini digunakan untuk menghapus tabel dari database.
-- <PERSON><PERSON> contoh ini, kita akan menghapus tabel 'program_tahfidz' yang telah dibuat sebelumnya.

-- Menghapus tabel 'program_tahfidz' jika ada
DROP TABLE IF EXISTS program_tahfidz;

-- Menghapus tabel dengan CASCADE (akan menghapus semua objek yang bergantung pada tabel ini)
-- HATI-HATI: Gunakan CASCADE dengan sangat hati-hati karena dapat menghapus data terkait
-- DROP TABLE IF EXISTS program_tahfidz CASCADE;

-- Con<PERSON><PERSON> menghapus beberapa tabel sekaligus
-- DROP TABLE IF EXISTS tabel1, tabel2, tabel3;

-- Catatan: Setelah tabel dihapus, semua data di dalamnya juga akan hilang
-- dan tidak dapat dikembalikan kecuali dari backup.
