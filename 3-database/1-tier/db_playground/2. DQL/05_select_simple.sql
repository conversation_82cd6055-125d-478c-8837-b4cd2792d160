-- 05_select_simple.sql
-- DQL: SELECT Sederhana
-- <PERSON>intah SELECT digunakan untuk mengambil data dari tabel.
-- <PERSON><PERSON> contoh ini, kita akan mengambil data dari tabel 'siswa'.

-- Mengambil semua kolom dari tabel siswa
SELECT * FROM siswa;

-- Mengambil kolom tertentu dari tabel siswa
SELECT id, nama, gender FROM siswa;

-- Mengambil data dengan alias kolom (memberikan nama lain pada kolom hasil)
SELECT 
    id AS "ID Siswa",
    nama AS "Nama Lengkap",
    gender AS "Jenis Kelamin"
FROM siswa;

-- Mengambil data dengan perhitungan sederhana
SELECT 
    nama,
    EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM tanggal_lahir) AS "Perkiraan Usia"
FROM siswa;

-- Mengambil data unik (tanpa duplikasi)
SELECT DISTINCT gender FROM siswa;

-- Menggabungkan beberapa kolom menjadi satu
SELECT 
    id,
    CONCAT(nama, ' (', gender, ')') AS "Nama dan Gender"
FROM siswa;
