-- 06_select_where.sql
-- DQL: SELECT dengan WHERE
-- Klausa WHERE digunakan untuk memfilter data berdasarkan kondisi tertentu.
-- <PERSON><PERSON> contoh ini, kita akan mengambil data dari tabel 'siswa' dengan berbagai kondisi filter.

-- Mengambil siswa dengan gender tertentu
SELECT id, nama, gender
FROM siswa
WHERE gender = 'laki-laki';

-- Mengambil siswa dari halaqoh tertentu
SELECT id, nama, halaqoh_id
FROM siswa
WHERE halaqoh_id = 5;

-- Mengambil siswa dengan nama yang mengandung kata tertentu
SELECT id, nama
FROM siswa
WHERE nama LIKE '%Ahmad%';

-- Mengambil siswa dengan beberapa kondisi (AND)
SELECT id, nama, gender, halaqoh_id
FROM siswa
WHERE gender = 'perempuan' AND halaqoh_id = 10;

-- Mengambil siswa dengan salah satu dari beberapa kondisi (OR)
SELECT id, nama, halaqoh_id
FROM siswa
WHERE halaqoh_id = 3 OR halaqoh_id = 7;

-- Mengambil siswa dengan rentang nilai (BETWEEN)
SELECT id, nama, nilai_ujian
FROM siswa
WHERE nilai_ujian BETWEEN 80 AND 100;

-- Mengambil siswa dengan nilai dalam daftar tertentu (IN)
SELECT id, nama, halaqoh_id
FROM siswa
WHERE halaqoh_id IN (1, 3, 5, 7);

-- Mengambil siswa dengan nilai yang tidak dalam daftar tertentu (NOT IN)
SELECT id, nama, halaqoh_id
FROM siswa
WHERE halaqoh_id NOT IN (2, 4, 6, 8);

-- Mengambil siswa dengan nilai yang tidak NULL
SELECT id, nama, email
FROM siswa
WHERE email IS NOT NULL;

-- Mengambil siswa dengan kombinasi kondisi kompleks
SELECT id, nama, gender, halaqoh_id
FROM siswa
WHERE (gender = 'laki-laki' AND halaqoh_id < 5)
   OR (gender = 'perempuan' AND halaqoh_id > 10);
