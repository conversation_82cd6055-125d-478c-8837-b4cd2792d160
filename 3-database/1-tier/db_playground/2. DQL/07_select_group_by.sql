-- 07_select_group_by.sql
-- DQL: SELECT dengan GROUP BY
-- Klausa GROUP BY digunakan untuk mengelompokkan data berdasarkan satu atau lebih kolom.
-- Biasanya digunakan bersama fungsi agregat seperti COUNT, SUM, AVG, MAX, MIN.

-- Menghitung jumlah siswa per gender
SELECT 
    gender,
    COUNT(*) AS jumlah_siswa
FROM 
    siswa
GROUP BY 
    gender;

-- Menghitung jumlah siswa per halaqoh
SELECT 
    halaqoh_id,
    COUNT(*) AS jumlah_siswa
FROM 
    siswa
GROUP BY 
    halaqoh_id
ORDER BY 
    halaqoh_id;

-- Menghitung jumlah setoran per jenis
SELECT 
    jenis,
    COUNT(*) AS jumlah_setoran
FROM 
    setoran
GROUP BY 
    jenis;

-- Menghitung jumlah setoran per siswa
SELECT 
    siswa_id,
    COUNT(*) AS jumlah_setoran
FROM 
    setoran
GROUP BY 
    siswa_id
ORDER BY 
    jumlah_setoran DESC;

-- Menghitung rata-rata halaman per jenis setoran
SELECT 
    jenis,
    AVG(halaman_akhir - halaman_awal + 1) AS rata_rata_halaman
FROM 
    setoran
GROUP BY 
    jenis;

-- Menghitung jumlah setoran per siswa dan jenis
SELECT 
    siswa_id,
    jenis,
    COUNT(*) AS jumlah_setoran
FROM 
    setoran
GROUP BY 
    siswa_id, jenis
ORDER BY 
    siswa_id, jenis;

-- Menghitung jumlah setoran per bulan
SELECT 
    EXTRACT(YEAR FROM waktu_setoran) AS tahun,
    EXTRACT(MONTH FROM waktu_setoran) AS bulan,
    COUNT(*) AS jumlah_setoran
FROM 
    setoran
GROUP BY 
    EXTRACT(YEAR FROM waktu_setoran),
    EXTRACT(MONTH FROM waktu_setoran)
ORDER BY 
    tahun, bulan;
