-- 08_select_having.sql
-- DQL: SELECT dengan HAVING
-- Klausa HAVING digunakan untuk memfilter hasil GROUP BY berdasarkan kondisi agregat.
-- Be<PERSON><PERSON> dengan WHERE yang memfilter baris sebelum pengelompokan,
-- HAVING memfilter hasil setelah pengelompokan.

-- <PERSON><PERSON>i halaqoh yang memiliki lebih dari 10 siswa
SELECT 
    halaqoh_id,
    COUNT(*) AS jumlah_siswa
FROM 
    siswa
GROUP BY 
    halaqoh_id
HAVING 
    COUNT(*) > 10
ORDER BY 
    jumlah_siswa DESC;

-- <PERSON><PERSON><PERSON> siswa yang memiliki setoran lebih dari 25 kali
SELECT 
    siswa_id,
    COUNT(*) AS jumlah_setoran
FROM 
    setoran
GROUP BY 
    siswa_id
HAVING 
    COUNT(*) > 25
ORDER BY 
    jumlah_setoran DESC;

-- <PERSON><PERSON><PERSON> jenis setoran dengan rata-rata halaman lebih dari 3
SELECT 
    jenis,
    AVG(halaman_akhir - halaman_awal + 1) AS rata_rata_halaman
FROM 
    setoran
GROUP BY 
    jenis
HAVING 
    AVG(halaman_akhir - halaman_awal + 1) > 3;

-- Mencari pengajar yang menerima setoran dari lebih dari 5 siswa berbeda
SELECT 
    pengajar_id,
    COUNT(DISTINCT siswa_id) AS jumlah_siswa
FROM 
    setoran
GROUP BY 
    pengajar_id
HAVING 
    COUNT(DISTINCT siswa_id) > 5
ORDER BY 
    jumlah_siswa DESC;

-- Mencari siswa yang memiliki setoran untuk semua jenis (ziyadah, rabth, ikhtibar)
SELECT 
    siswa_id,
    COUNT(DISTINCT jenis) AS jumlah_jenis_setoran
FROM 
    setoran
GROUP BY 
    siswa_id
HAVING 
    COUNT(DISTINCT jenis) = 3;

-- Mencari halaqoh dengan gender tertentu dan memiliki lebih dari 8 siswa
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    h.gender,
    COUNT(s.id) AS jumlah_siswa
FROM 
    halaqoh h
    JOIN siswa s ON h.id = s.halaqoh_id
GROUP BY 
    h.id, h.nama, h.gender
HAVING 
    h.gender = 'perempuan' AND COUNT(s.id) > 8
ORDER BY 
    jumlah_siswa DESC;
