-- 09_select_order_by.sql
-- DQL: SELECT dengan ORDER BY
-- Klausa ORDER BY digunakan untuk mengurutkan hasil query berdasarkan satu atau lebih kolom.
-- Data dapat diurutkan secara ascending (ASC) atau descending (DESC).

-- Mengurutkan siswa berdasarkan nama (ascending/menaik - default)
SELECT 
    id, nama, gender
FROM 
    siswa
ORDER BY 
    nama;

-- Mengurutkan siswa berdasarkan nama (descending/menurun)
SELECT 
    id, nama, gender
FROM 
    siswa
ORDER BY 
    nama DESC;

-- Mengurutkan berdasarkan beberapa kolom
-- Pertama berdasarkan gender, kemudian berdasarkan nama
SELECT 
    id, nama, gender, halaqoh_id
FROM 
    siswa
ORDER BY 
    gender, nama;

-- Mengurutkan dengan arah berbeda untuk kolom berbeda
-- Gender ascending, nama descending
SELECT 
    id, nama, gender
FROM 
    siswa
ORDER BY 
    gender ASC, nama DESC;

-- Mengurutkan berdasarkan posisi kolom (tidak disarankan, kurang jelas)
SELECT 
    id, nama, gender
FROM 
    siswa
ORDER BY 
    3, 2; -- Urut berdasarkan kolom ke-3 (gender), lalu kolom ke-2 (nama)

-- Mengurutkan berdasarkan hasil perhitungan
SELECT 
    id, 
    nama, 
    halaman_akhir - halaman_awal + 1 AS jumlah_halaman
FROM 
    setoran
ORDER BY 
    jumlah_halaman DESC;

-- Mengurutkan berdasarkan tanggal
SELECT 
    id, 
    siswa_id, 
    waktu_setoran
FROM 
    setoran
ORDER BY 
    waktu_setoran DESC;

-- Mengurutkan dengan NULLS FIRST/LAST
-- Nilai NULL akan ditampilkan di awal atau akhir hasil
SELECT 
    id, 
    nama, 
    email
FROM 
    siswa
ORDER BY 
    email NULLS FIRST; -- Siswa tanpa email ditampilkan lebih dulu

-- Mengurutkan dengan kondisi kustom menggunakan CASE
SELECT 
    id, 
    nama, 
    gender
FROM 
    siswa
ORDER BY 
    CASE 
        WHEN gender = 'laki-laki' THEN 1
        WHEN gender = 'perempuan' THEN 2
        ELSE 3
    END;
