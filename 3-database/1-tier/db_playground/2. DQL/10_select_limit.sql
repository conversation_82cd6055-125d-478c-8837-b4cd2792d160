-- 10_select_limit.sql
-- DQL: SELECT dengan LIMIT
-- Klausa LIMIT digunakan untuk membatasi jumlah baris yang dikembalikan oleh query.
-- Sangat berguna untuk pagination atau mengambil sejumlah data teratas.

-- Mengambil 10 siswa pertama
SELECT 
    id, nama, gender
FROM 
    siswa
LIMIT 10;

-- Mengambil 5 siswa dengan nama abjad terdepan
SELECT 
    id, nama, gender
FROM 
    siswa
ORDER BY 
    nama
LIMIT 5;

-- Mengambil 5 setoran terbaru
SELECT 
    id, siswa_id, jenis, waktu_setoran
FROM 
    setoran
ORDER BY 
    waktu_setoran DESC
LIMIT 5;

-- Menggunakan OFFSET untuk pagination
-- Mengambil 10 siswa, mulai dari siswa ke-11
SELECT 
    id, nama, gender
FROM 
    siswa
LIMIT 10 OFFSET 10;

-- Mengambil 10 siswa, mulai dari siswa ke-21
SELECT 
    id, nama, gender
FROM 
    siswa
LIMIT 10 OFFSET 20;

-- Mengambil 5 siswa dengan jumlah setoran terbanyak
SELECT 
    s.id, 
    s.nama, 
    COUNT(st.id) AS jumlah_setoran
FROM 
    siswa s
    LEFT JOIN setoran st ON s.id = st.siswa_id
GROUP BY 
    s.id, s.nama
ORDER BY 
    jumlah_setoran DESC
LIMIT 5;

-- Mengambil 3 halaqoh dengan jumlah siswa terbanyak
SELECT 
    h.id, 
    h.nama, 
    COUNT(s.id) AS jumlah_siswa
FROM 
    halaqoh h
    LEFT JOIN siswa s ON h.id = s.halaqoh_id
GROUP BY 
    h.id, h.nama
ORDER BY 
    jumlah_siswa DESC
LIMIT 3;

-- Mengambil 1 siswa dengan setoran terbanyak untuk setiap jenis
SELECT DISTINCT ON (jenis)
    jenis,
    siswa_id,
    COUNT(*) AS jumlah_setoran
FROM 
    setoran
GROUP BY 
    jenis, siswa_id
ORDER BY 
    jenis, jumlah_setoran DESC
LIMIT 3; -- Akan mengambil 1 baris untuk setiap jenis setoran (maksimal 3 jenis)
