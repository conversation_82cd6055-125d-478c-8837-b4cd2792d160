-- 11_select_join.sql
-- DQL: SELECT dengan JOIN
-- JOIN digunakan untuk menggabungkan data dari dua atau lebih tabel berdasarkan kolom yang terkait.
-- <PERSON><PERSON> contoh ini, kita akan menggunakan JOIN tanpa menentukan jenisnya (defaultnya adalah INNER JOIN).

-- Menggabungkan data siswa dengan halaqoh
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    s.gender AS gender_siswa,
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh
FROM 
    siswa s
    JOIN halaqoh h ON s.halaqoh_id = h.id
LIMIT 10;

-- Menggabungkan data siswa, halaqoh, dan pengajar
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    h.nama AS nama_halaqoh,
    p.nama AS nama_pengajar
FROM 
    siswa s
    JOIN halaqoh h ON s.halaqoh_id = h.id
    JOIN pengajar p ON h.pengajar_id = p.id
LIMIT 10;

-- Menggabungkan data siswa dengan setoran
SELECT 
    s.nama AS nama_siswa,
    st.jenis AS jenis_setoran,
    st.waktu_setoran,
    st.halaman_awal,
    st.halaman_akhir
FROM 
    siswa s
    JOIN setoran st ON s.id = st.siswa_id
ORDER BY 
    st.waktu_setoran DESC
LIMIT 10;

-- Menggabungkan data setoran dengan pengajar
SELECT 
    st.id AS setoran_id,
    st.jenis AS jenis_setoran,
    st.waktu_setoran,
    p.nama AS nama_pengajar
FROM 
    setoran st
    JOIN pengajar p ON st.pengajar_id = p.id
ORDER BY 
    st.waktu_setoran DESC
LIMIT 10;

-- Menggabungkan data setoran dengan siswa dan pengajar
SELECT 
    st.id AS setoran_id,
    s.nama AS nama_siswa,
    p.nama AS nama_pengajar,
    st.jenis AS jenis_setoran,
    st.waktu_setoran
FROM 
    setoran st
    JOIN siswa s ON st.siswa_id = s.id
    JOIN pengajar p ON st.pengajar_id = p.id
ORDER BY 
    st.waktu_setoran DESC
LIMIT 10;

-- Menggabungkan data setoran dengan quranidn untuk mendapatkan informasi ayat
SELECT 
    st.id AS setoran_id,
    s.nama AS nama_siswa,
    st.jenis AS jenis_setoran,
    q_awal.surah_name AS surah_mulai,
    q_awal.verse_id AS ayat_mulai,
    q_akhir.surah_name AS surah_akhir,
    q_akhir.verse_id AS ayat_akhir
FROM 
    setoran st
    JOIN siswa s ON st.siswa_id = s.id
    JOIN quranidn q_awal ON st.surah_mulai = q_awal.surah_id AND st.verse_mulai = q_awal.verse_id
    JOIN quranidn q_akhir ON st.surah_akhir = q_akhir.surah_id AND st.verse_akhir = q_akhir.verse_id
ORDER BY 
    st.waktu_setoran DESC
LIMIT 10;
