-- 12_select_inner_join.sql
-- DQL: SELECT dengan INNER JOIN
-- INNER JOIN menge<PERSON>likan baris dari kedua tabel yang memiliki nilai yang cocok di kolom yang dijoinkan.
-- <PERSON><PERSON> yang tidak memiliki pasangan di tabel lain tidak akan muncul di hasil query.

-- INNER JOIN antara siswa dan halaqoh
-- <PERSON>ya menampilkan siswa yang memiliki halaqoh
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh
FROM 
    siswa s
    INNER JOIN halaqoh h ON s.halaqoh_id = h.id
ORDER BY 
    s.nama
LIMIT 10;

-- INNER JOIN antara halaqoh dan pengajar
-- <PERSON><PERSON> menampilkan halaqoh yang memiliki pengajar
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    p.id AS pengajar_id,
    p.nama AS nama_pengajar
FROM 
    halaqoh h
    INNER JOIN pengajar p ON h.pengajar_id = p.id
ORDER BY 
    h.nama
LIMIT 10;

-- INNER JOIN antara siswa dan setoran
-- Hanya menampilkan siswa yang memiliki setoran
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    COUNT(st.id) AS jumlah_setoran
FROM 
    siswa s
    INNER JOIN setoran st ON s.id = st.siswa_id
GROUP BY 
    s.id, s.nama
ORDER BY 
    jumlah_setoran DESC
LIMIT 10;

-- INNER JOIN antara tiga tabel: siswa, halaqoh, dan pengajar
-- Hanya menampilkan data yang memiliki relasi di ketiga tabel
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    h.nama AS nama_halaqoh,
    p.nama AS nama_pengajar
FROM 
    siswa s
    INNER JOIN halaqoh h ON s.halaqoh_id = h.id
    INNER JOIN pengajar p ON h.pengajar_id = p.id
ORDER BY 
    s.nama
LIMIT 10;

-- INNER JOIN antara setoran dan quranidn
-- Hanya menampilkan setoran yang memiliki data ayat yang cocok
SELECT 
    st.id AS setoran_id,
    st.siswa_id,
    st.jenis,
    q.surah_name,
    q.verse_id,
    q.verse_text
FROM 
    setoran st
    INNER JOIN quranidn q ON st.surah_mulai = q.surah_id AND st.verse_mulai = q.verse_id
ORDER BY 
    st.waktu_setoran DESC
LIMIT 10;

-- INNER JOIN dengan kondisi tambahan di klausa WHERE
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    h.nama AS nama_halaqoh
FROM 
    siswa s
    INNER JOIN halaqoh h ON s.halaqoh_id = h.id
WHERE 
    s.gender = 'laki-laki' AND h.gender = 'laki-laki'
ORDER BY 
    s.nama
LIMIT 10;
