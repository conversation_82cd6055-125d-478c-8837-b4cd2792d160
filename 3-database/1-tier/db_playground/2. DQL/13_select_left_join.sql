-- 13_select_left_join.sql
-- DQL: SELECT dengan LEFT JOIN
-- LEFT JOIN mengembalikan semua baris dari tabel kiri (tabel pertama) dan baris yang cocok dari tabel kanan (tabel kedua).
-- <PERSON><PERSON> tidak ada kecocokan di tabel kanan, hasilnya akan NULL untuk kolom dari tabel kanan.

-- LEFT JOIN antara siswa dan setoran
-- Menampilkan semua siswa, termasuk yang belum memiliki setoran
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    COUNT(st.id) AS jumlah_setoran
FROM 
    siswa s
    LEFT JOIN setoran st ON s.id = st.siswa_id
GROUP BY 
    s.id, s.nama
ORDER BY 
    jumlah_setoran ASC
LIMIT 10;

-- LEFT JOIN untuk menemukan siswa yang belum pernah melakukan setoran
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    s.gender
FROM 
    siswa s
    LEFT JOIN setoran st ON s.id = st.siswa_id
WHERE 
    st.id IS NULL
ORDER BY 
    s.nama;

-- LEFT JOIN antara halaqoh dan siswa
-- Menampilkan semua halaqoh, termasuk yang belum memiliki siswa
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    COUNT(s.id) AS jumlah_siswa
FROM 
    halaqoh h
    LEFT JOIN siswa s ON h.id = s.halaqoh_id
GROUP BY 
    h.id, h.nama
ORDER BY 
    jumlah_siswa ASC;

-- LEFT JOIN untuk menemukan halaqoh yang belum memiliki siswa
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    h.gender
FROM 
    halaqoh h
    LEFT JOIN siswa s ON h.id = s.halaqoh_id
WHERE 
    s.id IS NULL
ORDER BY 
    h.nama;

-- LEFT JOIN antara pengajar dan halaqoh
-- Menampilkan semua pengajar, termasuk yang belum memiliki halaqoh
SELECT 
    p.id AS pengajar_id,
    p.nama AS nama_pengajar,
    COUNT(h.id) AS jumlah_halaqoh
FROM 
    pengajar p
    LEFT JOIN halaqoh h ON p.id = h.pengajar_id
GROUP BY 
    p.id, p.nama
ORDER BY 
    jumlah_halaqoh ASC;

-- LEFT JOIN untuk menemukan pengajar yang belum memiliki halaqoh
SELECT 
    p.id AS pengajar_id,
    p.nama AS nama_pengajar,
    p.gender
FROM 
    pengajar p
    LEFT JOIN halaqoh h ON p.id = h.pengajar_id
WHERE 
    h.id IS NULL
ORDER BY 
    p.nama;

-- LEFT JOIN dengan beberapa tabel
-- Menampilkan semua siswa, informasi halaqoh mereka (jika ada), dan pengajar (jika ada)
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    h.nama AS nama_halaqoh,
    p.nama AS nama_pengajar
FROM 
    siswa s
    LEFT JOIN halaqoh h ON s.halaqoh_id = h.id
    LEFT JOIN pengajar p ON h.pengajar_id = p.id
ORDER BY 
    s.nama
LIMIT 10;
