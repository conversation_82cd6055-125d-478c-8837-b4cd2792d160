-- 14_select_right_join.sql
-- DQL: SELECT dengan RIGHT JOIN
-- RIGHT JOIN mengembalikan semua baris dari tabel kanan (tabel kedua) dan baris yang cocok dari tabel kiri (tabel pertama).
-- <PERSON><PERSON> tidak ada kecocokan di tabel kiri, hasilnya akan NULL untuk kolom dari tabel kiri.

-- RIGHT JOIN antara setoran dan siswa
-- Menampilkan semua siswa, termasuk yang belum memiliki setoran
-- (<PERSON>il sama dengan LEFT JOIN siswa dan setoran, tapi urutan tabel dibalik)
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    COUNT(st.id) AS jumlah_setoran
FROM 
    setoran st
    RIGHT JOIN siswa s ON st.siswa_id = s.id
GROUP BY 
    s.id, s.nama
ORDER BY 
    jumlah_setoran ASC
LIMIT 10;

-- RIGHT JOIN untuk menemukan siswa yang belum pernah melakukan setoran
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    s.gender
FROM 
    setoran st
    RIGHT JOIN siswa s ON st.siswa_id = s.id
WHERE 
    st.id IS NULL
ORDER BY 
    s.nama;

-- RIGHT JOIN antara siswa dan halaqoh
-- Menampilkan semua halaqoh, termasuk yang belum memiliki siswa
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    COUNT(s.id) AS jumlah_siswa
FROM 
    siswa s
    RIGHT JOIN halaqoh h ON s.halaqoh_id = h.id
GROUP BY 
    h.id, h.nama
ORDER BY 
    jumlah_siswa ASC;

-- RIGHT JOIN untuk menemukan halaqoh yang belum memiliki siswa
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    h.gender
FROM 
    siswa s
    RIGHT JOIN halaqoh h ON s.halaqoh_id = h.id
WHERE 
    s.id IS NULL
ORDER BY 
    h.nama;

-- RIGHT JOIN antara halaqoh dan pengajar
-- Menampilkan semua pengajar, termasuk yang belum memiliki halaqoh
SELECT 
    p.id AS pengajar_id,
    p.nama AS nama_pengajar,
    COUNT(h.id) AS jumlah_halaqoh
FROM 
    halaqoh h
    RIGHT JOIN pengajar p ON h.pengajar_id = p.id
GROUP BY 
    p.id, p.nama
ORDER BY 
    jumlah_halaqoh ASC;

-- RIGHT JOIN untuk menemukan pengajar yang belum memiliki halaqoh
SELECT 
    p.id AS pengajar_id,
    p.nama AS nama_pengajar,
    p.gender
FROM 
    halaqoh h
    RIGHT JOIN pengajar p ON h.pengajar_id = p.id
WHERE 
    h.id IS NULL
ORDER BY 
    p.nama;

-- RIGHT JOIN dengan beberapa tabel
-- Menampilkan semua pengajar, halaqoh yang mereka ajar (jika ada), dan siswa di halaqoh tersebut (jika ada)
SELECT 
    p.id AS pengajar_id,
    p.nama AS nama_pengajar,
    h.nama AS nama_halaqoh,
    s.nama AS nama_siswa
FROM 
    siswa s
    RIGHT JOIN halaqoh h ON s.halaqoh_id = h.id
    RIGHT JOIN pengajar p ON h.pengajar_id = p.id
ORDER BY 
    p.nama, h.nama, s.nama
LIMIT 20;
