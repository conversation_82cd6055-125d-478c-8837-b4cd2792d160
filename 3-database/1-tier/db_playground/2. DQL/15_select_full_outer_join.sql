-- 15_select_full_outer_join.sql
-- DQL: SELECT dengan FULL OUTER JOIN
-- FULL OUTER JOIN mengembalikan semua baris dari kedua tabel, terlepas dari apakah ada kecocokan atau tidak.
-- <PERSON><PERSON> tidak ada kecocokan, hasilnya akan NULL untuk kolom dari tabel yang tidak memiliki data yang cocok.

-- FULL OUTER JOIN antara siswa dan setoran
-- Menampilkan semua siswa dan semua setoran, termasuk siswa tanpa setoran dan setoran tanpa siswa (jika ada)
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    st.id AS setoran_id,
    st.jenis,
    st.waktu_setoran
FROM 
    siswa s
    FULL OUTER JOIN setoran st ON s.id = st.siswa_id
WHERE 
    s.id IS NULL OR st.id IS NULL
ORDER BY 
    s.nama, st.waktu_setoran
LIMIT 10;

-- FULL OUTER JOIN antara halaqoh dan siswa
-- Menampilkan semua halaqoh dan semua siswa, termasuk halaqoh tanpa siswa dan siswa tanpa halaqoh
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    s.id AS siswa_id,
    s.nama AS nama_siswa
FROM 
    halaqoh h
    FULL OUTER JOIN siswa s ON h.id = s.halaqoh_id
WHERE 
    h.id IS NULL OR s.id IS NULL
ORDER BY 
    h.nama, s.nama;

-- FULL OUTER JOIN antara pengajar dan halaqoh
-- Menampilkan semua pengajar dan semua halaqoh, termasuk pengajar tanpa halaqoh dan halaqoh tanpa pengajar
SELECT 
    p.id AS pengajar_id,
    p.nama AS nama_pengajar,
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh
FROM 
    pengajar p
    FULL OUTER JOIN halaqoh h ON p.id = h.pengajar_id
WHERE 
    p.id IS NULL OR h.id IS NULL
ORDER BY 
    p.nama, h.nama;

-- FULL OUTER JOIN untuk menemukan ketidakcocokan antara siswa dan setoran
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    st.id AS setoran_id,
    st.jenis
FROM 
    siswa s
    FULL OUTER JOIN setoran st ON s.id = st.siswa_id
WHERE 
    s.id IS NULL OR st.id IS NULL
ORDER BY 
    s.nama, st.id
LIMIT 10;

-- FULL OUTER JOIN dengan beberapa tabel
-- Menampilkan semua pengajar, halaqoh, dan siswa, termasuk yang tidak memiliki relasi
SELECT 
    p.id AS pengajar_id,
    p.nama AS nama_pengajar,
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    s.id AS siswa_id,
    s.nama AS nama_siswa
FROM 
    pengajar p
    FULL OUTER JOIN halaqoh h ON p.id = h.pengajar_id
    FULL OUTER JOIN siswa s ON h.id = s.halaqoh_id
WHERE 
    p.id IS NULL OR h.id IS NULL OR s.id IS NULL
ORDER BY 
    p.nama, h.nama, s.nama
LIMIT 20;
