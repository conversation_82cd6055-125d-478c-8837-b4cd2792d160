-- 16_select_cross_join.sql
-- DQL: SELECT dengan CROSS JOIN
-- CROSS JOIN menghasilkan produk Cartesian dari dua tabel, yaitu semua kombinasi yang mungkin dari baris kedua tabel.
-- Tidak ada kondisi pencocokan (ON) yang diperlukan.
-- Hati-hati: CROSS JOIN dapat menghasilkan jumlah baris yang sangat besar!

-- CROSS JOIN antara jenis setoran dan hari dalam seminggu
-- <PERSON><PERSON><PERSON><PERSON>an semua kombinasi yang mungkin untuk jadwal setoran
WITH jenis_setoran AS (
    SELECT 'ziyadah' AS jenis UNION ALL
    SELECT 'rabth' UNION ALL
    SELECT 'ikhtibar'
),
hari <PERSON> (
    SELECT 'Senin' AS hari UNION ALL
    SELECT 'Selasa' UNION ALL
    SELECT 'Rabu' UNION ALL
    SELECT 'Kamis' UNION ALL
    SELECT 'Jumat' UNION ALL
    SELECT 'Sabtu' UNION ALL
    SELECT 'Minggu'
)
SELECT 
    j.jenis,
    h.hari,
    CASE 
        WHEN j.jenis = 'ziyadah' THEN '08:00'
        WHEN j.jenis = 'rabth' THEN '15:00'
        WHEN j.jenis = 'ikhtibar' THEN '19:00'
    END AS waktu
FROM 
    jenis_setoran j
    CROSS JOIN hari h
ORDER BY 
    h.hari, j.jenis;

-- CROSS JOIN antara pengajar dan jenis setoran
-- Menghasilkan semua kombinasi yang mungkin untuk jadwal pengajar
SELECT 
    p.id AS pengajar_id,
    p.nama AS nama_pengajar,
    j.jenis AS jenis_setoran
FROM 
    pengajar p
    CROSS JOIN (
        SELECT 'ziyadah' AS jenis UNION ALL
        SELECT 'rabth' UNION ALL
        SELECT 'ikhtibar'
    ) j
WHERE 
    p.id <= 5 -- Membatasi jumlah pengajar untuk contoh
ORDER BY 
    p.nama, j.jenis;

-- CROSS JOIN untuk membuat matriks halaqoh dan ruangan
WITH ruangan AS (
    SELECT 'Ruang A' AS ruang UNION ALL
    SELECT 'Ruang B' UNION ALL
    SELECT 'Ruang C' UNION ALL
    SELECT 'Ruang D' UNION ALL
    SELECT 'Ruang E'
)
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    r.ruang
FROM 
    halaqoh h
    CROSS JOIN ruangan r
WHERE 
    h.id <= 5 -- Membatasi jumlah halaqoh untuk contoh
ORDER BY 
    h.nama, r.ruang;

-- CROSS JOIN untuk membuat jadwal ujian
WITH tanggal_ujian AS (
    SELECT '2025-06-01'::date AS tanggal UNION ALL
    SELECT '2025-06-02'::date UNION ALL
    SELECT '2025-06-03'::date
),
sesi AS (
    SELECT 'Pagi' AS sesi, '08:00-10:00' AS waktu UNION ALL
    SELECT 'Siang', '13:00-15:00' UNION ALL
    SELECT 'Sore', '16:00-18:00'
)
SELECT 
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh,
    t.tanggal,
    s.sesi,
    s.waktu
FROM 
    halaqoh h
    CROSS JOIN tanggal_ujian t
    CROSS JOIN sesi s
WHERE 
    h.id <= 3 -- Membatasi jumlah halaqoh untuk contoh
ORDER BY 
    t.tanggal, s.sesi, h.nama;

-- CROSS JOIN dengan WHERE untuk simulasi INNER JOIN
-- (Tidak disarankan, lebih baik gunakan INNER JOIN langsung)
SELECT 
    s.id AS siswa_id,
    s.nama AS nama_siswa,
    h.id AS halaqoh_id,
    h.nama AS nama_halaqoh
FROM 
    siswa s
    CROSS JOIN halaqoh h
WHERE 
    s.halaqoh_id = h.id
    AND s.id <= 5 -- Membatasi jumlah siswa untuk contoh
ORDER BY 
    s.nama;
