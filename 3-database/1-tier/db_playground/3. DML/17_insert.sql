-- 17_insert.sql
-- DML: INSERT
-- <PERSON><PERSON><PERSON> INSERT digunakan untuk menambahkan data baru ke dalam tabel.
-- <PERSON><PERSON> contoh ini, kita akan membuat tabel sementara untuk latihan INSERT.

-- Membuat tabel sementara untuk latihan
CREATE TEMPORARY TABLE temp_siswa (
    id SERIAL PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    gender VARCHAR(20) NOT NULL,
    tanggal_lahir DATE,
    alamat TEXT,
    no_telepon VARCHAR(20),
    email VARCHAR(100),
    tanggal_daftar DATE DEFAULT CURRENT_DATE
);

-- INSERT dasar dengan menyebutkan semua kolom
INSERT INTO temp_siswa (id, nama, gender, tanggal_lahir, alamat, no_telepon, email, tanggal_daftar)
VALUES (1, '<PERSON>', 'laki-laki', '2010-05-15', 'Jl. Mawar No. 10', '081234567890', '<EMAIL>', '2023-07-01');

-- INSERT tanpa menyebutkan kolom yang memiliki nilai default
INSERT INTO temp_siswa (id, nama, gender, tanggal_lahir, alamat, no_telepon, email)
VALUES (2, 'Siti Aminah', 'perempuan', '2011-03-20', 'Jl. Melati No. 5', '081234567891', '<EMAIL>');

-- INSERT tanpa menyebutkan kolom yang boleh NULL
INSERT INTO temp_siswa (id, nama, gender, tanggal_lahir)
VALUES (3, 'Budi Santoso', 'laki-laki', '2010-08-10');

-- INSERT tanpa menyebutkan id (akan menggunakan SERIAL/auto-increment)
INSERT INTO temp_siswa (nama, gender, tanggal_lahir, alamat)
VALUES ('Dewi Safitri', 'perempuan', '2011-11-05', 'Jl. Anggrek No. 7');

-- INSERT beberapa baris sekaligus
INSERT INTO temp_siswa (nama, gender, tanggal_lahir, alamat)
VALUES 
    ('Eko Prasetyo', 'laki-laki', '2010-04-12', 'Jl. Dahlia No. 9'),
    ('Fitri Handayani', 'perempuan', '2011-07-22', 'Jl. Kenanga No. 3'),
    ('Gunawan Wibowo', 'laki-laki', '2010-09-30', 'Jl. Cempaka No. 11');

-- INSERT dengan subquery
INSERT INTO temp_siswa (nama, gender, tanggal_lahir)
SELECT 
    'Siswa ' || generate_series(1, 5),
    CASE WHEN generate_series(1, 5) % 2 = 0 THEN 'laki-laki' ELSE 'perempuan' END,
    CURRENT_DATE - (generate_series(1, 5) * 100 + 3650);

-- INSERT dengan RETURNING untuk mendapatkan nilai yang dimasukkan
INSERT INTO temp_siswa (nama, gender, tanggal_lahir)
VALUES ('Hadi Nugroho', 'laki-laki', '2010-12-15')
RETURNING id, nama, tanggal_daftar;

-- Melihat data yang telah dimasukkan
SELECT * FROM temp_siswa ORDER BY id;

-- Membersihkan tabel sementara (opsional)
DROP TABLE IF EXISTS temp_siswa;
