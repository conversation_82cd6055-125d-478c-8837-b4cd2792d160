-- 18_update.sql
-- DML: UPDATE
-- Perintah UPDATE digunakan untuk mengubah data yang sudah ada dalam tabel.
-- <PERSON><PERSON> contoh ini, kita akan membuat tabel sementara untuk latihan UPDATE.

-- Membuat tabel sementara untuk latihan
CREATE TEMPORARY TABLE temp_siswa (
    id SERIAL PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    gender VARCHAR(20) NOT NULL,
    tanggal_lahir DATE,
    alamat TEXT,
    no_telepon VARCHAR(20),
    email VARCHAR(100),
    nilai_ujian INTEGER,
    status VARCHAR(20) DEFAULT 'Aktif',
    tanggal_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Memasukkan data awal
INSERT INTO temp_siswa (nama, gender, tanggal_lahir, alamat, no_telepon, email, nilai_ujian)
VALUES 
    ('<PERSON>', 'laki-laki', '2010-05-15', 'Jl. Mawar No. 10', '081234567890', '<EMAIL>', 75),
    ('Siti <PERSON>', 'perempuan', '2011-03-20', 'Jl. Melati No. 5', '081234567891', '<EMAIL>', 85),
    ('Budi <PERSON>o', 'laki-laki', '2010-08-10', 'Jl. Kenanga No. 8', '081234567892', '<EMAIL>', 65),
    ('Dewi Safitri', 'perempuan', '2011-11-05', 'Jl. Anggrek No. 7', '081234567893', '<EMAIL>', 90),
    ('Eko Prasetyo', 'laki-laki', '2010-04-12', 'Jl. Dahlia No. 9', '081234567894', '<EMAIL>', 60);

-- Melihat data awal
SELECT * FROM temp_siswa ORDER BY id;

-- UPDATE satu kolom untuk satu baris
UPDATE temp_siswa
SET nilai_ujian = 80
WHERE id = 1;

-- UPDATE beberapa kolom untuk satu baris
UPDATE temp_siswa
SET 
    alamat = 'Jl. Melati No. 15',
    no_telepon = '081234567899',
    tanggal_update = CURRENT_TIMESTAMP
WHERE id = 2;

-- UPDATE satu kolom untuk beberapa baris
UPDATE temp_siswa
SET status = 'Lulus'
WHERE nilai_ujian >= 70;

-- UPDATE satu kolom untuk beberapa baris dengan kondisi berbeda
UPDATE temp_siswa
SET status = 'Tidak Lulus'
WHERE nilai_ujian < 70;

-- UPDATE dengan perhitungan
UPDATE temp_siswa
SET nilai_ujian = nilai_ujian + 5
WHERE status = 'Tidak Lulus';

-- UPDATE dengan subquery
UPDATE temp_siswa
SET status = 'Berprestasi'
WHERE id IN (
    SELECT id FROM temp_siswa
    WHERE nilai_ujian >= 85
);

-- UPDATE dengan CASE
UPDATE temp_siswa
SET 
    status = CASE 
        WHEN nilai_ujian >= 85 THEN 'Sangat Baik'
        WHEN nilai_ujian >= 70 THEN 'Baik'
        ELSE 'Perlu Perbaikan'
    END;

-- UPDATE semua baris (hati-hati!)
UPDATE temp_siswa
SET tanggal_update = CURRENT_TIMESTAMP;

-- UPDATE dengan RETURNING untuk melihat baris yang diubah
UPDATE temp_siswa
SET nilai_ujian = 95
WHERE id = 4
RETURNING id, nama, nilai_ujian, status;

-- Melihat data setelah UPDATE
SELECT * FROM temp_siswa ORDER BY id;

-- Membersihkan tabel sementara (opsional)
DROP TABLE IF EXISTS temp_siswa;
