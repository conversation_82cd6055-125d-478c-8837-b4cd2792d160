-- 19_delete.sql
-- DML: DELETE
-- Perintah DELETE digunakan untuk menghapus data dari tabel.
-- <PERSON><PERSON> contoh ini, kita akan membuat tabel sementara untuk latihan DELETE.

-- Membuat tabel sementara untuk latihan
CREATE TEMPORARY TABLE temp_siswa (
    id SERIAL PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    gender VARCHAR(20) NOT NULL,
    tanggal_lahir DATE,
    nilai_ujian INTEGER,
    status VARCHAR(20) DEFAULT 'Aktif'
);

-- Memasukkan data awal
INSERT INTO temp_siswa (nama, gender, tanggal_lahir, nilai_ujian, status)
VALUES 
    ('<PERSON>', 'laki-laki', '2010-05-15', 75, 'Aktif'),
    ('Siti <PERSON>h', 'perempuan', '2011-03-20', 85, 'Aktif'),
    ('<PERSON><PERSON>', 'laki-laki', '2010-08-10', 65, 'Aktif'),
    ('<PERSON><PERSON>', 'perempuan', '2011-11-05', 90, 'Aktif'),
    ('<PERSON><PERSON>', 'laki-laki', '2010-04-12', 60, 'Aktif'),
    ('Fitri Handayani', 'perempuan', '2011-07-22', 80, 'Aktif'),
    ('Gunawan Wibowo', 'laki-laki', '2010-09-30', 70, 'Aktif'),
    ('Hadi Nugroho', 'laki-laki', '2010-12-15', 55, 'Aktif'),
    ('Ina Mardiana', 'perempuan', '2011-01-25', 95, 'Aktif'),
    ('Joko Susilo', 'laki-laki', '2010-06-05', 50, 'Aktif');

-- Melihat data awal
SELECT * FROM temp_siswa ORDER BY id;

-- DELETE satu baris berdasarkan id
DELETE FROM temp_siswa
WHERE id = 1;

-- DELETE beberapa baris berdasarkan kondisi
DELETE FROM temp_siswa
WHERE nilai_ujian < 60;

-- DELETE dengan subquery
DELETE FROM temp_siswa
WHERE id IN (
    SELECT id FROM temp_siswa
    WHERE gender = 'laki-laki' AND nilai_ujian < 70
);

-- DELETE dengan RETURNING untuk melihat baris yang dihapus
DELETE FROM temp_siswa
WHERE id = 5
RETURNING id, nama, nilai_ujian;

-- Melihat data setelah DELETE
SELECT * FROM temp_siswa ORDER BY id;

-- Membuat tabel sementara lain untuk demonstrasi TRUNCATE
CREATE TEMPORARY TABLE temp_log (
    id SERIAL PRIMARY KEY,
    aksi VARCHAR(100),
    waktu TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Memasukkan beberapa data
INSERT INTO temp_log (aksi)
VALUES 
    ('Login'),
    ('Logout'),
    ('Update Profil'),
    ('Ganti Password'),
    ('Kirim Pesan');

-- Melihat data awal
SELECT * FROM temp_log;

-- TRUNCATE untuk menghapus semua data (lebih cepat dari DELETE tanpa WHERE)
TRUNCATE TABLE temp_log;

-- Melihat data setelah TRUNCATE
SELECT * FROM temp_log;

-- Membersihkan tabel sementara (opsional)
DROP TABLE IF EXISTS temp_siswa;
DROP TABLE IF EXISTS temp_log;
