-- 20_grant.sql
-- DCL: GRANT
-- <PERSON><PERSON><PERSON> GRANT digunakan untuk memberikan hak akses (privileges) kepada pengguna database.
-- CATATAN: Perintah ini memerlukan hak akses administrator dan mungkin tidak berfungsi
-- di semua lingkungan database, terutama di layanan cloud seperti Supabase.

-- Membuat role/user baru (memerlukan hak akses superuser)
-- CREATE ROLE tahfidz_admin;
-- CREATE ROLE tahfidz_guru;
-- CREATE ROLE tahfidz_siswa;

-- Member<PERSON>n hak akses login kepada role (memerlukan hak akses superuser)
-- ALTER ROLE tahfidz_admin WITH LOGIN PASSWORD 'password_admin';
-- ALTER ROLE tahfidz_guru WITH LOGIN PASSWORD 'password_guru';
-- ALTER ROLE tahfidz_siswa WITH LOGIN PASSWORD 'password_siswa';

-- <PERSON><PERSON>n semua hak akses pada semua tabel dalam skema public kepada tahfidz_admin
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tahfidz_admin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tahfidz_admin;

-- Memberikan hak akses tertentu pada tabel siswa kepada tahfidz_guru
GRANT SELECT, INSERT, UPDATE ON TABLE siswa TO tahfidz_guru;
GRANT SELECT, INSERT, UPDATE ON TABLE halaqoh TO tahfidz_guru;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE setoran TO tahfidz_guru;
GRANT USAGE ON SEQUENCE siswa_id_seq TO tahfidz_guru;
GRANT USAGE ON SEQUENCE halaqoh_id_seq TO tahfidz_guru;
GRANT USAGE ON SEQUENCE setoran_id_seq TO tahfidz_guru;

-- Memberikan hak akses hanya baca pada tabel tertentu kepada tahfidz_siswa
GRANT SELECT ON TABLE siswa TO tahfidz_siswa;
GRANT SELECT ON TABLE halaqoh TO tahfidz_siswa;
GRANT SELECT ON TABLE setoran TO tahfidz_siswa;
GRANT SELECT ON TABLE quranidn TO tahfidz_siswa;

-- Memberikan hak akses pada kolom tertentu saja
GRANT SELECT (id, nama, gender, halaqoh_id) ON TABLE siswa TO tahfidz_siswa;

-- Memberikan hak akses untuk menjalankan fungsi tertentu
GRANT EXECUTE ON FUNCTION nama_fungsi TO tahfidz_guru;

-- Memberikan hak akses dengan opsi GRANT OPTION
-- Memungkinkan penerima hak akses untuk memberikan hak akses yang sama kepada pengguna lain
GRANT SELECT ON TABLE halaqoh TO tahfidz_guru WITH GRANT OPTION;

-- Memberikan hak akses pada skema
GRANT USAGE ON SCHEMA public TO tahfidz_guru, tahfidz_siswa;

-- Memberikan hak akses pada database
GRANT CONNECT ON DATABASE postgres TO tahfidz_admin, tahfidz_guru, tahfidz_siswa;

-- Memberikan hak akses role kepada pengguna lain
GRANT tahfidz_guru TO nama_pengguna;

-- Melihat hak akses yang telah diberikan pada tabel
-- SELECT grantee, privilege_type 
-- FROM information_schema.role_table_grants 
-- WHERE table_name = 'siswa';
