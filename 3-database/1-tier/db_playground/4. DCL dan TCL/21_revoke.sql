-- 21_revoke.sql
-- DCL: REVOKE
-- <PERSON><PERSON><PERSON> REVOKE digunakan untuk mencabut hak akses (privileges) dari pengguna database.
-- CATATAN: Perintah ini memerlukan hak akses administrator dan mungkin tidak berfungsi
-- di semua lingkungan database, terutama di layanan cloud seperti Supabase.

-- Mencabut semua hak akses pada semua tabel dalam skema public dari tahfidz_siswa
REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA public FROM tahfidz_siswa;

-- Mencabut hak akses tertentu pada tabel siswa dari tahfidz_guru
REVOKE INSERT, UPDATE, DELETE ON TABLE siswa FROM tahfidz_guru;

-- Mencabut hak akses pada kolom tertentu
REVOKE SELECT (email, no_telepon, alamat) ON TABLE siswa FROM tahfidz_siswa;

-- Mencabut hak akses untuk menjalankan fungsi tertentu
REVOKE EXECUTE ON FUNCTION nama_fungsi FROM tahfidz_guru;

-- Mencabut hak akses dengan CASCADE
-- Akan mencabut juga hak akses yang telah diberikan oleh pengguna tersebut kepada pengguna lain
REVOKE SELECT ON TABLE halaqoh FROM tahfidz_guru CASCADE;

-- Mencabut hak akses pada skema
REVOKE USAGE ON SCHEMA public FROM tahfidz_siswa;

-- Mencabut hak akses pada database
REVOKE CONNECT ON DATABASE postgres FROM tahfidz_siswa;

-- Mencabut hak akses role dari pengguna lain
REVOKE tahfidz_guru FROM nama_pengguna;

-- Mencabut hak akses pada sequence
REVOKE USAGE ON SEQUENCE siswa_id_seq FROM tahfidz_guru;

-- Mencabut semua hak akses dari pengguna publik (public)
REVOKE ALL ON TABLE siswa FROM PUBLIC;
REVOKE ALL ON TABLE halaqoh FROM PUBLIC;
REVOKE ALL ON TABLE setoran FROM PUBLIC;

-- Melihat hak akses yang masih ada pada tabel
-- SELECT grantee, privilege_type 
-- FROM information_schema.role_table_grants 
-- WHERE table_name = 'siswa';
