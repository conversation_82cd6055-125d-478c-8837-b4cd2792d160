-- 22_start_transaction.sql
-- TCL: START TRANSACTION atau BEGIN
-- Perintah ini digunakan untuk memulai blok transaksi.
-- Transaksi memungkinkan serangkaian operasi database dijalankan sebagai satu kesatuan.

-- Membuat tabel sementara untuk latihan transaksi
CREATE TEMPORARY TABLE temp_siswa (
    id SERIAL PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    nilai INTEGER
);

CREATE TEMPORARY TABLE temp_nilai_log (
    id SERIAL PRIMARY KEY,
    siswa_id INTEGER,
    nilai_lama INTEGER,
    nilai_baru INTEGER,
    waktu_perubahan TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Memasukkan data awal
INSERT INTO temp_siswa (nama, nilai)
VALUES 
    ('Ahmad', 75),
    ('Budi', 80),
    ('<PERSON>', 85),
    ('<PERSON><PERSON>', 70),
    ('<PERSON><PERSON>', 90);

-- Melihat data awal
SELECT * FROM temp_siswa;
SELECT * FROM temp_nilai_log;

-- Me<PERSON>lai transaksi
BEGIN;
-- atau bisa juga: START TRANSACTION;

-- Operasi 1: Update nilai siswa
UPDATE temp_siswa
SET nilai = 95
WHERE nama = 'Ahmad';

-- Operasi 2: Catat perubahan nilai ke log
INSERT INTO temp_nilai_log (siswa_id, nilai_lama, nilai_baru)
VALUES (
    (SELECT id FROM temp_siswa WHERE nama = 'Ahmad'),
    75,
    95
);

-- Melihat data dalam transaksi (perubahan sudah terlihat dalam sesi ini)
SELECT * FROM temp_siswa;
SELECT * FROM temp_nilai_log;

-- Pada titik ini, perubahan belum permanen dan hanya terlihat dalam sesi ini.
-- Jika koneksi terputus atau terjadi error, perubahan akan otomatis di-rollback.

-- Untuk menyimpan perubahan, gunakan COMMIT (lihat file 23_commit.sql)
-- Untuk membatalkan perubahan, gunakan ROLLBACK (lihat file 24_rollback.sql)

-- Dalam contoh ini, kita akan melakukan COMMIT untuk menyimpan perubahan
COMMIT;

-- Melihat data setelah COMMIT (perubahan sudah permanen)
SELECT * FROM temp_siswa;
SELECT * FROM temp_nilai_log;

-- Contoh transaksi dengan savepoint
BEGIN;

-- Operasi 1: Update nilai siswa Budi
UPDATE temp_siswa
SET nilai = 85
WHERE nama = 'Budi';

-- Membuat savepoint setelah operasi 1
SAVEPOINT update_budi;

-- Operasi 2: Update nilai siswa Cindy
UPDATE temp_siswa
SET nilai = 90
WHERE nama = 'Cindy';

-- Membuat savepoint setelah operasi 2
SAVEPOINT update_cindy;

-- Operasi 3: Update nilai siswa Dodi
UPDATE temp_siswa
SET nilai = 75
WHERE nama = 'Dodi';

-- Kita bisa rollback ke savepoint tertentu jika diperlukan
ROLLBACK TO SAVEPOINT update_cindy;

-- Sekarang perubahan pada Dodi dibatalkan, tapi perubahan pada Budi dan Cindy masih ada
-- Untuk menyimpan perubahan yang tersisa
COMMIT;

-- Melihat data setelah COMMIT dengan savepoint
SELECT * FROM temp_siswa;

-- Membersihkan tabel sementara (opsional)
DROP TABLE IF EXISTS temp_siswa;
DROP TABLE IF EXISTS temp_nilai_log;
