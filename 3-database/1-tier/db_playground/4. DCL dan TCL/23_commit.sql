-- 23_commit.sql
-- TCL: COMMIT
-- <PERSON><PERSON><PERSON> COMMIT digunakan untuk menyimpan perubahan yang dilakukan dalam transaksi.
-- <PERSON><PERSON><PERSON> COMMIT, perubahan menjadi permanen dan terlihat oleh semua sesi.

-- Membuat tabel sementara untuk latihan transaksi
CREATE TEMPORARY TABLE temp_rekening (
    id SERIAL PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    saldo DECIMAL(10, 2) NOT NULL
);

CREATE TEMPORARY TABLE temp_transaksi_keuangan (
    id SERIAL PRIMARY KEY,
    rekening_id INTEGER,
    jenis VARCHAR(20) NOT NULL, -- 'debit' atau 'kredit'
    jumlah DECIMAL(10, 2) NOT NULL,
    keterangan TEXT,
    waktu TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Memasukkan data awal
INSERT INTO temp_rekening (nama, saldo)
VALUES 
    ('Ahmad', 1000000.00),
    ('Budi', 500000.00);

-- Melihat data awal
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Contoh 1: Transaksi sederhana dengan COMMIT
BEGIN;

-- Mengurangi saldo Ahmad
UPDATE temp_rekening
SET saldo = saldo - 200000.00
WHERE nama = 'Ahmad';

-- Menambah saldo Budi
UPDATE temp_rekening
SET saldo = saldo + 200000.00
WHERE nama = 'Budi';

-- Mencatat transaksi untuk Ahmad (debit)
INSERT INTO temp_transaksi_keuangan (rekening_id, jenis, jumlah, keterangan)
VALUES (
    (SELECT id FROM temp_rekening WHERE nama = 'Ahmad'),
    'debit',
    200000.00,
    'Transfer ke Budi'
);

-- Mencatat transaksi untuk Budi (kredit)
INSERT INTO temp_transaksi_keuangan (rekening_id, jenis, jumlah, keterangan)
VALUES (
    (SELECT id FROM temp_rekening WHERE nama = 'Budi'),
    'kredit',
    200000.00,
    'Transfer dari Ahmad'
);

-- Melihat data dalam transaksi (perubahan sudah terlihat dalam sesi ini)
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Menyimpan perubahan dengan COMMIT
COMMIT;

-- Melihat data setelah COMMIT (perubahan sudah permanen)
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Contoh 2: Transaksi dengan validasi sebelum COMMIT
BEGIN;

-- Variabel untuk menyimpan saldo Ahmad
DO $$
DECLARE
    saldo_ahmad DECIMAL(10, 2);
BEGIN
    -- Mengambil saldo Ahmad
    SELECT saldo INTO saldo_ahmad
    FROM temp_rekening
    WHERE nama = 'Ahmad';
    
    -- Validasi saldo cukup
    IF saldo_ahmad >= 300000.00 THEN
        -- Mengurangi saldo Ahmad
        UPDATE temp_rekening
        SET saldo = saldo - 300000.00
        WHERE nama = 'Ahmad';
        
        -- Menambah saldo Budi
        UPDATE temp_rekening
        SET saldo = saldo + 300000.00
        WHERE nama = 'Budi';
        
        -- Mencatat transaksi
        INSERT INTO temp_transaksi_keuangan (rekening_id, jenis, jumlah, keterangan)
        VALUES (
            (SELECT id FROM temp_rekening WHERE nama = 'Ahmad'),
            'debit',
            300000.00,
            'Transfer ke Budi'
        );
        
        INSERT INTO temp_transaksi_keuangan (rekening_id, jenis, jumlah, keterangan)
        VALUES (
            (SELECT id FROM temp_rekening WHERE nama = 'Budi'),
            'kredit',
            300000.00,
            'Transfer dari Ahmad'
        );
        
        -- Transaksi berhasil, akan di-COMMIT di luar blok DO
        RAISE NOTICE 'Transaksi berhasil';
    ELSE
        -- Saldo tidak cukup, akan di-ROLLBACK di luar blok DO
        RAISE NOTICE 'Saldo tidak cukup';
        RAISE EXCEPTION 'Saldo tidak cukup';
    END IF;
END $$;

-- Menyimpan perubahan dengan COMMIT
-- Jika ada EXCEPTION di blok DO, transaksi akan otomatis di-ROLLBACK
COMMIT;

-- Melihat data setelah COMMIT kedua
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Membersihkan tabel sementara (opsional)
DROP TABLE IF EXISTS temp_rekening;
DROP TABLE IF EXISTS temp_transaksi_keuangan;
