-- 24_rollback.sql
-- TCL: ROLLBACK
-- <PERSON><PERSON><PERSON> ROLLBACK digunakan untuk membatalkan perubahan yang dilakukan dalam transaksi.
-- <PERSON><PERSON><PERSON> ROLLBACK, semua perubahan dalam transaksi dibatalkan dan tidak disimpan.

-- Membuat tabel sementara untuk latihan transaksi
CREATE TEMPORARY TABLE temp_rekening (
    id SERIAL PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    saldo DECIMAL(10, 2) NOT NULL
);

CREATE TEMPORARY TABLE temp_transaksi_keuangan (
    id SERIAL PRIMARY KEY,
    rekening_id INTEGER,
    jenis VARCHAR(20) NOT NULL, -- 'debit' atau 'kredit'
    jumlah DECIMAL(10, 2) NOT NULL,
    keterangan TEXT,
    waktu TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Memasukkan data awal
INSERT INTO temp_rekening (nama, saldo)
VALUES 
    ('Ahmad', 1000000.00),
    ('Budi', 500000.00);

-- Melihat data awal
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Contoh 1: Transaksi dengan ROLLBACK penuh
BEGIN;

-- Mengurangi saldo Ahmad
UPDATE temp_rekening
SET saldo = saldo - 1500000.00 -- Jumlah melebihi saldo
WHERE nama = 'Ahmad';

-- Menambah saldo Budi
UPDATE temp_rekening
SET saldo = saldo + 1500000.00
WHERE nama = 'Budi';

-- Melihat data dalam transaksi (perubahan sudah terlihat dalam sesi ini)
SELECT * FROM temp_rekening;

-- Membatalkan semua perubahan dengan ROLLBACK
-- Misalnya karena kita menyadari jumlah transfer melebihi saldo Ahmad
ROLLBACK;

-- Melihat data setelah ROLLBACK (perubahan dibatalkan, kembali ke data awal)
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Contoh 2: Transaksi dengan SAVEPOINT dan ROLLBACK parsial
BEGIN;

-- Operasi 1: Mengurangi saldo Ahmad untuk pembayaran pertama
UPDATE temp_rekening
SET saldo = saldo - 200000.00
WHERE nama = 'Ahmad';

-- Mencatat transaksi untuk pembayaran pertama
INSERT INTO temp_transaksi_keuangan (rekening_id, jenis, jumlah, keterangan)
VALUES (
    (SELECT id FROM temp_rekening WHERE nama = 'Ahmad'),
    'debit',
    200000.00,
    'Pembayaran pertama'
);

-- Membuat SAVEPOINT setelah pembayaran pertama
SAVEPOINT pembayaran_pertama;

-- Operasi 2: Mengurangi saldo Ahmad untuk pembayaran kedua
UPDATE temp_rekening
SET saldo = saldo - 300000.00
WHERE nama = 'Ahmad';

-- Mencatat transaksi untuk pembayaran kedua
INSERT INTO temp_transaksi_keuangan (rekening_id, jenis, jumlah, keterangan)
VALUES (
    (SELECT id FROM temp_rekening WHERE nama = 'Ahmad'),
    'debit',
    300000.00,
    'Pembayaran kedua'
);

-- Membuat SAVEPOINT setelah pembayaran kedua
SAVEPOINT pembayaran_kedua;

-- Operasi 3: Mengurangi saldo Ahmad untuk pembayaran ketiga
UPDATE temp_rekening
SET saldo = saldo - 700000.00 -- Ini akan membuat saldo negatif
WHERE nama = 'Ahmad';

-- Mencatat transaksi untuk pembayaran ketiga
INSERT INTO temp_transaksi_keuangan (rekening_id, jenis, jumlah, keterangan)
VALUES (
    (SELECT id FROM temp_rekening WHERE nama = 'Ahmad'),
    'debit',
    700000.00,
    'Pembayaran ketiga'
);

-- Melihat data dalam transaksi (semua perubahan terlihat)
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Kita menyadari bahwa pembayaran ketiga terlalu besar dan akan membuat saldo negatif
-- Rollback ke SAVEPOINT pembayaran_kedua untuk membatalkan hanya pembayaran ketiga
ROLLBACK TO SAVEPOINT pembayaran_kedua;

-- Melihat data setelah ROLLBACK TO SAVEPOINT (pembayaran ketiga dibatalkan)
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Menyimpan perubahan yang tersisa (pembayaran pertama dan kedua)
COMMIT;

-- Melihat data setelah COMMIT (pembayaran pertama dan kedua disimpan)
SELECT * FROM temp_rekening;
SELECT * FROM temp_transaksi_keuangan;

-- Membersihkan tabel sementara (opsional)
DROP TABLE IF EXISTS temp_rekening;
DROP TABLE IF EXISTS temp_transaksi_keuangan;
