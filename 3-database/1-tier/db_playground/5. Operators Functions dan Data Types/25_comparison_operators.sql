-- 25_comparison_operators.sql
-- Contoh penggunaan operator perbandingan (=, <, >, <=, >=, <>)
-- Operator perbandingan digunakan untuk membandingkan nilai dan menghasilkan nilai boolean (TRUE/FALSE).

-- Operator sama dengan (=)
-- Mencari siswa dengan gender 'laki-laki'
SELECT id, nama, gender
FROM siswa
WHERE gender = 'laki-laki'
LIMIT 5;

-- Operator tidak sama dengan (<> atau !=)
-- Mencari siswa dengan gender bukan 'laki-laki'
SELECT id, nama, gender
FROM siswa
WHERE gender <> 'laki-laki'
LIMIT 5;

-- Operator lebih besar dari (>)
-- <PERSON><PERSON>i setoran dengan halaman_akhir lebih dari 100
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE halaman_akhir > 100
LIMIT 5;

-- Operator lebih kecil dari (<)
-- <PERSON><PERSON><PERSON> setoran dengan halaman_awal kurang dari 50
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE halaman_awal < 50
LIMIT 5;

-- Operator lebih besar atau sama dengan (>=)
-- Mencari setoran dengan halaman_akhir lebih dari atau sama dengan 100
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE halaman_akhir >= 100
LIMIT 5;

-- Operator lebih kecil atau sama dengan (<=)
-- Mencari setoran dengan halaman_awal kurang dari atau sama dengan 50
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE halaman_awal <= 50
LIMIT 5;

-- Kombinasi operator perbandingan
-- Mencari setoran dengan halaman_awal antara 50 dan 100
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE halaman_awal >= 50 AND halaman_awal <= 100
LIMIT 5;

-- Alternatif menggunakan BETWEEN
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE halaman_awal BETWEEN 50 AND 100
LIMIT 5;

-- Operator IS NULL
-- Mencari siswa dengan email NULL
SELECT id, nama, email
FROM siswa
WHERE email IS NULL
LIMIT 5;

-- Operator IS NOT NULL
-- Mencari siswa dengan email tidak NULL
SELECT id, nama, email
FROM siswa
WHERE email IS NOT NULL
LIMIT 5;

-- Operator LIKE untuk pencocokan pola
-- Mencari siswa dengan nama mengandung 'Ahmad'
SELECT id, nama
FROM siswa
WHERE nama LIKE '%Ahmad%'
LIMIT 5;

-- Operator IN untuk pencocokan dengan daftar nilai
-- Mencari siswa dengan halaqoh_id 1, 3, atau 5
SELECT id, nama, halaqoh_id
FROM siswa
WHERE halaqoh_id IN (1, 3, 5)
LIMIT 5;

-- Operator NOT IN untuk pencocokan dengan daftar nilai yang tidak cocok
-- Mencari siswa dengan halaqoh_id bukan 1, 3, atau 5
SELECT id, nama, halaqoh_id
FROM siswa
WHERE halaqoh_id NOT IN (1, 3, 5)
LIMIT 5;

-- Operator EXISTS untuk memeriksa keberadaan subquery
-- Mencari siswa yang memiliki setoran
SELECT id, nama
FROM siswa s
WHERE EXISTS (
    SELECT 1 FROM setoran st WHERE st.siswa_id = s.id
)
LIMIT 5;

-- Operator NOT EXISTS untuk memeriksa ketidakberadaan subquery
-- Mencari siswa yang tidak memiliki setoran
SELECT id, nama
FROM siswa s
WHERE NOT EXISTS (
    SELECT 1 FROM setoran st WHERE st.siswa_id = s.id
)
LIMIT 5;
