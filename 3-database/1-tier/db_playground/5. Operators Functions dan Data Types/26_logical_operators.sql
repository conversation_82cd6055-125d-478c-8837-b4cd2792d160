-- 26_logical_operators.sql
-- Contoh penggunaan operator logika (AND, OR, NOT)
-- Operator logika digunakan untuk menggabungkan atau memodifikasi kondisi boolean.

-- Operator AND
-- Mencari siswa dengan gender 'laki-laki' DAN halaqoh_id = 1
SELECT id, nama, gender, halaqoh_id
FROM siswa
WHERE gender = 'laki-laki' AND halaqoh_id = 1
LIMIT 5;

-- Operator OR
-- <PERSON><PERSON>i siswa dengan gender 'laki-laki' ATAU halaqoh_id = 1
SELECT id, nama, gender, halaqoh_id
FROM siswa
WHERE gender = 'laki-laki' OR halaqoh_id = 1
LIMIT 5;

-- Operator NOT
-- Mencari siswa dengan gender BUKAN 'laki-laki'
SELECT id, nama, gender
FROM siswa
WHERE NOT gender = 'laki-laki'
LIMIT 5;

-- Kombinasi AND dan OR
-- <PERSON><PERSON><PERSON> siswa dengan gender 'laki-laki' DAN (halaqoh_id = 1 ATAU halaqoh_id = 3)
SELECT id, nama, gender, halaqoh_id
FROM siswa
WHERE gender = 'laki-laki' AND (halaqoh_id = 1 OR halaqoh_id = 3)
LIMIT 5;

-- Kombinasi AND, OR, dan NOT
-- Mencari siswa dengan gender 'perempuan' DAN BUKAN (halaqoh_id = 2 ATAU halaqoh_id = 4)
SELECT id, nama, gender, halaqoh_id
FROM siswa
WHERE gender = 'perempuan' AND NOT (halaqoh_id = 2 OR halaqoh_id = 4)
LIMIT 5;

-- Mencari setoran dengan jenis 'ziyadah' DAN halaman_awal >= 50 DAN halaman_akhir <= 100
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE jenis = 'ziyadah' AND halaman_awal >= 50 AND halaman_akhir <= 100
LIMIT 5;

-- Mencari setoran dengan jenis 'ziyadah' ATAU jenis 'rabth' DAN halaman_awal >= 50
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE (jenis = 'ziyadah' OR jenis = 'rabth') AND halaman_awal >= 50
LIMIT 5;

-- Mencari setoran dengan jenis BUKAN 'ikhtibar' DAN halaman_akhir > 100
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE NOT jenis = 'ikhtibar' AND halaman_akhir > 100
LIMIT 5;

-- Menggunakan operator logika dengan fungsi
-- Mencari setoran yang dilakukan pada tahun 2025 DAN bulan April
SELECT id, siswa_id, jenis, waktu_setoran
FROM setoran
WHERE EXTRACT(YEAR FROM waktu_setoran) = 2025 AND EXTRACT(MONTH FROM waktu_setoran) = 4
LIMIT 5;

-- Menggunakan operator logika dengan subquery
-- Mencari siswa yang memiliki setoran jenis 'ziyadah' DAN jenis 'rabth'
SELECT id, nama
FROM siswa s
WHERE EXISTS (
    SELECT 1 FROM setoran st 
    WHERE st.siswa_id = s.id AND st.jenis = 'ziyadah'
) AND EXISTS (
    SELECT 1 FROM setoran st 
    WHERE st.siswa_id = s.id AND st.jenis = 'rabth'
)
LIMIT 5;

-- Menggunakan operator logika dengan IS NULL
-- Mencari siswa dengan email IS NULL ATAU no_telepon IS NULL
SELECT id, nama, email, no_telepon
FROM siswa
WHERE email IS NULL OR no_telepon IS NULL
LIMIT 5;

-- Menggunakan operator logika dengan BETWEEN
-- Mencari setoran dengan halaman_awal ANTARA 50 DAN 100 ATAU halaman_akhir ANTARA 150 DAN 200
SELECT id, siswa_id, jenis, halaman_awal, halaman_akhir
FROM setoran
WHERE (halaman_awal BETWEEN 50 AND 100) OR (halaman_akhir BETWEEN 150 AND 200)
LIMIT 5;
