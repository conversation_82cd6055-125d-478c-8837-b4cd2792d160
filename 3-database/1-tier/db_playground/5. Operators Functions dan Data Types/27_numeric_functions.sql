-- 27_numeric_functions.sql
-- <PERSON><PERSON><PERSON> penggunaan fungsi numerik (SUM, AVG, COUNT)
-- Fungsi numerik digunakan untuk melakukan perhitungan pada data numerik.

-- Fungsi COUNT
-- Menghitung jumlah total siswa
SELECT COUNT(*) AS jumlah_siswa
FROM siswa;

-- Menghitung jumlah siswa per gender
SELECT gender, COUNT(*) AS jumlah_siswa
FROM siswa
GROUP BY gender;

-- Menghitung jumlah siswa per halaqoh
SELECT halaqoh_id, COUNT(*) AS jumlah_siswa
FROM siswa
GROUP BY halaqoh_id
ORDER BY halaqoh_id;

-- Menghitung jumlah setoran per jenis
SELECT jenis, COUNT(*) AS jumlah_setoran
FROM setoran
GROUP BY jenis;

-- Fungsi SUM
-- Menghitung total halaman yang disetorkan
SELECT SUM(halaman_akhir - halaman_awal + 1) AS total_halaman
FROM setoran;

-- Menghitung total halaman per jenis setoran
SELECT jenis, SUM(halaman_akhir - halaman_awal + 1) AS total_halaman
FROM setoran
GROUP BY jenis;

-- Menghitung total halaman per siswa
SELECT siswa_id, SUM(halaman_akhir - halaman_awal + 1) AS total_halaman
FROM setoran
GROUP BY siswa_id
ORDER BY total_halaman DESC
LIMIT 5;

-- Fungsi AVG
-- Menghitung rata-rata halaman per setoran
SELECT AVG(halaman_akhir - halaman_awal + 1) AS rata_rata_halaman
FROM setoran;

-- Menghitung rata-rata halaman per jenis setoran
SELECT jenis, AVG(halaman_akhir - halaman_awal + 1) AS rata_rata_halaman
FROM setoran
GROUP BY jenis;

-- Menghitung rata-rata halaman per siswa
SELECT siswa_id, AVG(halaman_akhir - halaman_awal + 1) AS rata_rata_halaman
FROM setoran
GROUP BY siswa_id
ORDER BY rata_rata_halaman DESC
LIMIT 5;

-- Fungsi MIN dan MAX
-- Mencari halaman minimum dan maksimum yang disetorkan
SELECT 
    MIN(halaman_awal) AS halaman_min,
    MAX(halaman_akhir) AS halaman_max
FROM setoran;

-- Mencari halaman minimum dan maksimum per jenis setoran
SELECT 
    jenis,
    MIN(halaman_awal) AS halaman_min,
    MAX(halaman_akhir) AS halaman_max
FROM setoran
GROUP BY jenis;

-- Fungsi ROUND
-- Membulatkan rata-rata halaman per setoran
SELECT ROUND(AVG(halaman_akhir - halaman_awal + 1), 2) AS rata_rata_halaman
FROM setoran;

-- Fungsi ABS (nilai absolut)
-- Menghitung selisih absolut antara halaman_awal dan 100
SELECT 
    id, 
    halaman_awal, 
    ABS(halaman_awal - 100) AS selisih_dari_100
FROM setoran
ORDER BY selisih_dari_100
LIMIT 5;

-- Fungsi CEIL dan FLOOR
-- Membulatkan ke atas dan ke bawah rata-rata halaman
SELECT 
    AVG(halaman_akhir - halaman_awal + 1) AS rata_rata_asli,
    CEIL(AVG(halaman_akhir - halaman_awal + 1)) AS pembulatan_atas,
    FLOOR(AVG(halaman_akhir - halaman_awal + 1)) AS pembulatan_bawah
FROM setoran;

-- Fungsi MOD (modulo/sisa pembagian)
-- Mencari siswa dengan ID genap dan ganjil
SELECT 
    id, 
    nama, 
    CASE 
        WHEN MOD(id, 2) = 0 THEN 'Genap'
        ELSE 'Ganjil'
    END AS jenis_id
FROM siswa
LIMIT 10;

-- Fungsi POWER (pangkat)
-- Menghitung kuadrat dari halaman_awal
SELECT 
    id, 
    halaman_awal, 
    POWER(halaman_awal, 2) AS halaman_kuadrat
FROM setoran
WHERE halaman_awal <= 10
LIMIT 5;

-- Fungsi SQRT (akar kuadrat)
-- Menghitung akar kuadrat dari halaman_awal
SELECT 
    id, 
    halaman_awal, 
    SQRT(halaman_awal) AS akar_halaman
FROM setoran
WHERE halaman_awal <= 100
LIMIT 5;
