-- 28_string_functions.sql
-- <PERSON><PERSON><PERSON> penggunaan fungsi string (CONCAT, SUBSTRING, LENGTH)
-- Fungsi string digunakan untuk memanipulasi data teks.

-- Fungsi CONCAT
-- Menggabungkan nama dan gender siswa
SELECT 
    id,
    CONCAT(nama, ' (', gender, ')') AS nama_lengkap
FROM siswa
LIMIT 5;

-- Alternatif CONCAT dengan operator ||
SELECT 
    id,
    nama || ' (' || gender || ')' AS nama_lengkap
FROM siswa
LIMIT 5;

-- Menggabungkan informasi setoran
SELECT 
    id,
    CONCAT('Setoran ', jenis, ' halaman ', halaman_awal, '-', halaman_akhir) AS info_setoran
FROM setoran
LIMIT 5;

-- Fungsi LENGTH
-- Menghitung panjang nama siswa
SELECT 
    id,
    nama,
    LENGTH(nama) AS panjang_nama
FROM siswa
ORDER BY panjang_nama DESC
LIMIT 5;

-- <PERSON><PERSON><PERSON> siswa dengan nama terpendek
SELECT 
    id,
    nama,
    LENGTH(nama) AS panjang_nama
FROM siswa
ORDER BY panjang_nama ASC
LIMIT 5;

-- Fungsi SUBSTRING
-- Mengambil 5 karakter pertama dari nama siswa
SELECT 
    id,
    nama,
    SUBSTRING(nama, 1, 5) AS nama_pendek
FROM siswa
LIMIT 5;

-- Mengambil karakter dari posisi tertentu
SELECT 
    id,
    nama,
    SUBSTRING(nama, 3) AS dari_karakter_ketiga
FROM siswa
LIMIT 5;

-- Mengambil karakter dari belakang (negatif)
SELECT 
    id,
    nama,
    SUBSTRING(nama, -3) AS tiga_karakter_terakhir
FROM siswa
LIMIT 5;

-- Fungsi UPPER dan LOWER
-- Mengubah nama siswa menjadi huruf besar dan huruf kecil
SELECT 
    id,
    nama,
    UPPER(nama) AS nama_kapital,
    LOWER(nama) AS nama_kecil
FROM siswa
LIMIT 5;

-- Fungsi INITCAP
-- Mengubah setiap awal kata menjadi huruf besar
SELECT 
    id,
    nama,
    INITCAP(nama) AS nama_proper
FROM siswa
LIMIT 5;

-- Fungsi TRIM
-- Menghapus spasi di awal dan akhir string
SELECT 
    TRIM('   Contoh String dengan Spasi   ') AS hasil_trim;

-- Fungsi LTRIM dan RTRIM
-- Menghapus spasi hanya di awal atau akhir string
SELECT 
    LTRIM('   Contoh String dengan Spasi   ') AS hasil_ltrim,
    RTRIM('   Contoh String dengan Spasi   ') AS hasil_rtrim;

-- Fungsi REPLACE
-- Mengganti karakter dalam string
SELECT 
    id,
    nama,
    REPLACE(nama, 'a', 'A') AS nama_ganti_a
FROM siswa
LIMIT 5;

-- Fungsi POSITION
-- Mencari posisi substring dalam string
SELECT 
    id,
    nama,
    POSITION('a' IN nama) AS posisi_a_pertama
FROM siswa
WHERE POSITION('a' IN nama) > 0
LIMIT 5;

-- Fungsi SPLIT_PART
-- Memisahkan string berdasarkan delimiter dan mengambil bagian tertentu
SELECT 
    id,
    nama,
    SPLIT_PART(nama, ' ', 1) AS nama_depan,
    SPLIT_PART(nama, ' ', 2) AS nama_tengah_atau_belakang
FROM siswa
LIMIT 5;

-- Fungsi LEFT dan RIGHT
-- Mengambil karakter dari kiri atau kanan string
SELECT 
    id,
    nama,
    LEFT(nama, 3) AS tiga_karakter_awal,
    RIGHT(nama, 3) AS tiga_karakter_akhir
FROM siswa
LIMIT 5;

-- Fungsi REPEAT
-- Mengulang string beberapa kali
SELECT 
    REPEAT('*', 5) AS lima_bintang,
    REPEAT('AB', 3) AS ab_tiga_kali;

-- Fungsi LPAD dan RPAD
-- Menambahkan karakter di awal atau akhir string hingga panjang tertentu
SELECT 
    id,
    LPAD(CAST(id AS TEXT), 5, '0') AS id_dengan_padding
FROM siswa
LIMIT 5;
