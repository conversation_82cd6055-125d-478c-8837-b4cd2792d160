-- 29_datetime_functions.sql
-- Con<PERSON>h penggunaan fungsi datetime (NOW, DATE, YEAR)
-- Fungsi datetime digunakan untuk memanipulasi data tanggal dan waktu.

-- Fungsi NOW() dan CURRENT_TIMESTAMP
-- Mendapatkan tanggal dan waktu saat ini
SELECT 
    NOW() AS waktu_sekarang,
    CURRENT_TIMESTAMP AS timestamp_sekarang;

-- Fungsi CURRENT_DATE dan CURRENT_TIME
-- Mendapatkan tanggal dan waktu saat ini secara terpisah
SELECT 
    CURRENT_DATE AS tanggal_sekarang,
    CURRENT_TIME AS waktu_sekarang;

-- Fungsi EXTRACT
-- Mengekstrak komponen dari tanggal/waktu
SELECT 
    waktu_setoran,
    EXTRACT(YEAR FROM waktu_setoran) AS tahun,
    EXTRACT(MONTH FROM waktu_setoran) AS bulan,
    EXTRACT(DAY FROM waktu_setoran) AS hari,
    EXTRACT(HOUR FROM waktu_setoran) AS jam,
    EXTRACT(MINUTE FROM waktu_setoran) AS menit,
    EXTRACT(SECOND FROM waktu_setoran) AS detik
FROM setoran
LIMIT 5;

-- Menghitung jumlah setoran per bulan
SELECT 
    EXTRACT(YEAR FROM waktu_setoran) AS tahun,
    EXTRACT(MONTH FROM waktu_setoran) AS bulan,
    COUNT(*) AS jumlah_setoran
FROM setoran
GROUP BY 
    EXTRACT(YEAR FROM waktu_setoran),
    EXTRACT(MONTH FROM waktu_setoran)
ORDER BY 
    tahun, bulan;

-- Fungsi DATE_PART (alternatif EXTRACT)
SELECT 
    waktu_setoran,
    DATE_PART('year', waktu_setoran) AS tahun,
    DATE_PART('month', waktu_setoran) AS bulan,
    DATE_PART('day', waktu_setoran) AS hari
FROM setoran
LIMIT 5;

-- Fungsi DATE_TRUNC
-- Memotong tanggal/waktu ke presisi tertentu
SELECT 
    waktu_setoran,
    DATE_TRUNC('year', waktu_setoran) AS tahun,
    DATE_TRUNC('month', waktu_setoran) AS bulan,
    DATE_TRUNC('day', waktu_setoran) AS hari,
    DATE_TRUNC('hour', waktu_setoran) AS jam
FROM setoran
LIMIT 5;

-- Menghitung jumlah setoran per hari
SELECT 
    DATE_TRUNC('day', waktu_setoran) AS tanggal,
    COUNT(*) AS jumlah_setoran
FROM setoran
GROUP BY DATE_TRUNC('day', waktu_setoran)
ORDER BY tanggal
LIMIT 10;

-- Fungsi TO_CHAR
-- Memformat tanggal/waktu menjadi string
SELECT 
    waktu_setoran,
    TO_CHAR(waktu_setoran, 'YYYY-MM-DD') AS format_iso,
    TO_CHAR(waktu_setoran, 'DD Mon YYYY') AS format_lengkap,
    TO_CHAR(waktu_setoran, 'HH24:MI:SS') AS format_waktu,
    TO_CHAR(waktu_setoran, 'DD Mon YYYY HH24:MI:SS') AS format_lengkap_dengan_waktu
FROM setoran
LIMIT 5;

-- Fungsi TO_DATE dan TO_TIMESTAMP
-- Mengkonversi string menjadi tanggal/waktu
SELECT 
    TO_DATE('2025-05-15', 'YYYY-MM-DD') AS tanggal,
    TO_TIMESTAMP('2025-05-15 14:30:00', 'YYYY-MM-DD HH24:MI:SS') AS timestamp;

-- Operasi aritmatika pada tanggal
-- Menambah atau mengurangi interval waktu
SELECT 
    CURRENT_DATE AS hari_ini,
    CURRENT_DATE + 7 AS tujuh_hari_kedepan,
    CURRENT_DATE - 7 AS tujuh_hari_kebelakang;

-- Menggunakan INTERVAL
SELECT 
    CURRENT_TIMESTAMP AS sekarang,
    CURRENT_TIMESTAMP + INTERVAL '1 day' AS besok,
    CURRENT_TIMESTAMP + INTERVAL '1 week' AS minggu_depan,
    CURRENT_TIMESTAMP + INTERVAL '1 month' AS bulan_depan,
    CURRENT_TIMESTAMP + INTERVAL '1 year' AS tahun_depan;

-- Menghitung selisih hari antara dua tanggal
SELECT 
    waktu_setoran,
    CURRENT_DATE AS hari_ini,
    CURRENT_DATE - waktu_setoran::date AS selisih_hari
FROM setoran
LIMIT 5;

-- Fungsi AGE
-- Menghitung selisih waktu lengkap antara dua tanggal
SELECT 
    waktu_setoran,
    CURRENT_TIMESTAMP AS sekarang,
    AGE(CURRENT_TIMESTAMP, waktu_setoran) AS selisih_waktu
FROM setoran
LIMIT 5;

-- Mencari setoran dalam rentang waktu tertentu
SELECT 
    id,
    siswa_id,
    jenis,
    waktu_setoran
FROM setoran
WHERE waktu_setoran BETWEEN 
    TO_TIMESTAMP('2025-04-01', 'YYYY-MM-DD') AND 
    TO_TIMESTAMP('2025-04-30', 'YYYY-MM-DD')
LIMIT 5;
