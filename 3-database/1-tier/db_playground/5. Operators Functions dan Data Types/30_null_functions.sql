-- 30_null_functions.sql
-- Con<PERSON>h penggunaan fungsi NULL (IFNULL, COALESCE)
-- Fungsi NULL digunakan untuk menangani nilai NULL dalam query.

-- Fungsi COALESCE
-- Mengembalik<PERSON> nilai non-NULL pertama dari daftar argumen
-- Berguna untuk memberikan nilai default jika suatu kolom NULL

-- Menggunakan COALESCE untuk memberikan nilai default pada email yang NULL
SELECT 
    id,
    nama,
    COALESCE(email, 'Email tidak tersedia') AS email_atau_default
FROM siswa
LIMIT 10;

-- Menggunakan COALESCE untuk memberikan nilai default pada beberapa kolom
SELECT 
    id,
    nama,
    COALESCE(no_telepon, 'Telepon tidak tersedia') AS telepon_atau_default,
    COALESCE(alamat, 'Alamat tidak tersedia') AS alamat_atau_default
FROM siswa
LIMIT 10;

-- Menggunakan COALESCE dengan beberapa alternatif
SELECT 
    id,
    nama,
    COALESCE(email, no_telepon, 'Kontak tidak tersedia') AS kontak
FROM siswa
LIMIT 10;

-- Fungsi NULLIF
-- Mengembalikan NULL jika dua ekspresi sama, atau nilai ekspresi pertama jika berbeda
-- Berguna untuk menghindari pembagian dengan nol

-- Contoh NULLIF untuk menghindari pembagian dengan nol
SELECT 
    10 / 2 AS pembagian_normal,
    10 / NULLIF(0, 0) AS pembagian_dengan_nol;

-- Menggunakan NULLIF untuk menandai nilai kosong sebagai NULL
SELECT 
    id,
    nama,
    NULLIF(email, '') AS email_bukan_string_kosong
FROM siswa
LIMIT 10;

-- Fungsi IS NULL dan IS NOT NULL
-- Memeriksa apakah nilai NULL atau tidak

-- Mencari siswa dengan email NULL
SELECT 
    id,
    nama,
    email
FROM siswa
WHERE email IS NULL
LIMIT 5;

-- Mencari siswa dengan email tidak NULL
SELECT 
    id,
    nama,
    email
FROM siswa
WHERE email IS NOT NULL
LIMIT 5;

-- Kombinasi IS NULL dengan COALESCE
SELECT 
    id,
    nama,
    email,
    CASE 
        WHEN email IS NULL THEN 'Email tidak tersedia'
        ELSE email
    END AS email_atau_pesan
FROM siswa
LIMIT 10;

-- Menghitung jumlah nilai NULL dan tidak NULL
SELECT 
    COUNT(*) AS total_siswa,
    COUNT(email) AS siswa_dengan_email,
    COUNT(*) - COUNT(email) AS siswa_tanpa_email
FROM siswa;

-- Menghitung persentase nilai NULL
SELECT 
    COUNT(*) AS total_siswa,
    COUNT(email) AS siswa_dengan_email,
    COUNT(*) - COUNT(email) AS siswa_tanpa_email,
    ROUND((COUNT(*) - COUNT(email))::NUMERIC / COUNT(*) * 100, 2) AS persentase_tanpa_email
FROM siswa;

-- Menggunakan COALESCE dalam GROUP BY
SELECT 
    COALESCE(gender, 'Tidak Diketahui') AS gender,
    COUNT(*) AS jumlah_siswa
FROM siswa
GROUP BY COALESCE(gender, 'Tidak Diketahui');

-- Menggunakan COALESCE dalam ORDER BY
SELECT 
    id,
    nama,
    email
FROM siswa
ORDER BY COALESCE(email, 'zzz') -- Email NULL akan muncul di akhir
LIMIT 10;

-- Menggunakan COALESCE dalam JOIN
SELECT 
    s.id,
    s.nama,
    COALESCE(h.nama, 'Tidak ada halaqoh') AS nama_halaqoh
FROM siswa s
LEFT JOIN halaqoh h ON s.halaqoh_id = h.id
LIMIT 10;
