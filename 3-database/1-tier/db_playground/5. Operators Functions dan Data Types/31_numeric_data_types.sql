-- 31_numeric_data_types.sql
-- <PERSON><PERSON>h penggunaan tipe data numerik
-- Tipe data numerik digunakan untuk menyimpan angka.

-- Membuat tabel sementara untuk demonstrasi tipe data numerik
CREATE TEMPORARY TABLE demo_numeric (
    id SERIAL PRIMARY KEY,
    
    -- INTEGER: bilangan bulat 4 byte (-2147483648 hingga +2147483647)
    nilai_integer INTEGER,
    
    -- SMALLINT: bilangan bulat 2 byte (-32768 hingga +32767)
    nilai_smallint SMALLINT,
    
    -- BIGINT: bilangan bulat 8 byte (sangat besar)
    nilai_bigint BIGINT,
    
    -- NUMERIC/DECIMAL: angka desimal dengan presisi yang ditentukan
    -- NUMERIC(precision, scale): precision = total digit, scale = digit setelah koma
    nilai_numeric NUMERIC(10, 2),
    
    -- REAL: angka floating-point 4 byte (presisi 6 digit)
    nilai_real REAL,
    
    -- DOUBLE PRECISION: angka floating-point 8 byte (presisi 15 digit)
    nilai_double DOUBLE PRECISION,
    
    -- MONEY: tipe khusus untuk nilai mata uang
    nilai_money MONEY
);

-- Memasukkan data dengan berbagai tipe numerik
INSERT INTO demo_numeric (
    nilai_integer, nilai_smallint, nilai_bigint, 
    nilai_numeric, nilai_real, nilai_double, nilai_money
)
VALUES
    (123456, 1000, 9223372036854775807, 1234.56, 123.456, 123.4567890123456, 1234.56),
    (-123456, -1000, -9223372036854775808, -1234.56, -123.456, -123.4567890123456, -1234.56),
    (0, 0, 0, 0.00, 0.0, 0.0, 0.00),
    (2147483647, 32767, 9223372036854775807, 9999999.99, 3.14159, 3.141592653589793, 999999.99),
    (NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- Melihat data yang telah dimasukkan
SELECT * FROM demo_numeric;

-- Operasi aritmatika dengan tipe numerik
SELECT 
    id,
    nilai_integer,
    nilai_numeric,
    -- Penjumlahan
    nilai_integer + nilai_numeric AS penjumlahan,
    -- Pengurangan
    nilai_integer - nilai_numeric AS pengurangan,
    -- Perkalian
    nilai_integer * nilai_numeric AS perkalian,
    -- Pembagian
    CASE 
        WHEN nilai_numeric <> 0 THEN nilai_integer / nilai_numeric 
        ELSE NULL 
    END AS pembagian
FROM demo_numeric
WHERE nilai_integer IS NOT NULL AND nilai_numeric IS NOT NULL;

-- Konversi antar tipe numerik
SELECT 
    id,
    nilai_integer,
    nilai_real,
    nilai_numeric,
    -- Konversi INTEGER ke NUMERIC
    nilai_integer::NUMERIC AS integer_ke_numeric,
    -- Konversi REAL ke NUMERIC (bisa kehilangan presisi)
    nilai_real::NUMERIC(10, 4) AS real_ke_numeric,
    -- Konversi NUMERIC ke INTEGER (membuang bagian desimal)
    nilai_numeric::INTEGER AS numeric_ke_integer,
    -- Konversi dengan CAST
    CAST(nilai_numeric AS INTEGER) AS numeric_ke_integer_cast
FROM demo_numeric
WHERE nilai_integer IS NOT NULL;

-- Fungsi pembulatan untuk tipe numerik
SELECT 
    id,
    nilai_numeric,
    nilai_real,
    -- Pembulatan ke atas
    CEIL(nilai_numeric) AS pembulatan_atas_numeric,
    CEIL(nilai_real) AS pembulatan_atas_real,
    -- Pembulatan ke bawah
    FLOOR(nilai_numeric) AS pembulatan_bawah_numeric,
    FLOOR(nilai_real) AS pembulatan_bawah_real,
    -- Pembulatan ke digit terdekat
    ROUND(nilai_numeric) AS pembulatan_numeric,
    ROUND(nilai_real) AS pembulatan_real,
    -- Pembulatan ke digit tertentu
    ROUND(nilai_numeric, 1) AS pembulatan_numeric_1_digit,
    ROUND(nilai_real, 1) AS pembulatan_real_1_digit
FROM demo_numeric
WHERE nilai_numeric IS NOT NULL AND nilai_real IS NOT NULL;

-- Fungsi matematika lainnya
SELECT 
    id,
    nilai_integer,
    nilai_real,
    -- Nilai absolut
    ABS(nilai_integer) AS absolut_integer,
    ABS(nilai_real) AS absolut_real,
    -- Akar kuadrat (hanya untuk nilai positif)
    CASE 
        WHEN nilai_real >= 0 THEN SQRT(nilai_real)
        ELSE NULL
    END AS akar_kuadrat,
    -- Pangkat
    POWER(nilai_integer, 2) AS kuadrat_integer,
    -- Logaritma
    CASE 
        WHEN nilai_real > 0 THEN LOG(nilai_real)
        ELSE NULL
    END AS logaritma_natural
FROM demo_numeric
WHERE nilai_integer IS NOT NULL AND nilai_real IS NOT NULL;

-- Membersihkan tabel sementara
DROP TABLE IF EXISTS demo_numeric;
