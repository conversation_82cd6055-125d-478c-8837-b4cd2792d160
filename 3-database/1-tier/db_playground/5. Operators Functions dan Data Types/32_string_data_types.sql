-- 32_string_data_types.sql
-- Contoh penggunaan tipe data string
-- Tipe data string digunakan untuk menyimpan teks.

-- Membuat tabel sementara untuk demonstrasi tipe data string
CREATE TEMPORARY TABLE demo_string (
    id SERIAL PRIMARY KEY,
    
    -- CHAR(n): string dengan panjang tetap, diisi spasi jika kurang
    nilai_char CHAR(10),
    
    -- VARCHAR(n): string dengan panjang variabel, maksimum n karakter
    nilai_varchar VARCHAR(50),
    
    -- TEXT: string dengan panjang tidak terbatas
    nilai_text TEXT,
    
    -- CITEXT: case-insensitive TEXT (perlu ekstensi citext)
    -- nilai_citext CITEXT,
    
    -- UUID: untuk menyimpan UUID (Universally Unique Identifier)
    nilai_uuid UUID,
    
    -- Tipe data untuk menyimpan byte data
    nilai_bytea BYTEA
);

-- Memasukkan data dengan berbagai tipe string
INSERT INTO demo_string (
    nilai_char, nilai_varchar, nilai_text, nilai_uuid, nilai_bytea
)
VALUES
    (
        'ABC', 
        'Ini adalah contoh VARCHAR', 
        'Ini adalah contoh TEXT yang bisa sangat panjang dan tidak dibatasi panjangnya seperti VARCHAR. TEXT cocok untuk menyimpan konten panjang seperti artikel, deskripsi, atau konten lainnya.',
        'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
        E'\\xDEADBEEF'
    ),
    (
        'DEF', 
        'Contoh lain VARCHAR dengan panjang berbeda', 
        'TEXT pendek',
        '123e4567-e89b-12d3-a456-************',
        E'\\x0123456789ABCDEF'
    ),
    (
        'GHI123456', -- Akan disimpan persis 10 karakter
        NULL, 
        NULL,
        NULL,
        NULL
    ),
    (
        'JKL', -- Akan diisi spasi hingga 10 karakter
        'VARCHAR dengan ''quote''', 
        'TEXT dengan "double quote" dan \'single quote\'',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        E'\\x00FF00FF'
    );

-- Melihat data yang telah dimasukkan
SELECT * FROM demo_string;

-- Melihat panjang sebenarnya dari nilai CHAR (tanpa spasi tambahan)
SELECT 
    id,
    nilai_char,
    LENGTH(nilai_char) AS panjang_dengan_spasi,
    LENGTH(TRIM(nilai_char)) AS panjang_tanpa_spasi
FROM demo_string;

-- Operasi string dasar
SELECT 
    id,
    nilai_varchar,
    -- Mengubah ke huruf besar
    UPPER(nilai_varchar) AS upper_case,
    -- Mengubah ke huruf kecil
    LOWER(nilai_varchar) AS lower_case,
    -- Menghitung panjang string
    LENGTH(nilai_varchar) AS panjang,
    -- Mengambil substring
    SUBSTRING(nilai_varchar, 1, 10) AS substring_10_char
FROM demo_string
WHERE nilai_varchar IS NOT NULL;

-- Operasi string lanjutan
SELECT 
    id,
    nilai_text,
    -- Mengganti substring
    REPLACE(nilai_text, 'TEXT', 'String') AS text_replaced,
    -- Mencari posisi substring
    POSITION('contoh' IN nilai_text) AS position_contoh,
    -- Memotong string dari kiri
    LEFT(nilai_text, 20) AS left_20_char,
    -- Memotong string dari kanan
    RIGHT(nilai_text, 20) AS right_20_char
FROM demo_string
WHERE nilai_text IS NOT NULL;

-- Operasi dengan UUID
SELECT 
    id,
    nilai_uuid,
    -- Menghasilkan UUID baru
    GEN_RANDOM_UUID() AS new_uuid
FROM demo_string
WHERE nilai_uuid IS NOT NULL;

-- Operasi dengan BYTEA
SELECT 
    id,
    nilai_bytea,
    -- Mengkonversi BYTEA ke representasi hex
    ENCODE(nilai_bytea, 'hex') AS hex_representation,
    -- Panjang BYTEA dalam byte
    OCTET_LENGTH(nilai_bytea) AS byte_length
FROM demo_string
WHERE nilai_bytea IS NOT NULL;

-- Konversi antar tipe string
SELECT 
    id,
    nilai_char,
    nilai_varchar,
    -- CHAR ke VARCHAR
    nilai_char::VARCHAR AS char_to_varchar,
    -- VARCHAR ke TEXT
    nilai_varchar::TEXT AS varchar_to_text,
    -- TEXT ke VARCHAR (bisa terpotong jika terlalu panjang)
    CASE 
        WHEN LENGTH(nilai_text) <= 50 THEN nilai_text::VARCHAR(50)
        ELSE SUBSTRING(nilai_text, 1, 50)::VARCHAR(50)
    END AS text_to_varchar
FROM demo_string
WHERE nilai_char IS NOT NULL;

-- Membersihkan tabel sementara
DROP TABLE IF EXISTS demo_string;
