-- 33_datetime_data_types.sql
-- Contoh penggunaan tipe data datetime
-- Tipe data datetime digunakan untuk menyimpan tanggal dan waktu.

-- Membuat tabel sementara untuk demonstrasi tipe data datetime
CREATE TEMPORARY TABLE demo_datetime (
    id SERIAL PRIMARY KEY,
    
    -- DATE: menyimpan tanggal (tahun, bulan, hari)
    nilai_date DATE,
    
    -- TIME: menyimpan waktu (jam, menit, detik)
    nilai_time TIME,
    
    -- TIME WITH TIME ZONE: menyimpan waktu dengan zona waktu
    nilai_time_tz TIME WITH TIME ZONE,
    
    -- TIMESTAMP: menyimpan tanggal dan waktu
    nilai_timestamp TIMESTAMP,
    
    -- TIMESTAMP WITH TIME ZONE: menyimpan tanggal dan waktu dengan zona waktu
    nilai_timestamp_tz TIMESTAMP WITH TIME ZONE,
    
    -- INTERVAL: menyimpan interval waktu
    nilai_interval INTERVAL
);

-- Memasukkan data dengan berbagai tipe datetime
INSERT INTO demo_datetime (
    nilai_date, nilai_time, nilai_time_tz, 
    nilai_timestamp, nilai_timestamp_tz, nilai_interval
)
VALUES
    (
        '2025-05-15', -- DATE
        '14:30:00', -- TIME
        '14:30:00+07', -- TIME WITH TIME ZONE
        '2025-05-15 14:30:00', -- TIMESTAMP
        '2025-05-15 14:30:00+07', -- TIMESTAMP WITH TIME ZONE
        '1 year 2 months 3 days 4 hours 5 minutes 6 seconds' -- INTERVAL
    ),
    (
        '2025-12-31', -- DATE
        '23:59:59', -- TIME
        '23:59:59+07', -- TIME WITH TIME ZONE
        '2025-12-31 23:59:59', -- TIMESTAMP
        '2025-12-31 23:59:59+07', -- TIMESTAMP WITH TIME ZONE
        '10 years' -- INTERVAL
    ),
    (
        '2025-01-01', -- DATE
        '00:00:00', -- TIME
        '00:00:00+07', -- TIME WITH TIME ZONE
        '2025-01-01 00:00:00', -- TIMESTAMP
        '2025-01-01 00:00:00+07', -- TIMESTAMP WITH TIME ZONE
        '1 day 12 hours' -- INTERVAL
    ),
    (
        CURRENT_DATE, -- DATE saat ini
        CURRENT_TIME, -- TIME saat ini
        CURRENT_TIME, -- TIME WITH TIME ZONE saat ini
        CURRENT_TIMESTAMP, -- TIMESTAMP saat ini
        CURRENT_TIMESTAMP, -- TIMESTAMP WITH TIME ZONE saat ini
        '1 month' -- INTERVAL
    );

-- Melihat data yang telah dimasukkan
SELECT * FROM demo_datetime;

-- Operasi dengan DATE
SELECT 
    id,
    nilai_date,
    -- Tanggal saat ini
    CURRENT_DATE AS tanggal_sekarang,
    -- Selisih hari
    CURRENT_DATE - nilai_date AS selisih_hari,
    -- Menambah hari
    nilai_date + 7 AS tanggal_plus_7_hari,
    -- Mengurangi hari
    nilai_date - 7 AS tanggal_minus_7_hari,
    -- Mengekstrak komponen
    EXTRACT(YEAR FROM nilai_date) AS tahun,
    EXTRACT(MONTH FROM nilai_date) AS bulan,
    EXTRACT(DAY FROM nilai_date) AS hari,
    -- Hari dalam seminggu (0=Minggu, 1=Senin, ..., 6=Sabtu)
    EXTRACT(DOW FROM nilai_date) AS hari_dalam_seminggu
FROM demo_datetime;

-- Operasi dengan TIME
SELECT 
    id,
    nilai_time,
    nilai_time_tz,
    -- Waktu saat ini
    CURRENT_TIME AS waktu_sekarang,
    -- Menambah waktu
    nilai_time + '1 hour'::INTERVAL AS time_plus_1_jam,
    -- Mengekstrak komponen
    EXTRACT(HOUR FROM nilai_time) AS jam,
    EXTRACT(MINUTE FROM nilai_time) AS menit,
    EXTRACT(SECOND FROM nilai_time) AS detik,
    -- Konversi zona waktu
    nilai_time_tz AT TIME ZONE 'UTC' AS waktu_dalam_utc
FROM demo_datetime;

-- Operasi dengan TIMESTAMP
SELECT 
    id,
    nilai_timestamp,
    nilai_timestamp_tz,
    -- Timestamp saat ini
    CURRENT_TIMESTAMP AS timestamp_sekarang,
    -- Menambah interval
    nilai_timestamp + '1 day'::INTERVAL AS timestamp_plus_1_hari,
    nilai_timestamp + '1 month'::INTERVAL AS timestamp_plus_1_bulan,
    nilai_timestamp + '1 year'::INTERVAL AS timestamp_plus_1_tahun,
    -- Selisih timestamp
    AGE(CURRENT_TIMESTAMP, nilai_timestamp) AS selisih_waktu,
    -- Konversi zona waktu
    nilai_timestamp_tz AT TIME ZONE 'UTC' AS timestamp_dalam_utc
FROM demo_datetime;

-- Operasi dengan INTERVAL
SELECT 
    id,
    nilai_interval,
    -- Operasi dengan interval
    nilai_interval * 2 AS interval_kali_2,
    nilai_interval / 2 AS interval_bagi_2,
    -- Mengekstrak komponen
    EXTRACT(YEAR FROM nilai_interval) AS tahun,
    EXTRACT(MONTH FROM nilai_interval) AS bulan,
    EXTRACT(DAY FROM nilai_interval) AS hari,
    EXTRACT(HOUR FROM nilai_interval) AS jam,
    -- Justifikasi interval (normalisasi)
    JUSTIFY_DAYS(nilai_interval) AS interval_justify_days,
    JUSTIFY_HOURS(nilai_interval) AS interval_justify_hours,
    JUSTIFY_INTERVAL(nilai_interval) AS interval_justify_full
FROM demo_datetime;

-- Konversi antar tipe datetime
SELECT 
    id,
    nilai_date,
    nilai_time,
    nilai_timestamp,
    -- DATE + TIME ke TIMESTAMP
    nilai_date + nilai_time AS date_plus_time,
    -- TIMESTAMP ke DATE
    nilai_timestamp::DATE AS timestamp_to_date,
    -- TIMESTAMP ke TIME
    nilai_timestamp::TIME AS timestamp_to_time,
    -- TEXT ke DATE
    '2025-06-15'::DATE AS text_to_date,
    -- TEXT ke TIMESTAMP
    '2025-06-15 10:30:00'::TIMESTAMP AS text_to_timestamp
FROM demo_datetime;

-- Fungsi format datetime
SELECT 
    id,
    nilai_date,
    nilai_timestamp,
    -- Format DATE
    TO_CHAR(nilai_date, 'DD Mon YYYY') AS format_date_1,
    TO_CHAR(nilai_date, 'YYYY-MM-DD') AS format_date_2,
    TO_CHAR(nilai_date, 'Day, DD Month YYYY') AS format_date_3,
    -- Format TIMESTAMP
    TO_CHAR(nilai_timestamp, 'HH24:MI:SS') AS format_time,
    TO_CHAR(nilai_timestamp, 'DD Mon YYYY HH24:MI:SS') AS format_timestamp,
    TO_CHAR(nilai_timestamp, 'YYYY-MM-DD"T"HH24:MI:SS') AS format_iso
FROM demo_datetime;

-- Membersihkan tabel sementara
DROP TABLE IF EXISTS demo_datetime;
