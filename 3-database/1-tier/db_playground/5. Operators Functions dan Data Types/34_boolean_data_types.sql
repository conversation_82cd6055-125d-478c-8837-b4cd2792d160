-- 34_boolean_data_types.sql
-- <PERSON><PERSON><PERSON> penggunaan tipe data boolean
-- Tipe data boolean digunakan untuk menyimpan nilai kebenaran (TRUE/FALSE).

-- Membuat tabel sementara untuk demonstrasi tipe data boolean
CREATE TEMPORARY TABLE demo_boolean (
    id SERIAL PRIMARY KEY,
    
    -- BOOLEAN: menyimpan nilai TRUE, FALSE, atau NULL
    nilai_boolean BOOLEAN,
    
    -- <PERSON><PERSON><PERSON> lain untuk demonstrasi
    nama VARCHAR(50),
    nilai INTEGER
);

-- Memasukkan data dengan berbagai nilai boolean
INSERT INTO demo_boolean (nilai_boolean, nama, nilai)
VALUES
    (TRUE, 'Item 1', 100),
    (FALSE, 'Item 2', 50),
    (NULL, 'Item 3', 75),
    ('yes', 'Item 4', 90),    -- 'yes', 'true', 't', 'y', '1' dikonversi ke TRUE
    ('no', 'Item 5', 60),     -- 'no', 'false', 'f', 'n', '0' dikonversi ke FALSE
    ('t', 'Item 6', 85),
    ('f', 'Item 7', 40),
    ('1', 'Item 8', 95),
    ('0', 'Item 9', 30);

-- Melihat data yang telah dimasukkan
SELECT * FROM demo_boolean;

-- Memfilter data berdasarkan nilai boolean
-- Mencari item dengan nilai_boolean = TRUE
SELECT id, nama, nilai_boolean
FROM demo_boolean
WHERE nilai_boolean = TRUE;

-- Mencari item dengan nilai_boolean = FALSE
SELECT id, nama, nilai_boolean
FROM demo_boolean
WHERE nilai_boolean = FALSE;

-- Mencari item dengan nilai_boolean IS NULL
SELECT id, nama, nilai_boolean
FROM demo_boolean
WHERE nilai_boolean IS NULL;

-- Operasi logika dengan boolean
SELECT 
    id,
    nama,
    nilai_boolean,
    nilai,
    -- AND: TRUE jika kedua kondisi TRUE
    nilai_boolean AND (nilai > 50) AS boolean_and_nilai_besar,
    -- OR: TRUE jika salah satu kondisi TRUE
    nilai_boolean OR (nilai > 50) AS boolean_or_nilai_besar,
    -- NOT: membalikkan nilai boolean
    NOT nilai_boolean AS boolean_not
FROM demo_boolean
WHERE nilai_boolean IS NOT NULL;

-- Menggunakan boolean dalam ekspresi kondisional
SELECT 
    id,
    nama,
    nilai_boolean,
    -- CASE dengan boolean
    CASE 
        WHEN nilai_boolean = TRUE THEN 'Aktif'
        WHEN nilai_boolean = FALSE THEN 'Tidak Aktif'
        ELSE 'Tidak Diketahui'
    END AS status,
    -- IF dengan boolean (menggunakan CASE)
    CASE 
        WHEN nilai_boolean = TRUE THEN nilai * 1.1
        ELSE nilai
    END AS nilai_disesuaikan
FROM demo_boolean;

-- Konversi boolean ke tipe lain
SELECT 
    id,
    nama,
    nilai_boolean,
    -- Boolean ke integer (1 atau 0)
    CASE 
        WHEN nilai_boolean = TRUE THEN 1
        WHEN nilai_boolean = FALSE THEN 0
        ELSE NULL
    END AS boolean_to_int,
    -- Boolean ke text
    nilai_boolean::TEXT AS boolean_to_text,
    -- Boolean ke varchar dengan format kustom
    CASE 
        WHEN nilai_boolean = TRUE THEN 'Ya'
        WHEN nilai_boolean = FALSE THEN 'Tidak'
        ELSE 'N/A'
    END AS boolean_to_custom
FROM demo_boolean;

-- Konversi tipe lain ke boolean
SELECT 
    id,
    nama,
    nilai,
    -- Integer ke boolean (0 = FALSE, lainnya = TRUE)
    (nilai > 0)::BOOLEAN AS nilai_positif,
    -- Text ke boolean
    (nama LIKE '%1%')::BOOLEAN AS nama_mengandung_1,
    -- Ekspresi kompleks ke boolean
    (nilai > 50 AND nama LIKE 'Item%')::BOOLEAN AS kriteria_terpenuhi
FROM demo_boolean;

-- Agregasi dengan boolean
SELECT 
    -- Menghitung jumlah TRUE
    COUNT(*) FILTER (WHERE nilai_boolean = TRUE) AS jumlah_true,
    -- Menghitung jumlah FALSE
    COUNT(*) FILTER (WHERE nilai_boolean = FALSE) AS jumlah_false,
    -- Menghitung jumlah NULL
    COUNT(*) FILTER (WHERE nilai_boolean IS NULL) AS jumlah_null,
    -- Menghitung total
    COUNT(*) AS jumlah_total,
    -- Persentase TRUE
    ROUND(
        COUNT(*) FILTER (WHERE nilai_boolean = TRUE)::NUMERIC / 
        COUNT(*) * 100, 
        2
    ) AS persentase_true
FROM demo_boolean;

-- Membersihkan tabel sementara
DROP TABLE IF EXISTS demo_boolean;
