-- 35_json_data_types.sql
-- <PERSON><PERSON>h penggunaan tipe data JSON
-- Tipe data JSON digunakan untuk menyimpan data dalam format JSON.

-- Membuat tabel sementara untuk demonstrasi tipe data JSON
CREATE TEMPORARY TABLE demo_json (
    id SERIAL PRIMARY KEY,
    
    -- JSON: menyimpan data JSON tanpa validasi
    nilai_json JSON,
    
    -- JSONB: menyimpan data JSON dalam format biner, lebih efisien untuk query
    nilai_jsonb JSONB,
    
    -- <PERSON><PERSON><PERSON> lain untuk demonstrasi
    keterangan VARCHAR(50)
);

-- Memasukkan data dengan berbagai nilai JSON
INSERT INTO demo_json (nilai_json, nilai_jsonb, keterangan)
VALUES
    (
        '{"nama": "<PERSON>", "usia": 15, "hobi": ["membaca", "menulis"]}',
        '{"nama": "<PERSON>", "usia": 15, "hobi": ["membaca", "menulis"]}',
        'Data siswa 1'
    ),
    (
        '{"nama": "Budi", "usia": 16, "alamat": {"jalan": "Jl. <PERSON>", "kota": "Jakarta"}}',
        '{"nama": "Budi", "usia": 16, "alamat": {"jalan": "Jl. Mawar", "kota": "Jakarta"}}',
        'Data siswa 2'
    ),
    (
        '{"nama": "<PERSON>", "usia": 15, "nilai": {"matematika": 90, "bahasa": 85}}',
        '{"nama": "<PERSON>", "usia": 15, "nilai": {"matematika": 90, "bahasa": 85}}',
        'Data siswa 3'
    ),
    (
        '[1, 2, 3, 4, 5]',
        '[1, 2, 3, 4, 5]',
        'Array sederhana'
    ),
    (
        '{"setoran": [{"jenis": "ziyadah", "halaman": 10}, {"jenis": "rabth", "halaman": 15}]}',
        '{"setoran": [{"jenis": "ziyadah", "halaman": 10}, {"jenis": "rabth", "halaman": 15}]}',
        'Data setoran'
    );

-- Melihat data yang telah dimasukkan
SELECT * FROM demo_json;

-- Mengakses elemen JSON dengan operator ->
-- -> mengembalikan elemen JSON
SELECT 
    id,
    keterangan,
    nilai_json->'nama' AS nama_json,
    nilai_jsonb->'nama' AS nama_jsonb,
    nilai_json->'usia' AS usia_json,
    nilai_jsonb->'usia' AS usia_jsonb
FROM demo_json
WHERE nilai_json->>'nama' IS NOT NULL;

-- Mengakses elemen JSON dengan operator ->>
-- ->> mengembalikan elemen sebagai TEXT
SELECT 
    id,
    keterangan,
    nilai_json->>'nama' AS nama_text,
    nilai_jsonb->>'nama' AS nama_text_b,
    nilai_json->>'usia' AS usia_text,
    nilai_jsonb->>'usia' AS usia_text_b
FROM demo_json
WHERE nilai_json->>'nama' IS NOT NULL;

-- Mengakses elemen nested JSON
SELECT 
    id,
    keterangan,
    nilai_json->'alamat'->>'jalan' AS jalan,
    nilai_json->'alamat'->>'kota' AS kota
FROM demo_json
WHERE nilai_json->'alamat' IS NOT NULL;

-- Mengakses elemen array JSON
SELECT 
    id,
    keterangan,
    nilai_json->'hobi'->0 AS hobi_pertama,
    nilai_json->'hobi'->1 AS hobi_kedua
FROM demo_json
WHERE nilai_json->'hobi' IS NOT NULL;

-- Mengakses elemen array nested JSON
SELECT 
    id,
    keterangan,
    nilai_json->'setoran'->0->>'jenis' AS jenis_setoran_pertama,
    nilai_json->'setoran'->0->>'halaman' AS halaman_setoran_pertama
FROM demo_json
WHERE nilai_json->'setoran' IS NOT NULL;

-- Memfilter berdasarkan nilai dalam JSON
SELECT 
    id,
    keterangan,
    nilai_jsonb->>'nama' AS nama,
    nilai_jsonb->>'usia' AS usia
FROM demo_json
WHERE 
    (nilai_jsonb->>'usia')::INTEGER > 15;

-- Memfilter berdasarkan keberadaan key dalam JSON
SELECT 
    id,
    keterangan,
    nilai_jsonb
FROM demo_json
WHERE 
    nilai_jsonb ? 'alamat';

-- Memfilter berdasarkan keberadaan path dalam JSON
SELECT 
    id,
    keterangan,
    nilai_jsonb
FROM demo_json
WHERE 
    nilai_jsonb @> '{"nama": "Ahmad"}';

-- Menggunakan fungsi JSON_ARRAY_ELEMENTS
-- Mengubah array JSON menjadi baris
SELECT 
    id,
    keterangan,
    json_array_elements(nilai_json->'hobi') AS hobi
FROM demo_json
WHERE nilai_json->'hobi' IS NOT NULL;

-- Menggunakan fungsi JSON_EACH
-- Mengubah objek JSON menjadi baris key-value
SELECT 
    id,
    keterangan,
    json_each(nilai_json).*
FROM demo_json
WHERE id = 1;

-- Menggunakan fungsi JSON_OBJECT_KEYS
-- Mendapatkan semua key dari objek JSON
SELECT 
    id,
    keterangan,
    json_object_keys(nilai_json) AS json_keys
FROM demo_json
WHERE json_typeof(nilai_json) = 'object';

-- Menggunakan fungsi JSON_BUILD_OBJECT
-- Membuat objek JSON baru
SELECT 
    id,
    json_build_object(
        'id', id,
        'keterangan', keterangan,
        'nama', nilai_json->>'nama',
        'info', json_build_object(
            'usia', (nilai_json->>'usia')::INTEGER,
            'timestamp', CURRENT_TIMESTAMP
        )
    ) AS new_json
FROM demo_json
WHERE nilai_json->>'nama' IS NOT NULL;

-- Menggunakan fungsi JSON_BUILD_ARRAY
-- Membuat array JSON baru
SELECT 
    json_build_array(
        1,
        'text',
        TRUE,
        json_build_object('key', 'value'),
        CURRENT_DATE
    ) AS array_json;

-- Membersihkan tabel sementara
DROP TABLE IF EXISTS demo_json;
