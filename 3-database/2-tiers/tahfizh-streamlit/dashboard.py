import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import sqlalchemy
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Get Supabase connection string
connection_string = os.getenv("SUPABASE_CONNECTION_STRING")

# Create SQLAlchemy engine
engine = create_engine(connection_string)

# Set page config
st.set_page_config(
    page_title="Dashboard Pendidikan",
    page_icon="📚",
    layout="wide"
)

st.title("Dashboard Manajemen Pendidikan")

# Function to execute SQL queries
def run_query(query):
    with engine.connect() as conn:
        return pd.read_sql(text(query), conn)

# 1. STATISTIK UMUM
st.header("1. Statistik Umum")

# Create three columns for statistics
col1, col2, col3 = st.columns(3)

# Query for total counts
total_siswa = run_query("SELECT COUNT(*) as total FROM siswa").iloc[0]['total']
total_pengajar = run_query("SELECT COUNT(*) as total FROM pengajar").iloc[0]['total']
total_kelas = run_query("SELECT COUNT(*) as total FROM kelas").iloc[0]['total']
total_halaqoh = run_query("SELECT COUNT(*) as total FROM halaqoh").iloc[0]['total']

# Display metrics
with col1:
    st.metric("Total Siswa", total_siswa)
    st.metric("Total Pengajar", total_pengajar)

with col2:
    st.metric("Total Kelas", total_kelas)
    st.metric("Total Halaqoh", total_halaqoh)
    
with col3:
    # Calculate and display ratio
    ratio = round(total_siswa / total_pengajar, 2) if total_pengajar > 0 else 0
    st.metric("Rasio Siswa-Pengajar", f"{ratio}:1")

# Gender distribution
gender_siswa = run_query("""
    SELECT gender, COUNT(*) as count 
    FROM siswa 
    GROUP BY gender
""")

gender_pengajar = run_query("""
    SELECT gender, COUNT(*) as count 
    FROM pengajar 
    GROUP BY gender
""")

col1, col2 = st.columns(2)

with col1:
    st.subheader("Distribusi Gender Siswa")
    fig = px.pie(gender_siswa, values='count', names='gender', hole=0.4)
    st.plotly_chart(fig, use_container_width=True)

with col2:
    st.subheader("Distribusi Gender Pengajar")
    fig = px.pie(gender_pengajar, values='count', names='gender', hole=0.4)
    st.plotly_chart(fig, use_container_width=True)

# 2. PERFORMA PEMBELAJARAN AL-QURAN
st.header("2. Performa Pembelajaran Al-Quran")

# Query for average progress per halaqoh
progress_per_halaqoh = run_query("""
    SELECT h.nama as halaqoh_nama, 
           COUNT(s.id) as jumlah_setoran,
           AVG(s.halaman_akhir - s.halaman_awal + 1) as rata_rata_halaman
    FROM setoran s
    JOIN siswa si ON s.siswa_id = si.id
    JOIN halaqoh h ON si.halaqoh_id = h.id
    GROUP BY h.nama
    ORDER BY rata_rata_halaman DESC
""")

# Query for setoran frequency over time
setoran_frequency = run_query("""
    SELECT DATE_TRUNC('week', waktu_setoran) as week, 
           COUNT(*) as jumlah_setoran
    FROM setoran
    GROUP BY week
    ORDER BY week
""")
setoran_frequency['week'] = pd.to_datetime(setoran_frequency['week'])

# Create visualizations
col1, col2 = st.columns(2)

with col1:
    st.subheader("Progres Hafalan per Halaqoh")
    fig = px.bar(progress_per_halaqoh, 
                x='halaqoh_nama', 
                y='rata_rata_halaman',
                color='jumlah_setoran',
                labels={'halaqoh_nama': 'Halaqoh', 
                        'rata_rata_halaman': 'Rata-rata Halaman per Setoran',
                        'jumlah_setoran': 'Jumlah Setoran'})
    st.plotly_chart(fig, use_container_width=True)

with col2:
    st.subheader("Frekuensi Setoran Mingguan")
    fig = px.line(setoran_frequency, 
                 x='week', 
                 y='jumlah_setoran',
                 labels={'week': 'Minggu', 'jumlah_setoran': 'Jumlah Setoran'})
    st.plotly_chart(fig, use_container_width=True)

# Query for most common verses/pages
common_pages = run_query("""
    SELECT halaman_awal as halaman, COUNT(*) as frekuensi
    FROM setoran
    GROUP BY halaman_awal
    ORDER BY frekuensi DESC
    LIMIT 20
""")

st.subheader("Halaman Al-Quran yang Paling Sering Disetorkan")
fig = px.bar(common_pages, 
            x='halaman', 
            y='frekuensi',
            labels={'halaman': 'Halaman Al-Quran', 'frekuensi': 'Frekuensi Setoran'})
st.plotly_chart(fig, use_container_width=True)

# 3. MANAJEMEN KELAS DAN HALAQOH
st.header("3. Manajemen Kelas dan Halaqoh")

# Query for student distribution
siswa_per_kelas = run_query("""
    SELECT k.nama as kelas_nama, COUNT(*) as jumlah_siswa
    FROM siswa s
    JOIN kelas k ON s.kelas_id = k.id
    GROUP BY k.nama
    ORDER BY jumlah_siswa DESC
""")

siswa_per_halaqoh = run_query("""
    SELECT h.nama as halaqoh_nama, COUNT(*) as jumlah_siswa
    FROM siswa s
    JOIN halaqoh h ON s.halaqoh_id = h.id
    GROUP BY h.nama
    ORDER BY jumlah_siswa DESC
""")

# Query for teacher workload
beban_pengajar = run_query("""
    SELECT p.nama as pengajar_nama, 
           COUNT(DISTINCT pp.pelajaran_id) as jumlah_pelajaran,
           COUNT(DISTINCT pp.kelas_id) as jumlah_kelas,
           COUNT(DISTINCT h.id) as jumlah_halaqoh
    FROM pengajar p
    LEFT JOIN pengajar_pelajaran pp ON p.id = pp.pengajar_id
    LEFT JOIN halaqoh h ON p.id = h.pengajar_id
    GROUP BY p.nama
    ORDER BY (COUNT(DISTINCT pp.pelajaran_id) + COUNT(DISTINCT pp.kelas_id) + COUNT(DISTINCT h.id)) DESC
    LIMIT 10
""")

# Create visualizations
col1, col2 = st.columns(2)

with col1:
    st.subheader("Distribusi Siswa per Kelas")
    fig = px.bar(siswa_per_kelas, 
                x='kelas_nama', 
                y='jumlah_siswa',
                labels={'kelas_nama': 'Kelas', 'jumlah_siswa': 'Jumlah Siswa'})
    st.plotly_chart(fig, use_container_width=True)

with col2:
    st.subheader("Distribusi Siswa per Halaqoh")
    fig = px.bar(siswa_per_halaqoh, 
                x='halaqoh_nama', 
                y='jumlah_siswa',
                labels={'halaqoh_nama': 'Halaqoh', 'jumlah_siswa': 'Jumlah Siswa'})
    st.plotly_chart(fig, use_container_width=True)

st.subheader("Beban Kerja Pengajar")
fig = go.Figure()
fig.add_trace(go.Bar(
    x=beban_pengajar['pengajar_nama'],
    y=beban_pengajar['jumlah_pelajaran'],
    name='Jumlah Pelajaran'
))
fig.add_trace(go.Bar(
    x=beban_pengajar['pengajar_nama'],
    y=beban_pengajar['jumlah_kelas'],
    name='Jumlah Kelas'
))
fig.add_trace(go.Bar(
    x=beban_pengajar['pengajar_nama'],
    y=beban_pengajar['jumlah_halaqoh'],
    name='Jumlah Halaqoh'
))
fig.update_layout(barmode='stack')
st.plotly_chart(fig, use_container_width=True)

# Query for class performance comparison
performa_kelas = run_query("""
    SELECT k.nama as kelas_nama, 
           COUNT(s.id) as jumlah_setoran,
           AVG(s.halaman_akhir - s.halaman_awal + 1) as rata_rata_halaman
    FROM setoran s
    JOIN siswa si ON s.siswa_id = si.id
    JOIN kelas k ON si.kelas_id = k.id
    GROUP BY k.nama
    ORDER BY rata_rata_halaman DESC
""")

st.subheader("Performa Kelas dalam Setoran Al-Quran")
fig = px.scatter(performa_kelas, 
                x='jumlah_setoran', 
                y='rata_rata_halaman',
                size='jumlah_setoran',
                color='kelas_nama',
                labels={'jumlah_setoran': 'Jumlah Setoran', 
                        'rata_rata_halaman': 'Rata-rata Halaman per Setoran',
                        'kelas_nama': 'Kelas'})
st.plotly_chart(fig, use_container_width=True)
