<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1f6e8c;
            --secondary-color: #0e2954;
            --accent-color: #84a7a1;
            --light-color: #f1bebe;
            --dark-color: #333;
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            color: var(--dark-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem 0;
            text-align: center;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            box-shadow: var(--box-shadow);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            font-size: 1.2rem;
            font-weight: 300;
            opacity: 0.9;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            text-align: center;
        }

        .verse-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .verse-card {
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            transition: transform 0.3s ease;
        }

        .verse-card:hover {
            transform: translateY(-5px);
        }

        .verse-header {
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .verse-number {
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .verse-number-circle {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 30px;
            height: 30px;
            background-color: white;
            color: var(--primary-color);
            border-radius: 50%;
            font-weight: bold;
        }

        .verse-page {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .verse-content {
            padding: 1.5rem;
        }

        .arabic-text {
            font-family: 'Amiri', serif;
            font-size: 2rem;
            line-height: 2;
            text-align: right;
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        .indo-text {
            font-size: 1rem;
            color: var(--dark-color);
            border-top: 1px solid #eee;
            padding-top: 1rem;
        }

        .read-text {
            font-size: 0.9rem;
            color: var(--accent-color);
            margin-top: 1rem;
            font-style: italic;
        }

        .info-bar {
            background-color: var(--light-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-label {
            font-weight: 500;
            color: var(--primary-color);
        }

        .sajdah-badge {
            background-color: var(--accent-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }

        footer {
            text-align: center;
            margin-top: 3rem;
            padding: 1.5rem;
            background-color: var(--secondary-color);
            color: white;
            border-radius: var(--border-radius);
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }

            .arabic-text {
                font-size: 1.5rem;
            }

            .info-bar {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Surah Maryam</h1>
            <p class="subtitle">Surat ke-19 dalam Al-Qur'an</p>
        </header>

        <div id="info-container" class="info-bar" style="display: none;">
            <div class="info-item">
                <span class="info-label">Surah:</span>
                <span id="surah-name"></span>
            </div>
            <div class="info-item">
                <span class="info-label">Jumlah Ayat:</span>
                <span id="total-verses"></span>
            </div>
            <div class="info-item">
                <span class="info-label">Juz:</span>
                <span id="juz-id"></span>
            </div>
        </div>

        <div id="loading" class="loading">
            <p>Memuat ayat-ayat Surah Maryam...</p>
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="verses" class="verse-container"></div>

        <footer>
            <p>© 2025 Maryam API Viewer | Dibuat dengan ❤️</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            fetchMaryamVerses();
        });

        async function fetchMaryamVerses() {
            try {
                const response = await fetch('https://adorable-recreation-production.up.railway.app/maryam');

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();
                displayVerses(data);
            } catch (error) {
                console.error('Error fetching data:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').innerHTML = `
                    <p>Gagal memuat data. Kemungkinan penyebabnya:</p>
                    <ol style="text-align: left; margin-top: 10px; margin-left: 20px;">
                        <li>API server tidak berjalan. Jalankan server dengan perintah: <code>python maryam_api.py</code></li>
                        <li>CORS belum diaktifkan di API. Pastikan API sudah diupdate dengan middleware CORS</li>
                    </ol>
                    <p style="margin-top: 10px;">Error: ${error.message}</p>
                `;
            }
        }

        function displayVerses(data) {
            const versesContainer = document.getElementById('verses');
            const loadingElement = document.getElementById('loading');
            const infoContainer = document.getElementById('info-container');

            // Update info bar
            document.getElementById('surah-name').textContent = data.surah_name;
            document.getElementById('total-verses').textContent = data.total_verses;
            document.getElementById('juz-id').textContent = data.verses[0].juz_id;

            // Show info bar
            infoContainer.style.display = 'flex';

            // Hide loading
            loadingElement.style.display = 'none';

            // Create verse cards
            data.verses.forEach(verse => {
                const verseCard = document.createElement('div');
                verseCard.className = 'verse-card';

                const isSajdah = verse.is_sajadah ? '<span class="sajdah-badge">Sajdah</span>' : '';

                verseCard.innerHTML = `
                    <div class="verse-header">
                        <div class="verse-number">
                            <span class="verse-number-circle">${verse.verse_id}</span>
                            Ayat ${verse.verse_id} ${isSajdah}
                        </div>
                        <div class="verse-page">
                            Halaman ${verse.page}
                        </div>
                    </div>
                    <div class="verse-content">
                        <div class="arabic-text">${verse.verse_text}</div>
                        <div class="indo-text">${verse.indo_text}</div>
                        <div class="read-text">${verse.read_text || ''}</div>
                    </div>
                `;

                versesContainer.appendChild(verseCard);
            });
        }
    </script>
</body>
</html>
