import os
import psycopg2
import json
import socket
from dotenv import load_dotenv
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Force IPv4 connections
original_getaddrinfo = socket.getaddrinfo

def getaddrinfo_ipv4_only(*args, **kwargs):
    responses = original_getaddrinfo(*args, **kwargs)
    return [response for response in responses if response[0] == socket.AF_INET]

# Replace the original getaddrinfo with our IPv4-only version
socket.getaddrinfo = getaddrinfo_ipv4_only

# Load environment variables from .env file
load_dotenv()

# Get database connection parameters from environment variables
# First try to get individual connection parameters
db_host = os.getenv("DB_HOST")
db_port = os.getenv("DB_PORT", "5432")
db_name = os.getenv("DB_NAME")
db_user = os.getenv("DB_USER")
db_password = os.getenv("DB_PASSWORD")

# Then try to get connection string (as fallback)
connection_string = os.getenv("SUPABASE_CONNECTION_STRING")

# Get port from environment variable (for Railway)
port = int(os.getenv("PORT", 8000))

app = FastAPI(title="Surah Maryam API", description="API to retrieve verses from Surah Maryam")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Sample data for fallback when database is unreachable
SAMPLE_VERSES = [
    {
        "id": 2307,
        "surah_id": 19,
        "verse_id": 1,
        "verse_text": "كهيعص",
        "indo_text": "Kaaf Haa Yaa 'Ain Shaad.",
        "read_text": "Kaaf Haa Yaa 'Ain Shaad",
        "page": 305,
        "surah_name": "Maryam",
        "is_sajadah": False,
        "juz_id": 16
    },
    {
        "id": 2308,
        "surah_id": 19,
        "verse_id": 2,
        "verse_text": "ذِكْرُ رَحْمَتِ رَبِّكَ عَبْدَهُ زَكَرِيَّا",
        "indo_text": "(Yang dibacakan ini adalah) penjelasan tentang rahmat Tuhan kamu kepada hamba-Nya, Zakaria,",
        "read_text": "Zikru rahmati rabbika 'abdahu zakariyya",
        "page": 305,
        "surah_name": "Maryam",
        "is_sajadah": False,
        "juz_id": 16
    },
    {
        "id": 2309,
        "surah_id": 19,
        "verse_id": 3,
        "verse_text": "إِذْ نَادَى رَبَّهُ نِدَاءً خَفِيًّا",
        "indo_text": "yaitu tatkala ia berdoa kepada Tuhannya dengan suara yang lembut.",
        "read_text": "Iz nada rabbahu nida'an khafiyya",
        "page": 305,
        "surah_name": "Maryam",
        "is_sajadah": False,
        "juz_id": 16
    },
    {
        "id": 2310,
        "surah_id": 19,
        "verse_id": 4,
        "verse_text": "قَالَ رَبِّ إِنِّي وَهَنَ الْعَظْمُ مِنِّي وَاشْتَعَلَ الرَّأْسُ شَيْبًا وَلَمْ أَكُنْ بِدُعَائِكَ رَبِّ شَقِيًّا",
        "indo_text": "Ia berkata \"Ya Tuhanku, sesungguhnya tulangku telah lemah dan kepalaku telah ditumbuhi uban, dan aku belum pernah kecewa dalam berdoa kepada Engkau, ya Tuhanku.",
        "read_text": "Qala rabbi inni wahanal-'azmu minni wasyta'alar-ra'su syaiban walam akun bidu'a'ika rabbi syaqiyya",
        "page": 305,
        "surah_name": "Maryam",
        "is_sajadah": False,
        "juz_id": 16
    },
    {
        "id": 2311,
        "surah_id": 19,
        "verse_id": 5,
        "verse_text": "وَإِنِّي خِفْتُ الْمَوَالِيَ مِنْ وَرَائِي وَكَانَتِ امْرَأَتِي عَاقِرًا فَهَبْ لِي مِنْ لَدُنْكَ وَلِيًّا",
        "indo_text": "Dan sesungguhnya aku khawatir terhadap mawaliku sepeninggalku, sedang isteriku adalah seorang yang mandul, maka anugerahilah aku dari sisi Engkau seorang putera,",
        "read_text": "Wa inni khiftul-mawaliya miw wara'i wakanatimra'ati 'aqiran fahab li mil ladunka waliyya",
        "page": 305,
        "surah_name": "Maryam",
        "is_sajadah": False,
        "juz_id": 16
    }
]

def get_maryam_verses():
    """
    Retrieve all verses from Surah Maryam (Surah 19) from the database
    If database is unreachable, return sample data
    """
    try:
        # Set connection timeout to 5 seconds to avoid long waits
        connect_timeout = 5

        # Connect to the database
        if db_host and db_name and db_user and db_password:
            # Use individual connection parameters
            conn = psycopg2.connect(
                host=db_host,
                port=db_port,
                dbname=db_name,
                user=db_user,
                password=db_password,
                connect_timeout=connect_timeout
            )
        elif connection_string:
            # Use connection string
            conn = psycopg2.connect(connection_string + " connect_timeout=5")
        else:
            print("No database connection parameters provided")
            print("Using sample data as fallback")
            return SAMPLE_VERSES

        cursor = conn.cursor()

        # Execute query to get all verses from Surah Maryam (surah_id = 19)
        cursor.execute("""
            SELECT id, surah_id, verse_id, verse_text, indo_text, read_text, page, surah_name, is_sajadah, juz_id
            FROM quranidn
            WHERE surah_id = 19
            ORDER BY verse_id ASC
        """)

        # Fetch all rows
        rows = cursor.fetchall()

        # Close connection
        cursor.close()
        conn.close()

        # Convert rows to list of dictionaries
        verses = []
        for row in rows:
            verse = {
                "id": row[0],
                "surah_id": row[1],
                "verse_id": row[2],
                "verse_text": row[3],
                "indo_text": row[4],
                "read_text": row[5],
                "page": row[6],
                "surah_name": row[7],
                "is_sajadah": row[8],
                "juz_id": row[9]
            }
            verses.append(verse)

        return verses

    except Exception as e:
        print(f"Error connecting to database: {e}")
        print("Using sample data as fallback")
        return SAMPLE_VERSES

@app.get("/")
def read_root():
    return {"message": "Welcome to Surah Maryam API. Use /maryam endpoint to get all verses."}

@app.get("/maryam")
def get_maryam():
    """
    Endpoint to get all verses from Surah Maryam
    """
    verses = get_maryam_verses()

    # Since we're using fallback data, verses will never be None
    is_fallback = len(verses) <= len(SAMPLE_VERSES)

    return JSONResponse(content={
        "surah_name": "Maryam",
        "surah_id": 19,
        "total_verses": len(verses),
        "verses": verses,
        "is_fallback_data": is_fallback,
        "status": "Using fallback data" if is_fallback else "Using database data"
    })

if __name__ == "__main__":
    uvicorn.run("maryam_api:app", host="0.0.0.0", port=port)
