# Panduan Deployment ke Railway

Dokumen ini berisi langkah-langkah untuk men-deploy aplikasi Python FastAPI ke platform Railway.

## Persiapan File

Sebelum melakukan deployment, pastikan Anda memiliki file-file berikut:

1. **requirements.txt** - Berisi daftar package yang dibutuhkan
   ```
   fastapi==0.104.1
   uvicorn==0.23.2
   psycopg2-binary==2.9.9
   python-dotenv==1.0.0
   ```

2. **Procfile** - Berisi perintah untuk menjalankan aplikasi
   ```
   web: uvicorn maryam_api:app --host=0.0.0.0 --port=$PORT
   ```

3. **File aplikasi utama** (maryam_api.py)

## Langkah-langkah Deployment

### 1. Buat Akun Railway

1. Kunjungi [Railway.app](https://railway.app/)
2. Daftar menggunakan GitHub atau email
3. Login ke akun Anda

### 2. Instal Railway CLI (Command Line Interface)

Buka terminal dan jalankan perintah:

- Untuk macOS:
  ```
  brew install railway
  ```

- Untuk Windows/Linux:
  ```
  npm i -g @railway/cli
  ```

### 3. Login ke Railway dari CLI

```
railway login
```

Perintah ini akan membuka browser untuk autentikasi.

### 4. Buat Project Baru di Railway

1. Buka dashboard Railway di browser
2. Klik tombol "New Project"
3. Pilih "Deploy from GitHub" atau "Empty Project"

### 5. Hubungkan Project Lokal dengan Project Railway

Di terminal, jalankan:

```
railway link
```

Pilih project yang baru saja Anda buat.

### 6. Atur Variabel Lingkungan (Environment Variables)

Di dashboard Railway:
1. Buka project Anda
2. Klik tab "Variables"
3. Tambahkan variabel berikut:
   - `DB_HOST` = [host database Supabase]
   - `DB_PORT` = 5432
   - `DB_NAME` = [nama database]
   - `DB_USER` = [username database]
   - `DB_PASSWORD` = [password database]
   - `PORT` = 8000

### 7. Deploy Aplikasi

Di terminal, jalankan:

```
railway up
```

Perintah ini akan mengupload kode Anda, membangun aplikasi, dan men-deploy-nya.

### 8. Lihat Status Deployment

```
railway status
```

### 9. Buka Aplikasi yang Sudah Di-deploy

Di dashboard Railway:
1. Klik tab "Settings"
2. Cari bagian "Domains"
3. Klik URL yang disediakan untuk membuka aplikasi Anda

Atau gunakan perintah:

```
railway open
```

### 10. Melihat Log Aplikasi

Untuk melihat log aplikasi yang berjalan:

```
railway logs
```

## Troubleshooting

### Masalah Koneksi Database

Jika aplikasi tidak dapat terhubung ke database, periksa:

1. Variabel lingkungan sudah benar
2. Firewall database mengizinkan koneksi dari Railway
3. Pastikan menggunakan koneksi IPv4 (bukan IPv6)

### Aplikasi Error saat Deployment

1. Periksa log dengan `railway logs`
2. Pastikan semua dependensi tercantum di requirements.txt
3. Verifikasi Procfile sudah benar

## Tips Tambahan

1. **Gunakan Fallback Data**: Implementasikan mekanisme fallback jika database tidak dapat diakses
2. **Set Timeout Koneksi**: Tambahkan timeout pada koneksi database untuk menghindari aplikasi hang
3. **Gunakan Variabel Lingkungan**: Jangan hardcode kredensial atau konfigurasi sensitif
4. **Aktifkan CORS**: Jika API akan diakses dari domain lain

## Contoh Kode untuk Koneksi Database yang Robust

```python
def get_database_connection():
    try:
        # Coba koneksi dengan parameter individual
        if db_host and db_name and db_user and db_password:
            conn = psycopg2.connect(
                host=db_host,
                port=db_port,
                dbname=db_name,
                user=db_user,
                password=db_password,
                connect_timeout=5
            )
            return conn
        # Fallback ke connection string
        elif connection_string:
            conn = psycopg2.connect(connection_string + " connect_timeout=5")
            return conn
        else:
            print("No database connection parameters provided")
            return None
    except Exception as e:
        print(f"Error connecting to database: {e}")
        return None
```

## Referensi

- [Dokumentasi Railway](https://docs.railway.app/)
- [Dokumentasi FastAPI](https://fastapi.tiangolo.com/)
- [Dokumentasi Supabase](https://supabase.io/docs)
