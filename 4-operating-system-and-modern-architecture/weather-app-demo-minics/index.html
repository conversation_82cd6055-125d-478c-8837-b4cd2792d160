<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather App</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            transition: background-image 0.5s ease;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        .app-title {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .search-container {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .search-box {
            display: flex;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 30px;
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .search-input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: none;
            outline: none;
            font-size: 1rem;
            background: transparent;
        }

        .search-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .search-button:hover {
            background-color: #357abd;
        }

        .weather-container {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .city-name {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .weather-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #4a90e2;
        }

        .temperature {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .description {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #555;
        }

        .details {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .detail {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .detail-label {
            font-size: 0.9rem;
            color: #777;
            margin-bottom: 0.5rem;
        }

        .detail-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .forecast {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .forecast-day {
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 15px;
            padding: 1rem;
            flex: 1;
            min-width: 150px;
            margin: 0.5rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .forecast-date {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .forecast-temp {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .forecast-wind {
            color: #555;
            font-size: 0.9rem;
        }

        .error-message {
            background-color: rgba(255, 255, 255, 0.8);
            color: #e74c3c;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 1rem;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin: 2rem 0;
        }

        .loading::after {
            content: "...";
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: "."; }
            40% { content: ".."; }
            60%, 100% { content: "..."; }
        }

        .photo-credit {
            position: absolute;
            bottom: 10px;
            right: 10px;
            color: white;
            font-size: 0.8rem;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            background-color: rgba(0, 0, 0, 0.5);
            padding: 5px 10px;
            border-radius: 5px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .weather-container {
                padding: 1.5rem;
            }

            .details {
                flex-direction: column;
                gap: 1rem;
            }

            .forecast {
                flex-direction: column;
            }

            .forecast-day {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="app-title">Weather App</h1>

        <div class="search-container">
            <div class="search-box">
                <input type="text" class="search-input" placeholder="Enter city name..." id="city-input" value="Jakarta">
                <button class="search-button" id="search-button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <div style="text-align: center; color: white; font-size: 0.8rem; margin-top: 0.5rem; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);">
                Note: If weather data doesn't load, try <a href="https://cors-anywhere.herokuapp.com/corsdemo" target="_blank" style="color: white; text-decoration: underline;">enabling CORS-Anywhere</a>
            </div>
        </div>

        <div class="loading" id="loading">Loading</div>

        <div class="error-message" id="error-message"></div>

        <div class="weather-container" id="weather-container">
            <h2 class="city-name" id="city-name">Jakarta</h2>

            <div class="weather-icon" id="weather-icon">
                <i class="fas fa-cloud-sun"></i>
            </div>

            <div class="temperature" id="temperature">--°C</div>

            <div class="description" id="description">--</div>

            <div class="details">
                <div class="detail">
                    <div class="detail-label">Wind</div>
                    <div class="detail-value" id="wind">-- km/h</div>
                </div>
            </div>

            <div class="forecast" id="forecast">
                <!-- Forecast days will be added here dynamically -->
            </div>
        </div>
    </div>

    <div class="photo-credit" id="photo-credit"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const cityInput = document.getElementById('city-input');
            const searchButton = document.getElementById('search-button');
            const cityName = document.getElementById('city-name');
            const temperature = document.getElementById('temperature');
            const description = document.getElementById('description');
            const wind = document.getElementById('wind');
            const weatherIcon = document.getElementById('weather-icon');
            const forecast = document.getElementById('forecast');
            const errorMessage = document.getElementById('error-message');
            const loading = document.getElementById('loading');
            const weatherContainer = document.getElementById('weather-container');
            const photoCredit = document.getElementById('photo-credit');

            // Weather icon mapping
            const weatherIcons = {
                'sunny': 'fa-sun',
                'clear': 'fa-sun',
                'partly cloudy': 'fa-cloud-sun',
                'cloudy': 'fa-cloud',
                'overcast': 'fa-cloud',
                'rain': 'fa-cloud-rain',
                'light rain': 'fa-cloud-rain',
                'moderate rain': 'fa-cloud-rain',
                'heavy rain': 'fa-cloud-showers-heavy',
                'patchy rain': 'fa-cloud-rain',
                'patchy rain nearby': 'fa-cloud-sun-rain',
                'thunderstorm': 'fa-bolt',
                'snow': 'fa-snowflake',
                'mist': 'fa-smog',
                'fog': 'fa-smog'
            };

            // Function to get weather icon
            function getWeatherIcon(weatherDesc) {
                const desc = weatherDesc.toLowerCase();
                for (const key in weatherIcons) {
                    if (desc.includes(key)) {
                        return weatherIcons[key];
                    }
                }
                return 'fa-cloud'; // Default icon
            }

            // Function to fetch weather data
            async function getWeatherData(city) {
                try {
                    // Try direct fetch first (if running on http)
                    try {
                        const directResponse = await fetch(`http://goweather.xyz/weather/${city}`);
                        const directData = await directResponse.json();

                        if (directData.temperature && directData.temperature !== '') {
                            return directData;
                        }
                    } catch (directError) {
                        console.log('Direct fetch failed, trying CORS proxy...');
                    }

                    // If direct fetch fails, try with CORS proxy
                    // Note: CORS Anywhere might require visiting https://cors-anywhere.herokuapp.com/corsdemo
                    // and requesting temporary access to the demo server
                    const response = await fetch(`https://cors-anywhere.herokuapp.com/http://goweather.xyz/weather/${city}`);
                    const data = await response.json();

                    if (!data.temperature || data.temperature === '') {
                        throw new Error('Weather data not available for this city');
                    }

                    return data;
                } catch (error) {
                    // If all methods fail, return mock data for demonstration
                    if (city.toLowerCase() === 'jakarta') {
                        return {
                            temperature: "+28 °C",
                            wind: "9 km/h",
                            description: "Patchy rain nearby",
                            forecast: [
                                {day: "1", temperature: "32 °C", wind: "9 km/h"},
                                {day: "2", temperature: "+28 °C", wind: "3 km/h"},
                                {day: "3", temperature: "+27 °C", wind: "5 km/h"}
                            ]
                        };
                    }
                    throw new Error('Weather data not available for this city');
                }
            }

            // Function to fetch city image from Pexels
            async function getCityImage(city) {
                try {
                    const response = await fetch(`https://api.pexels.com/v1/search?query=${city}&per_page=1`, {
                        headers: {
                            'Authorization': 'SnghtfeJnjSnlafamyNcYD8eTt7S9J2ZHLeMLkKUrddOG9doWhGNL2Yv'
                        }
                    });

                    const data = await response.json();

                    if (data.photos && data.photos.length > 0) {
                        return {
                            url: data.photos[0].src.landscape,
                            photographer: data.photos[0].photographer,
                            photographerUrl: data.photos[0].photographer_url
                        };
                    } else {
                        // If no specific city image, try to get a generic cityscape
                        const fallbackResponse = await fetch(`https://api.pexels.com/v1/search?query=cityscape&per_page=1`, {
                            headers: {
                                'Authorization': 'SnghtfeJnjSnlafamyNcYD8eTt7S9J2ZHLeMLkKUrddOG9doWhGNL2Yv'
                            }
                        });

                        const fallbackData = await fallbackResponse.json();

                        if (fallbackData.photos && fallbackData.photos.length > 0) {
                            return {
                                url: fallbackData.photos[0].src.landscape,
                                photographer: fallbackData.photos[0].photographer,
                                photographerUrl: fallbackData.photos[0].photographer_url
                            };
                        }
                    }

                    throw new Error('No image found');
                } catch (error) {
                    throw error;
                }
            }

            // Function to update the UI with weather data
            function updateWeatherUI(data, city) {
                cityName.textContent = city;
                temperature.textContent = data.temperature;
                description.textContent = data.description || 'No description available';
                wind.textContent = data.wind || 'No wind data';

                // Update weather icon
                const iconClass = getWeatherIcon(data.description || '');
                weatherIcon.innerHTML = `<i class="fas ${iconClass}"></i>`;

                // Clear previous forecast
                forecast.innerHTML = '';

                // Add forecast days
                if (data.forecast && data.forecast.length > 0) {
                    data.forecast.forEach(day => {
                        if (day.temperature && day.temperature !== ' °C') {
                            const forecastDay = document.createElement('div');
                            forecastDay.className = 'forecast-day';

                            const dayNumber = parseInt(day.day);
                            const today = new Date();
                            const forecastDate = new Date();
                            forecastDate.setDate(today.getDate() + dayNumber);

                            forecastDay.innerHTML = `
                                <div class="forecast-date">Day ${day.day}</div>
                                <div class="forecast-temp">${day.temperature}</div>
                                <div class="forecast-wind">${day.wind}</div>
                            `;

                            forecast.appendChild(forecastDay);
                        }
                    });
                } else {
                    const noForecast = document.createElement('div');
                    noForecast.className = 'forecast-day';
                    noForecast.innerHTML = '<div class="forecast-date">No forecast data available</div>';
                    forecast.appendChild(noForecast);
                }

                weatherContainer.style.display = 'block';
            }

            // Function to search for weather
            async function searchWeather() {
                const city = cityInput.value.trim();

                if (!city) {
                    showError('Please enter a city name');
                    return;
                }

                // Show loading
                loading.style.display = 'block';
                errorMessage.style.display = 'none';
                weatherContainer.style.display = 'none';

                try {
                    // Get weather data and city image in parallel
                    const [weatherData, imageData] = await Promise.all([
                        getWeatherData(city),
                        getCityImage(city)
                    ]);

                    // Update UI with weather data
                    updateWeatherUI(weatherData, city);

                    // Update background image
                    if (imageData) {
                        document.body.style.backgroundImage = `url('${imageData.url}')`;
                        photoCredit.innerHTML = `Photo by <a href="${imageData.photographerUrl}" target="_blank" style="color: white; text-decoration: underline;">${imageData.photographer}</a> on Pexels`;
                        photoCredit.style.display = 'block';
                    }

                } catch (error) {
                    showError(error.message || 'Failed to fetch weather data');
                } finally {
                    loading.style.display = 'none';
                }
            }

            // Function to show error message
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                weatherContainer.style.display = 'none';
                loading.style.display = 'none';
            }

            // Event listeners
            searchButton.addEventListener('click', searchWeather);

            cityInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchWeather();
                }
            });

            // Initial search with default city (Jakarta)
            searchWeather();
        });
    </script>
</body>
</html>
