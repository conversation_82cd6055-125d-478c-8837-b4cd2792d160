<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            min-height: 100vh;
            transition: background-image 1s ease-in-out;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            background-image: url('https://images.pexels.com/photos/1105766/pexels-photo-1105766.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200');
            background-color: #1e213a;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: -1;
        }

        .container {
            width: 100%;
            max-width: 800px;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        .search-container {
            display: flex;
            margin-bottom: 30px;
        }

        .search-container input {
            flex: 1;
            padding: 15px;
            border: none;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 10px 0 0 10px;
            color: white;
            font-size: 16px;
            outline: none;
        }

        .search-container input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-container button {
            padding: 15px 20px;
            border: none;
            background-color: #4a90e2;
            color: white;
            border-radius: 0 10px 10px 0;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .search-container button:hover {
            background-color: #357abd;
        }

        .weather-info {
            text-align: center;
            margin-bottom: 30px;
        }

        .city {
            font-size: 36px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .temperature {
            font-size: 64px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .description {
            font-size: 24px;
            margin-bottom: 20px;
            text-transform: capitalize;
        }

        .details {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
        }

        .detail {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .detail-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 5px;
        }

        .detail-value {
            font-size: 18px;
            font-weight: 600;
        }

        .forecast {
            display: flex;
            justify-content: space-between;
            gap: 15px;
        }

        .forecast-day {
            flex: 1;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: transform 0.3s;
        }

        .forecast-day:hover {
            transform: translateY(-5px);
            background-color: rgba(255, 255, 255, 0.2);
        }

        .forecast-date {
            font-size: 16px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .forecast-temp {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .forecast-wind {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            display: none;
            color: #ff6b6b;
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background-color: rgba(255, 0, 0, 0.1);
            border-radius: 10px;
        }

        .attribution {
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .attribution a {
            color: #4a90e2;
            text-decoration: none;
        }

        .attribution a:hover {
            text-decoration: underline;
        }

        .cors-notice {
            margin-top: 10px;
            text-align: center;
            font-size: 12px;
            color: #ff9800;
            background-color: rgba(255, 152, 0, 0.1);
            padding: 5px 10px;
            border-radius: 5px;
            display: none;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }

            .city {
                font-size: 28px;
            }

            .temperature {
                font-size: 48px;
            }

            .description {
                font-size: 20px;
            }

            .forecast {
                flex-direction: column;
            }

            .forecast-day {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="search-container">
            <input type="text" id="city-input" placeholder="Enter city name..." value="Jakarta">
            <button id="search-btn">Search</button>
        </div>

        <div class="loading">
            <div class="loading-spinner"></div>
            <p>Loading weather data...</p>
        </div>

        <div class="error" id="error-message">
            City not found. Please try another city.
        </div>

        <div class="cors-notice" id="cors-notice">
            If weather data doesn't load, please <a href="https://cors-anywhere.herokuapp.com/corsdemo" target="_blank">click here</a> to request temporary access to the CORS proxy.
        </div>

        <div class="weather-info">
            <h1 class="city" id="city">Jakarta</h1>
            <div class="temperature" id="temperature">15 °C</div>
            <div class="description" id="description">Sunny</div>

            <div class="details">
                <div class="detail">
                    <div class="detail-label">WIND</div>
                    <div class="detail-value" id="wind">5 km/h</div>
                </div>
            </div>
        </div>

        <div class="forecast" id="forecast">
            <!-- Forecast days will be added here by JavaScript -->
        </div>

        <div class="attribution">
            <p>Weather data provided by <a href="http://goweather.xyz" target="_blank">goweather.xyz</a></p>
            <p>Images provided by <a href="https://www.pexels.com" target="_blank">Pexels</a></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const cityInput = document.getElementById('city-input');
            const searchBtn = document.getElementById('search-btn');
            const cityElement = document.getElementById('city');
            const temperatureElement = document.getElementById('temperature');
            const descriptionElement = document.getElementById('description');
            const windElement = document.getElementById('wind');
            const forecastElement = document.getElementById('forecast');
            const loadingElement = document.querySelector('.loading');
            const errorElement = document.getElementById('error-message');
            const corsNoticeElement = document.getElementById('cors-notice');

            const pexelsApiKey = 'SnghtfeJnjSnlafamyNcYD8eTt7S9J2ZHLeMLkKUrddOG9doWhGNL2Yv';

            // Initial load
            getWeatherData('Jakarta');
            
            // Search button click event
            searchBtn.addEventListener('click', function() {
                const city = cityInput.value.trim();
                if (city) {
                    getWeatherData(city);
                }
            });
            
            // Enter key press event
            cityInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const city = cityInput.value.trim();
                    if (city) {
                        getWeatherData(city);
                    }
                }
            });
            
            // Function to get weather data
            function getWeatherData(city) {
                // Show loading
                loadingElement.style.display = 'block';
                errorElement.style.display = 'none';
                corsNoticeElement.style.display = 'none';
                
                // Note: CORS proxy might require visiting https://cors-anywhere.herokuapp.com/
                // and requesting temporary access to the demo server
                
                // Try with CORS proxy first
                fetch(`https://cors-anywhere.herokuapp.com/https://goweather.xyz/weather/${city}`)
                    .catch(error => {
                        console.error('CORS proxy failed, trying direct API call:', error);
                        // If CORS proxy fails, try direct API call
                        return fetch(`https://goweather.xyz/weather/${city}`);
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('City not found or API error');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Hide loading
                        loadingElement.style.display = 'none';
                        
                        // Check if we have valid data
                        if (!data.temperature && !data.description) {
                            throw new Error('No weather data available for this city');
                        }
                        
                        // Update UI with weather data
                        cityElement.textContent = city;
                        temperatureElement.textContent = data.temperature || 'N/A';
                        descriptionElement.textContent = data.description || 'Unknown';
                        windElement.textContent = data.wind || 'N/A';
                        
                        // Update forecast
                        if (data.forecast && Array.isArray(data.forecast)) {
                            updateForecast(data.forecast);
                        } else {
                            updateForecast([]);
                        }
                        
                        // Get background image - search for landscape/cityscape of the city
                        getBackgroundImage(city);
                    })
                    .catch(error => {
                        // Hide loading and show error
                        loadingElement.style.display = 'none';
                        errorElement.style.display = 'block';
                        errorElement.textContent = `Error: ${error.message || 'Could not fetch weather data'}`;
                        console.error('Error fetching weather data:', error);
                        
                        // Show CORS notice if it might be a CORS issue
                        if (error.message && (error.message.includes('CORS') || error.message.includes('Failed to fetch'))) {
                            corsNoticeElement.style.display = 'block';
                        }
                        
                        // Still try to get a background image for the city
                        getBackgroundImage(city);
                    });
            }
            
            // Function to update forecast
            function updateForecast(forecast) {
                forecastElement.innerHTML = '';
                
                // If no forecast data or empty array, show placeholder
                if (!forecast || forecast.length === 0) {
                    for (let i = 1; i <= 3; i++) {
                        const currentDate = new Date();
                        const forecastDate = new Date(currentDate);
                        forecastDate.setDate(currentDate.getDate() + i);
                        
                        // Format date as "Mon, 01"
                        const formattedDate = forecastDate.toLocaleDateString('en-US', {
                            weekday: 'short',
                            day: '2-digit'
                        });
                        
                        const forecastDay = document.createElement('div');
                        forecastDay.className = 'forecast-day';
                        forecastDay.innerHTML = `
                            <div class="forecast-date">${formattedDate}</div>
                            <div class="forecast-temp">N/A</div>
                            <div class="forecast-wind">N/A</div>
                        `;
                        
                        forecastElement.appendChild(forecastDay);
                    }
                    return;
                }
                
                // Get current date
                const currentDate = new Date();
                
                forecast.forEach((day, index) => {
                    // Calculate date for this forecast day
                    const forecastDate = new Date(currentDate);
                    forecastDate.setDate(currentDate.getDate() + parseInt(day.day || (index + 1)));
                    
                    // Format date as "Mon, 01"
                    const formattedDate = forecastDate.toLocaleDateString('en-US', {
                        weekday: 'short',
                        day: '2-digit'
                    });
                    
                    const forecastDay = document.createElement('div');
                    forecastDay.className = 'forecast-day';
                    forecastDay.innerHTML = `
                        <div class="forecast-date">${formattedDate}</div>
                        <div class="forecast-temp">${day.temperature || 'N/A'}</div>
                        <div class="forecast-wind">${day.wind || 'N/A'}</div>
                    `;
                    
                    forecastElement.appendChild(forecastDay);
                });
            }
            
            // Function to get background image from Pexels
            function getBackgroundImage(city) {
                // Search for landscape/cityscape images of the city
                const query = `${city} landscape cityscape`;
                
                fetch(`https://api.pexels.com/v1/search?query=${encodeURIComponent(query)}&per_page=3&orientation=landscape`, {
                    headers: {
                        'Authorization': pexelsApiKey
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Pexels API error');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.photos && data.photos.length > 0) {
                        // Get a random image from the results
                        const randomIndex = Math.floor(Math.random() * Math.min(data.photos.length, 3));
                        const imageUrl = data.photos[randomIndex].src.landscape;
                        document.body.style.backgroundImage = `url(${imageUrl})`;
                        
                        // Clear any error messages about images
                        if (errorElement.textContent.includes('image data')) {
                            errorElement.style.display = 'none';
                        }
                    } else {
                        // Fallback to just city name if no results for landscape
                        getBackgroundImageFallback(city);
                    }
                })
                .catch(error => {
                    console.error('Error fetching background image:', error);
                    getBackgroundImageFallback(city);
                });
            }
            
            // Fallback function for background image
            function getBackgroundImageFallback(city) {
                // Try with just the city name
                fetch(`https://api.pexels.com/v1/search?query=${encodeURIComponent(city)}&per_page=3&orientation=landscape`, {
                    headers: {
                        'Authorization': pexelsApiKey
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Pexels API error');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.photos && data.photos.length > 0) {
                        // Get a random image from the results
                        const randomIndex = Math.floor(Math.random() * Math.min(data.photos.length, 3));
                        const imageUrl = data.photos[randomIndex].src.landscape;
                        document.body.style.backgroundImage = `url(${imageUrl})`;
                    } else {
                        // Show error message that no images were found for this city
                        if (!errorElement.style.display || errorElement.style.display === 'none') {
                            errorElement.style.display = 'block';
                            errorElement.textContent = `No image data found for "${city}". Please try another city.`;
                        } else if (!errorElement.textContent.includes('weather')) {
                            // Only update if the current error is not about weather
                            errorElement.textContent = `No image data found for "${city}". Please try another city.`;
                        }
                        
                        // Keep the current background if there is one, or use a default background
                        if (!document.body.style.backgroundImage || document.body.style.backgroundImage === 'none') {
                            // Use a default background image
                            document.body.style.backgroundImage = 'url("https://images.pexels.com/photos/1105766/pexels-photo-1105766.jpeg?auto=compress&cs=tinysrgb&fit=crop&h=627&w=1200")';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching fallback background image:', error);
                    if (!errorElement.style.display || errorElement.style.display === 'none') {
                        errorElement.style.display = 'block';
                        errorElement.textContent = `Error loading image data. Please try again later.`;
                    }
                });
            }
        });
    </script>
</body>
</html>
