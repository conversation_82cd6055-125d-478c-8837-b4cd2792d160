<!doctype html>
<html lang="id">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title><PERSON><PERSON><PERSON><PERSON> CORS (Cross-Origin Resource Sharing)</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family: "Inter", sans-serif;
                background-color: #f0f4f8;
            }
            .threat-card {
                background-color: white;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transition:
                    transform 0.3s ease,
                    box-shadow 0.3s ease;
                overflow: hidden;
            }
            .threat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }
            .simulation-area {
                border: 2px dashed #cbd5e1;
                border-radius: 8px;
                padding: 20px;
                min-height: 300px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                text-align: center;
                background-color: #f9fafb;
                position: relative;
            }
            .btn {
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: 500;
                transition:
                    background-color 0.3s ease,
                    transform 0.2s ease;
                cursor: pointer;
            }
            .btn-primary {
                background-color: #3b82f6;
                color: white;
            }
            .btn-primary:hover {
                background-color: #2563eb;
                transform: translateY(-2px);
            }
            .btn-danger {
                background-color: #ef4444;
                color: white;
            }
            .btn-danger:hover {
                background-color: #dc2626;
                transform: translateY(-2px);
            }
            .btn-success {
                background-color: #10b981;
                color: white;
            }
            .btn-success:hover {
                background-color: #059669;
                transform: translateY(-2px);
            }
            .icon {
                font-size: 3rem;
                margin-bottom: 10px;
            }
            .message-box {
                padding: 12px;
                border-radius: 8px;
                margin-top: 15px;
                font-size: 0.9rem;
                text-align: center;
                max-width: 90%;
            }
            .message-box code {
                background-color: #e0e0e0;
                padding: 2px 4px;
                border-radius: 4px;
                font-family: monospace;
            }
            .message-success {
                background-color: #d1fae5;
                color: #065f46;
                border: 1px solid #6ee7b7;
            }
            .message-error {
                background-color: #fee2e2;
                color: #991b1b;
                border: 1px solid #fca5a5;
            }
            .message-warning {
                background-color: #ffedd5;
                color: #9a3412;
                border: 1px solid #fdba74;
            }
            .message-info {
                background-color: #e0e7ff;
                color: #3730a3;
                border: 1px solid #a5b4fc;
            }
            .server-icon {
                position: relative;
                width: 80px;
                height: 100px;
                background-color: #9ca3af;
                border-radius: 8px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-around;
                padding: 10px 0;
                border: 2px solid #4b5563;
            }
            .server-light {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: #22c55e;
                margin: 2px;
            }
            .server-label {
                position: absolute;
                bottom: -25px;
                font-size: 0.8rem;
                font-weight: 600;
                width: 130px;
                text-align: center;
            }
            .browser-icon {
                position: relative;
                width: 100px;
                height: 80px;
                background-color: #f3f4f6;
                border-radius: 8px 8px 0 0;
                border: 2px solid #4b5563;
                display: flex;
                flex-direction: column;
            }
            .browser-icon .header {
                height: 20px;
                background-color: #d1d5db;
                border-radius: 6px 6px 0 0;
                display: flex;
                align-items: center;
                padding: 0 5px;
            }
            .browser-icon .header .circle {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #9ca3af;
                margin-right: 3px;
            }
            .browser-icon .body {
                flex-grow: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #4b5563;
                font-size: 0.7rem;
                padding: 5px;
                text-align: center;
            }
            .cors-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            width: 100%;
            max-width: 700px;
            position: relative;
            margin-top: 50px;
            flex-wrap: wrap;
        }
        .origin-tag {
        position: absolute;
        top: -35px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #e0e7ff;
        border: 2px solid #3b82f6;
        border-radius: 6px;
        padding: 4px 8px;
        font-size: 0.75rem;
        font-weight: 600;
        color: #1e40af;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        z-index: 5;
        white-space: nowrap;
    }
            .cors-node {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                margin: 0 20px;
                margin-bottom: 30px;
            }
            .request-packet {
                position: absolute;
                padding: 6px 8px;
                border-radius: 6px;
                font-size: 0.75rem;
                opacity: 0;
                transition: all 2.5s linear;
                z-index: 10;
                background-color: #60a5fa;
                color: white;
                max-width: 180px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                white-space: nowrap;
            }
            .response-packet {
                position: absolute;
                padding: 6px 8px;
                border-radius: 6px;
                font-size: 0.75rem;
                opacity: 0;
                transition: all 2.5s linear;
                z-index: 10;
                background-color: #a3e635;
                color: #365314;
                max-width: 180px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                white-space: nowrap;
            }
            .rejected-packet {
                background-color: #f87171;
                color: white;
            }
            .options-packet {
                background-color: #c084fc;
                color: white;
            }
            .cors-details {
                margin-top: 20px;
                padding: 15px;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                width: 100%;
                max-width: 700px;
            }
            .request-headers, .response-headers {
                font-family: monospace;
                background-color: #f1f5f9;
                padding: 10px;
                border-radius: 6px;
                margin-top: 10px;
                text-align: left;
                font-size: 0.85rem;
                overflow-x: auto;
            }
            .header-highlight {
                color: #2563eb;
                font-weight: bold;
            }
            .allowed-domain {
                background-color: #ecfdf5;
                border: 1px solid #6ee7b7;
                padding: 3px 6px;
                border-radius: 4px;
                margin: 2px;
                display: inline-block;
                font-size: 0.8rem;
            }
            .domain-selector {
                margin-bottom: 15px;
            }
            .info-tooltip {
                display: inline-block;
                background-color: #3b82f6;
                color: white;
                width: 18px;
                height: 18px;
                border-radius: 50%;
                text-align: center;
                line-height: 18px;
                font-size: 12px;
                font-weight: bold;
                cursor: help;
                position: relative;
                margin-left: 5px;
            }
            .info-tooltip:hover::after {
                content: attr(data-tooltip);
                position: absolute;
                background-color: #1e40af;
                color: white;
                padding: 5px 10px;
                border-radius: 6px;
                width: 200px;
                z-index: 100;
                top: -5px;
                left: 25px;
                font-size: 0.8rem;
                font-weight: normal;
                text-align: left;
            }
            @media (max-width: 640px) {
                .cors-container {
                    flex-direction: column;
                }
            }
        </style>
    </head>
    <body class="p-4">
        <header class="max-w-4xl mx-auto mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                Simulasi CORS (Cross-Origin Resource Sharing)
            </h1>
            <p class="text-gray-600">
                Protokol keamanan yang mengontrol akses sumber daya lintas domain pada browser
            </p>
        </header>

        <main class="max-w-4xl mx-auto">
            <div class="threat-card p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">
                    Penjelasan CORS
                </h2>
                <p class="mb-4">
                    CORS (Cross-Origin Resource Sharing) adalah mekanisme keamanan yang diterapkan oleh browser untuk membatasi permintaan HTTP yang dibuat dari satu asal (origin) ke asal yang berbeda. CORS memungkinkan server menentukan domain mana yang diizinkan untuk mengakses sumber dayanya.
                </p>
                <p class="mb-4">
                    Tanpa CORS, situs web hanya dapat meminta sumber daya dari asal yang sama karena kebijakan Same-Origin Policy (SOP) yang diterapkan browser. CORS memperluas SOP dengan menambahkan header HTTP yang memungkinkan server mengizinkan permintaan lintas asal tertentu.
                </p>
                <p class="mb-4">
                    <strong>Penting:</strong> Asal (origin) ditentukan oleh tiga komponen: protokol (http/https), domain, dan port. Misalnya, <code>https://app.example.com</code>. Browser secara otomatis menyertakan informasi ini dalam header <code>Origin</code> untuk setiap permintaan lintas-origin, bukan berdasarkan alamat IP pengguna. Origin adalah identitas dari halaman web yang membuat permintaan, bukan identitas dari perangkat pengguna.
                </p>
            </div>

            <div class="threat-card p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">
                    Simulasi Interaktif CORS
                </h2>
                
                <div class="domain-selector mb-6">
                <p class="mb-2 font-semibold">Pilih domain sumber permintaan:</p>
            <select id="request-domain" class="p-2 border rounded">
                <option value="https://app.example.com">https://app.example.com (Diizinkan)</option>
                <option value="https://evil.com">https://evil.com (Tidak Diizinkan)</option>
                <option value="https://partner.example.net">https://partner.example.net (Diizinkan)</option>
            </select>
            <span class="info-tooltip" data-tooltip="Domain ini akan menjadi asal (origin) permintaan yang dikirim ke API server. Nilai ini ditentukan berdasarkan URL halaman web tempat kode JavaScript dijalankan, bukan berdasarkan IP pengguna. Browser secara otomatis menambahkan header Origin ini untuk keamanan.">i</span>
            </div>
                
                <div class="mb-6">
                    <p class="mb-2 font-semibold">Jenis permintaan:</p>
                    <select id="request-type" class="p-2 border rounded">
                        <option value="simple">Simple Request (GET)</option>
                        <option value="preflight">Preflight Request (PUT/DELETE)</option>
                    </select>
                    <span class="info-tooltip" data-tooltip="Simple Request langsung dikirim, sedangkan Preflight Request akan mengirim OPTIONS terlebih dahulu untuk memeriksa apakah permintaan utama diizinkan">i</span>
                </div>
                
                <div class="simulation-area" id="cors-simulation">
                    <div class="cors-container">
                        <div class="cors-node">
                            <div class="browser-icon">
                                <div class="header">
                                    <div class="circle"></div>
                                    <div class="circle"></div>
                                    <div class="circle"></div>
                                </div>
                                <div class="body">
                                    Web Client
                                </div>
                                <div class="origin-tag" id="browser-origin">
                                    Origin: https://app.example.com
                                </div>
                            </div>
                            <span class="server-label">Browser Client</span>
                        </div>
                    
                        <div class="cors-node">
                            <div class="server-icon">
                                <div class="server-light"></div>
                                <div class="server-light"></div>
                                <div class="server-light"></div>
                            </div>
                            <span class="server-label">API Server (api.example.com)</span>
                        </div>
                    </div>
                    <div class="message-box message-info mt-4 mb-2">
                        <strong>Catatan:</strong> Origin adalah identitas situs web tempat kode JavaScript berjalan, bukan identitas browser atau IP pengguna. Browser secara otomatis mengirimkan Origin dalam header HTTP untuk permintaan lintas domain.
                    </div>
                    
                    <!-- Request Packets -->
                    <div id="request-packet" class="request-packet">
                        GET /api/data
                    </div>
                    <div id="preflight-packet" class="request-packet options-packet">
                        OPTIONS /api/data
                    </div>
                    
                    <!-- Response Packets -->
                    <div id="preflight-response" class="response-packet">
                        200 OK + CORS Headers
                    </div>
                    <div id="success-response" class="response-packet">
                        200 OK + Data
                    </div>
                    <div id="error-response" class="response-packet rejected-packet">
                        CORS Error
                    </div>
                    
                    <!-- Message Box for explanations -->
                    <div id="cors-message" class="message-box message-info" style="display: none"></div>
                </div>
                
                <div class="flex justify-center mt-4 space-x-4">
                    <button id="start-simulation" class="btn btn-primary">
                        Jalankan Simulasi
                    </button>
                    <button id="reset-simulation" class="btn btn-danger">
                        Reset
                    </button>
                </div>
                
                <div id="cors-details" class="cors-details mt-6" style="display: none">
                    <h3 class="font-bold text-lg mb-2">Detail CORS</h3>
                    <div class="mb-4">
                        <h4 class="font-semibold">Domain yang diizinkan server:</h4>
                        <div class="mt-1">
                            <span class="allowed-domain">https://app.example.com</span>
                            <span class="allowed-domain">https://partner.example.net</span>
                        </div>
                    </div>
                    
                    <div id="request-details" class="mb-4">
                        <h4 class="font-semibold">Request Headers:</h4>
                        <div id="request-headers" class="request-headers">
                            <!-- Filled by JavaScript -->
                        </div>
                    </div>
                    
                    <div id="response-details">
                        <h4 class="font-semibold">Response Headers:</h4>
                        <div id="response-headers" class="response-headers">
                            <!-- Filled by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="threat-card p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">
                    Bagaimana CORS Bekerja
                </h2>
                
                <div class="mb-4">
                    <h3 class="font-semibold text-lg mb-2">Simple Request</h3>
                    <p>
                        Permintaan HTTP yang memenuhi kriteria tertentu disebut sebagai Simple Request. Contohnya adalah permintaan GET, HEAD, atau POST dengan content-type tertentu. Browser langsung mengirim permintaan ini ke server, tetapi menambahkan header <code>Origin</code> yang menunjukkan asal permintaan.
                    </p>
                </div>
                
                <div class="mb-4">
                    <h3 class="font-semibold text-lg mb-2">Header Origin</h3>
                    <p>
                        Header <code>Origin</code> secara otomatis ditambahkan oleh browser pada setiap permintaan lintas-origin. Nilai header ini adalah skema (http/https), nama host (domain), dan port dari situs web tempat permintaan berasal, misalnya <code>https://app.example.com</code>. 
                    </p>
                    <p class="mt-2">
                        Perlu dipahami bahwa nilai Origin <strong>bukan</strong> berdasarkan IP pengguna, melainkan berdasarkan URL situs yang memuat dan menjalankan kode JavaScript yang membuat permintaan tersebut. Header ini tidak dapat dimanipulasi oleh JavaScript dan selalu ditetapkan oleh browser untuk alasan keamanan.
                    </p>
                </div>
                
                <div class="mb-4">
                    <h3 class="font-semibold text-lg mb-2">Preflight Request</h3>
                    <p>
                        Untuk permintaan yang lebih kompleks (seperti PUT, DELETE, atau dengan header kustom), browser terlebih dahulu mengirim permintaan OPTIONS (preflight) untuk memeriksa apakah server mengizinkan permintaan utama. Server harus merespons dengan header CORS yang tepat untuk mengizinkan permintaan utama.
                    </p>
                    <p class="mt-2">
                        Dalam permintaan preflight, browser juga menyertakan header <code>Origin</code> yang sama. Ini memungkinkan server mengevaluasi apakah domain asal diizinkan sebelum memproses permintaan utama. Header <code>Origin</code> dalam preflight request juga ditentukan otomatis oleh browser berdasarkan URL halaman web, bukan dari IP pengguna.
                    </p>
                </div>
                
                <div class="mb-4">
                    <h3 class="font-semibold text-lg mb-2">Header CORS Utama</h3>
                    <ul class="list-disc pl-6">
                        <li><code>Access-Control-Allow-Origin</code>: Menentukan domain yang diizinkan</li>
                        <li><code>Access-Control-Allow-Methods</code>: Metode HTTP yang diizinkan</li>
                        <li><code>Access-Control-Allow-Headers</code>: Header yang diizinkan</li>
                        <li><code>Access-Control-Allow-Credentials</code>: Apakah credentials (seperti cookies) diizinkan</li>
                    </ul>
                </div>
                
                <div class="mb-4">
                    <h3 class="font-semibold text-lg mb-2">Bagaimana Browser Menentukan Origin</h3>
                    <p>
                        <strong>Origin bukanlah browser client itu sendiri</strong>, melainkan identitas dari situs web yang diakses pengguna. Browser menentukan nilai <code>Origin</code> secara otomatis berdasarkan tiga komponen dari URL halaman web yang membuat permintaan:
                    </p>
                    <ul class="list-disc pl-6 mt-2">
                        <li><strong>Protokol</strong>: http:// atau https://</li>
                        <li><strong>Domain</strong>: nama domain situs (misalnya app.example.com)</li>
                        <li><strong>Port</strong>: nomor port jika ditentukan secara eksplisit (misalnya :8080)</li>
                    </ul>
                    <p class="mt-2">
                        Misalnya, jika pengguna mengakses halaman web di <code>https://app.example.com</code> dan JavaScript di halaman tersebut membuat permintaan lintas-origin ke <code>https://api.example.com/data</code>, browser akan otomatis menyertakan header <code>Origin: https://app.example.com</code> dalam permintaan.
                    </p>
                    <p class="mt-2">
                        <strong>Penting untuk diingat:</strong> 
                    </p>
                    <ul class="list-disc pl-6">
                        <li>Header Origin <strong>tidak berkaitan</strong> dengan alamat IP pengguna</li>
                        <li>Origin ditentukan oleh URL halaman web di mana JavaScript dijalankan</li>
                        <li>Header ini tidak dapat dimanipulasi oleh JavaScript (tidak seperti header kustom lainnya)</li>
                        <li>Nilai Origin otomatis ditambahkan oleh browser untuk keamanan</li>
                        <li>Bahkan jika pengguna memiliki alamat IP yang sama, jika mereka mengakses situs dari domain berbeda, Origin akan berbeda</li>
                        <li>Origin adalah <strong>situs web yang dikunjungi pengguna</strong>, bukan browser atau perangkat pengguna itu sendiri</li>
                    </ul>
                </div>
            </div>
        </main>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Elements
                const browserOrigin = document.getElementById('browser-origin');
                const requestPacket = document.getElementById('request-packet');
                const preflightPacket = document.getElementById('preflight-packet');
                const preflightResponse = document.getElementById('preflight-response');
                const successResponse = document.getElementById('success-response');
                const errorResponse = document.getElementById('error-response');
                const corsMessage = document.getElementById('cors-message');
                const startButton = document.getElementById('start-simulation');
                const resetButton = document.getElementById('reset-simulation');
                const requestDomain = document.getElementById('request-domain');
                const requestType = document.getElementById('request-type');
                const corsDetails = document.getElementById('cors-details');
                const requestHeaders = document.getElementById('request-headers');
                const responseHeaders = document.getElementById('response-headers');
                
                // State
                let simulationInProgress = false;
                const allowedDomains = ['https://app.example.com', 'https://partner.example.net'];
                
                // Update browser origin display when domain changes
                requestDomain.addEventListener('change', function() {
                    // Menampilkan domain lengkap dengan protokol di UI
                    browserOrigin.textContent = this.value;
                });
                
                // Start simulation
                startButton.addEventListener('click', simulateCORS);
                
                // Reset simulation
                resetButton.addEventListener('click', resetSimulation);
                
                function simulateCORS() {
                    if (simulationInProgress) return;
                    simulationInProgress = true;
                    
                    resetSimulation(false);
                    
                    const domain = requestDomain.value;
                    const type = requestType.value;
                    const isDomainAllowed = allowedDomains.includes(domain);
                    const isPreflight = type === 'preflight';
                    
                    browserOrigin.textContent = domain;
                    
                    // Show CORS details section
                    corsDetails.style.display = 'block';
                    
                    // Display appropriate request method
                    requestPacket.textContent = isPreflight ? 'PUT /api/data' : 'GET /api/data';
                    // Tampilkan domain lengkap dengan protokol
                    browserOrigin.textContent = domain;
                    
                    // Set up request headers
                    let reqHeaders = `Host: api.example.com\n`;
                    reqHeaders += `<span class="header-highlight">Origin: ${domain}</span>\n`;
                    
                    if (isPreflight) {
                        reqHeaders += `Access-Control-Request-Method: PUT\n`;
                        reqHeaders += `Access-Control-Request-Headers: Content-Type, X-Custom-Header`;
                    }
                    
                    requestHeaders.innerHTML = reqHeaders;
                    
                    // Position elements for animation
                    const container = document.querySelector('.cors-container');
                    const browserNode = document.querySelector('.cors-node:first-child');
                    const serverNode = document.querySelector('.cors-node:last-child');
                    
                    const browserRect = browserNode.getBoundingClientRect();
                    const serverRect = serverNode.getBoundingClientRect();
                    const containerRect = container.getBoundingClientRect();
                    
                    // Simulate the request flow
                    if (isPreflight) {
                        // Preflight request (OPTIONS)
                        simulatePreflightRequest(
                            browserRect, 
                            serverRect, 
                            containerRect, 
                            isDomainAllowed
                        );
                    } else {
                        // Simple request
                        simulateSimpleRequest(
                            browserRect, 
                            serverRect, 
                            containerRect, 
                            isDomainAllowed
                        );
                    }
                }
                
                function simulatePreflightRequest(browserRect, serverRect, containerRect, isDomainAllowed) {
                    // Position preflight packet
                    preflightPacket.style.top = `${browserRect.top - containerRect.top + 30}px`;
                    preflightPacket.style.left = `${browserRect.left - containerRect.left + 120}px`;
                    preflightPacket.style.opacity = '1';
                    
                    // Show CORS message
                    corsMessage.innerHTML = "Browser mengirim permintaan OPTIONS terlebih dahulu (preflight request) untuk memeriksa apakah server mengizinkan metode PUT dan header kustom";
                    corsMessage.className = "message-box message-info";
                    corsMessage.style.display = "block";
                    
                    // Animate preflight packet to server
                    setTimeout(() => {
                        preflightPacket.style.left = `${serverRect.left - containerRect.left - 20}px`;
                    }, 2000);
                    
                    setTimeout(() => {
                        preflightPacket.style.opacity = '0';
                        
                        // Prepare preflight response
                        preflightResponse.style.top = `${serverRect.top - containerRect.top + 30}px`;
                        preflightResponse.style.left = `${serverRect.left - containerRect.left - 20}px`;
                        
                        let respHeaders = '';
                        
                        if (isDomainAllowed) {
                            respHeaders = `HTTP/1.1 200 OK\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Origin: ${requestDomain.value}</span>\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Methods: GET, POST, PUT, DELETE</span>\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Headers: Content-Type, X-Custom-Header</span>\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Credentials: true</span>\n`;
                            respHeaders += `Vary: Origin`;
                            
                            // Show preflight success
                            preflightResponse.textContent = "200 OK + CORS Headers";
                            corsMessage.innerHTML = "Preflight berhasil! Server memeriksa Origin: " + requestDomain.value + " dan mengonfirmasi bahwa permintaan PUT diizinkan dari asal ini";
                            corsMessage.className = "message-box message-success";
                        } else {
                            respHeaders = `HTTP/1.1 200 OK\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Origin: app.example.com</span>\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Methods: GET, POST, PUT, DELETE</span>\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Headers: Content-Type, X-Custom-Header</span>\n`;
                            respHeaders += `Vary: Origin`;
                            
                            // Show preflight failure
                            preflightResponse.textContent = "CORS Error (Preflight)";
                            preflightResponse.className = "response-packet rejected-packet";
                            corsMessage.innerHTML = "Preflight gagal! Browser mengirim <code>Origin: " + requestDomain.value + "</code> tetapi origin ini tidak ada dalam daftar <code>Access-Control-Allow-Origin</code> yang diizinkan server";
                            corsMessage.className = "message-box message-error";
                        }
                        
                        responseHeaders.innerHTML = respHeaders;
                        preflightResponse.style.opacity = '1';
                        
                        // Animate preflight response back to browser
                        setTimeout(() => {
                            preflightResponse.style.left = `${browserRect.left - containerRect.left + 120}px`;
                        }, 1000);
                        
                        // Handle main request after preflight
                        setTimeout(() => {
                            preflightResponse.style.opacity = '0';
                            
                            if (isDomainAllowed) {
                                // If preflight successful, proceed with main request
                                simulateMainRequest(browserRect, serverRect, containerRect, true);
                            } else {
                                // If preflight failed, browser blocks the main request
                                corsMessage.innerHTML = "Browser membatalkan permintaan utama karena preflight gagal. Permintaan PUT tidak akan dikirim ke server.";
                                corsMessage.className = "message-box message-error";
                                simulationInProgress = false;
                            }
                        }, 3000);
                    }, 2000);
                }
                
                function simulateSimpleRequest(browserRect, serverRect, containerRect, isDomainAllowed) {
                    simulateMainRequest(browserRect, serverRect, containerRect, isDomainAllowed);
                }
                
                function simulateMainRequest(browserRect, serverRect, containerRect, isDomainAllowed) {
                    // Position request packet
                    requestPacket.style.top = `${browserRect.top - containerRect.top + 30}px`;
                    requestPacket.style.left = `${browserRect.left - containerRect.left + 120}px`;
                    requestPacket.style.opacity = '1';
                    
                    // Show appropriate message
                    if (requestType.value === 'simple') {
                        corsMessage.innerHTML = "Browser mengirim permintaan GET dengan header <code>Origin: " + requestDomain.value + "</code>. Header ini otomatis ditambahkan oleh browser berdasarkan URL halaman tempat JavaScript dijalankan. Origin adalah situs web yang dikunjungi pengguna, bukan browser atau IP pengguna.";
                        corsMessage.className = "message-box message-info";
                    } else {
                        corsMessage.innerHTML = "Setelah preflight berhasil, browser mengirim permintaan PUT yang sebenarnya dengan header <code>Origin: " + requestDomain.value + "</code>";
                        corsMessage.className = "message-box message-info";
                    }
                    corsMessage.style.display = "block";
                    
                    // Animate request packet to server
                    setTimeout(() => {
                        requestPacket.style.left = `${serverRect.left - containerRect.left - 20}px`;
                    }, 2000);
                    
                    setTimeout(() => {
                        requestPacket.style.opacity = '0';
                        
                        // Prepare response
                        if (isDomainAllowed) {
                            successResponse.style.top = `${serverRect.top - containerRect.top + 30}px`;
                            successResponse.style.left = `${serverRect.left - containerRect.left - 20}px`;
                            successResponse.style.opacity = '1';
                            
                            let respHeaders = `HTTP/1.1 200 OK\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Origin: ${requestDomain.value}</span>\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Credentials: true</span>\n`;
                            respHeaders += `Content-Type: application/json\n`;
                            respHeaders += `Vary: Origin`;
                            
                            responseHeaders.innerHTML = respHeaders;
                            
                            // Animate success response back to browser
                            setTimeout(() => {
                                successResponse.style.left = `${browserRect.left - containerRect.left + 120}px`;
                                corsMessage.innerHTML = "Permintaan berhasil! Server merespons dengan data dan header CORS yang benar";
                                corsMessage.className = "message-box message-success";
                            }, 1000);
                            
                            setTimeout(() => {
                                successResponse.style.opacity = '0';
                                simulationInProgress = false;
                            }, 3000);
                        } else {
                            errorResponse.style.top = `${serverRect.top - containerRect.top + 30}px`;
                            errorResponse.style.left = `${serverRect.left - containerRect.left - 20}px`;
                            errorResponse.style.opacity = '1';
                            
                            let respHeaders = `HTTP/1.1 200 OK\n`;
                            respHeaders += `<span class="header-highlight">Access-Control-Allow-Origin: app.example.com</span>\n`;
                            respHeaders += `Content-Type: application/json\n`;
                            respHeaders += `Vary: Origin`;
                            
                            responseHeaders.innerHTML = respHeaders;
                            
                            // Animate error response back to browser
                            setTimeout(() => {
                                errorResponse.style.left = `${browserRect.left - containerRect.left + 120}px`;
                                corsMessage.innerHTML = "Browser memblokir respons karena header <code>Access-Control-Allow-Origin</code> tidak cocok dengan asal permintaan. Server merespons, tetapi browser menolak untuk membagikan respons dengan JavaScript.";
                                corsMessage.className = "message-box message-error";
                            }, 1000);
                            
                            setTimeout(() => {
                                errorResponse.style.opacity = '0';
                                simulationInProgress = false;
                            }, 3000);
                        }
                    }, 2000);
                }
                
                function resetSimulation(resetProgress = true) {
                    // Hide all packets
                    requestPacket.style.opacity = '0';
                    preflightPacket.style.opacity = '0';
                    preflightResponse.style.opacity = '0';
                    successResponse.style.opacity = '0';
                    errorResponse.style.opacity = '0';
                    
                    // Reset styles
                    preflightResponse.className = "response-packet";
                    
                    // Hide message and details
                    corsMessage.style.display = 'none';
                    if (resetProgress) {
                        simulationInProgress = false;
                        corsDetails.style.display = 'none';
                    }
                }
            });
        </script>
    </body>
</html>