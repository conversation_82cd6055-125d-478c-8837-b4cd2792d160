<!doctype html>
<html lang="id">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Perangkat Keras <PERSON>aringan</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family: "Inter", sans-serif;
                background-color: #f8f9fa;
                color: #343a40;
            }
            .sim-container {
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);
                overflow: hidden;
                margin-bottom: 24px;
            }
            .sim-header {
                padding: 16px;
                display: flex;
                align-items: center;
            }
            .sim-title {
                font-weight: 600;
                font-size: 1.25rem;
                margin-left: 12px;
            }
            .sim-area {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
                background-color: #f8f9fa;
                position: relative;
                min-height: 200px;
                margin: 0 16px 16px;
                overflow: hidden;
            }
            .device-icon {
                font-size: 2.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background-color: #e7f5ff;
                color: #1864ab;
                flex-shrink: 0;
            }
            .network-device {
                position: absolute;
                width: 80px;
                height: 80px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                z-index: 10;
            }
            .network-device-label {
                font-size: 0.75rem;
                font-weight: 600;
                margin-top: 5px;
                background-color: white;
                padding: 2px 6px;
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }
            .network-device.active {
                animation: pulse 1s infinite alternate;
            }
            @keyframes pulse {
                0% {
                    transform: scale(1);
                }
                100% {
                    transform: scale(1.1);
                    box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
                }
            }
            .computer {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-color: #f1f3f5;
                color: #343a40;
                border: 1px solid #ced4da;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.25rem;
            }
            .connection-line {
                position: absolute;
                height: 3px;
                background-color: #adb5bd;
                transform-origin: left center;
                z-index: 5;
            }
            .connection-line.active {
                background-color: #3b82f6;
                height: 4px;
            }
            .data-packet {
                position: absolute;
                width: 12px;
                height: 8px;
                border-radius: 4px;
                background-color: #fd7e14;
                z-index: 15;
                transition: left 0.8s ease, top 0.8s ease;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            }
            .data-packet.blocked {
                background-color: #fa5252;
            }
            .explanation-box {
                padding: 12px 16px;
                border-radius: 8px;
                background-color: #e7f5ff;
                color: #1864ab;
                border: 1px solid #74c0fc;
                margin: 12px 16px 16px;
                font-size: 0.9rem;
                display: none;
            }
            .btn {
                display: inline-block;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: 500;
                background-color: #3b82f6;
                color: white;
                transition: all 0.3s ease;
                cursor: pointer;
                text-align: center;
                margin-top: 8px;
            }
            .btn:hover {
                background-color: #2563eb;
                transform: translateY(-2px);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
        </style>
    </head>
    <body class="p-4 md:p-8">
        <header class="text-center mb-10">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800">
                Perangkat Keras Jaringan Komputer
            </h1>
            <p class="text-gray-600 mt-2 max-w-3xl mx-auto">
                Berikut adalah simulasi interaktif yang menjelaskan berbagai perangkat keras jaringan
                dan bagaimana cara kerjanya. Klik tombol "Mulai Simulasi" untuk melihat visualisasi.
            </p>
        </header>

        <div class="max-w-5xl mx-auto">
            <!-- Router Simulation -->
            <div class="sim-container">
                <div class="sim-header bg-blue-100">
                    <div class="device-icon">🌐</div>
                    <div class="sim-title">Router</div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">
                        Router adalah perangkat yang menghubungkan dua atau lebih jaringan berbeda dan mengarahkan (routing) data antar jaringan tersebut. Router berperan sebagai "penunjuk jalan" bagi paket data untuk mencapai tujuannya.
                    </p>
                    
                    <div class="sim-area" id="router-sim">
                        <!-- Network devices will be positioned with JavaScript -->
                        <div class="network-device" id="router-device" style="top: 45%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="device-icon">🌐</div>
                            <div class="network-device-label">Router</div>
                        </div>
                        
                        <!-- Network A devices -->
                        <div class="network-device" id="network-a-pc1" style="top: 20%; left: 20%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">LAN A</div>
                        </div>
                        
                        <div class="network-device" id="network-a-pc2" style="top: 70%; left: 20%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">LAN A</div>
                        </div>
                        
                        <!-- Network B devices -->
                        <div class="network-device" id="network-b-pc1" style="top: 20%; left: 80%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">LAN B</div>
                        </div>
                        
                        <div class="network-device" id="network-b-pc2" style="top: 70%; left: 80%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">LAN B</div>
                        </div>
                        
                        <!-- Internet -->
                        <div class="network-device" id="internet-cloud" style="top: 45%; left: 110%;">
                            <div class="device-icon">☁️</div>
                            <div class="network-device-label">Internet</div>
                        </div>
                    </div>
                    
                    <div class="explanation-box" id="router-explanation">
                        Router menghubungkan jaringan lokal (LAN) ke Internet dan mengarahkan lalu lintas data ke tujuan yang benar.
                    </div>
                    
                    <div class="text-center mt-4">
                        <button id="router-btn" class="btn">
                            Mulai Simulasi
                        </button>
                    </div>
                </div>
            </div>

            <!-- Access Point Simulation -->
            <div class="sim-container">
                <div class="sim-header bg-green-100">
                    <div class="device-icon" style="background-color: #d3f9d8; color: #2b8a3e;">📶</div>
                    <div class="sim-title">Access Point</div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">
                        Access Point adalah perangkat yang memungkinkan perangkat nirkabel (wireless) terhubung ke jaringan kabel. Access Point membuat jaringan WiFi dan mengubah data antara format nirkabel dan kabel.
                    </p>
                    
                    <div class="sim-area" id="ap-sim">
                        <!-- Access Point device -->
                        <div class="network-device" id="ap-device" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="device-icon" style="background-color: #d3f9d8; color: #2b8a3e;">📶</div>
                            <div class="network-device-label">Access Point</div>
                        </div>
                        
                        <!-- Wireless devices -->
                        <div class="network-device" id="wireless-device1" style="top: 20%; left: 20%;">
                            <div class="computer">📱</div>
                            <div class="network-device-label">Smartphone</div>
                        </div>
                        
                        <div class="network-device" id="wireless-device2" style="top: 70%; left: 30%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">Laptop</div>
                        </div>
                        
                        <div class="network-device" id="wireless-device3" style="top: 25%; left: 70%;">
                            <div class="computer">📱</div>
                            <div class="network-device-label">Tablet</div>
                        </div>
                        
                        <!-- Router connection -->
                        <div class="network-device" id="router-connection" style="top: 75%; left: 80%;">
                            <div class="device-icon" style="font-size: 1.8rem;">🌐</div>
                            <div class="network-device-label">Ke Router</div>
                        </div>
                    </div>
                    
                    <div class="explanation-box" id="ap-explanation">
                        Access Point membuat jaringan WiFi dan memungkinkan perangkat seperti smartphone, laptop, dan tablet terhubung ke jaringan tanpa kabel.
                    </div>
                    
                    <div class="text-center mt-4">
                        <button id="ap-btn" class="btn" style="background-color: #2b8a3e;">
                            Mulai Simulasi
                        </button>
                    </div>
                </div>
            </div>

            <!-- Switch Simulation -->
            <div class="sim-container">
                <div class="sim-header bg-purple-100">
                    <div class="device-icon" style="background-color: #e5dbff; color: #5f3dc4;">🔄</div>
                    <div class="sim-title">Switch</div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">
                        Switch adalah perangkat yang menghubungkan beberapa perangkat dalam satu jaringan lokal (LAN) dan mengirimkan data ke perangkat yang tepat berdasarkan alamat MAC. Switch lebih cerdas dari Hub karena mengirim data hanya ke tujuan yang ditentukan.
                    </p>
                    
                    <div class="sim-area" id="switch-sim">
                        <!-- Switch device -->
                        <div class="network-device" id="switch-device" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="device-icon" style="background-color: #e5dbff; color: #5f3dc4;">🔄</div>
                            <div class="network-device-label">Switch</div>
                        </div>
                        
                        <!-- Connected devices -->
                        <div class="network-device" id="switch-pc1" style="top: 25%; left: 15%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC 1</div>
                        </div>
                        
                        <div class="network-device" id="switch-pc2" style="top: 25%; left: 85%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC 2</div>
                        </div>
                        
                        <div class="network-device" id="switch-pc3" style="top: 75%; left: 15%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC 3</div>
                        </div>
                        
                        <div class="network-device" id="switch-pc4" style="top: 75%; left: 85%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC 4</div>
                        </div>
                    </div>
                    
                    <div class="explanation-box" id="switch-explanation">
                        Switch menghubungkan beberapa perangkat dalam satu jaringan dan mengirimkan data langsung ke perangkat tujuan, tidak ke semua perangkat.
                    </div>
                    
                    <div class="text-center mt-4">
                        <button id="switch-btn" class="btn" style="background-color: #5f3dc4;">
                            Mulai Simulasi
                        </button>
                    </div>
                </div>
            </div>

            <!-- Perangkat lain akan ditambahkan di langkah berikutnya -->
            
            <!-- Hub Simulation -->
            <div class="sim-container">
                <div class="sim-header bg-yellow-100">
                    <div class="device-icon" style="background-color: #fff3bf; color: #e67700;">🔌</div>
                    <div class="sim-title">Hub</div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">
                        Hub adalah perangkat jaringan sederhana yang menghubungkan beberapa perangkat dalam satu jaringan. Tidak seperti Switch, Hub mengirim data ke semua perangkat yang terhubung, bukan hanya ke perangkat tujuan, yang membuat jaringan kurang efisien.
                    </p>
                    
                    <div class="sim-area" id="hub-sim">
                        <!-- Hub device -->
                        <div class="network-device" id="hub-device" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="device-icon" style="background-color: #fff3bf; color: #e67700;">🔌</div>
                            <div class="network-device-label">Hub</div>
                        </div>
                        
                        <!-- Connected devices -->
                        <div class="network-device" id="hub-pc1" style="top: 25%; left: 15%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC 1</div>
                        </div>
                        
                        <div class="network-device" id="hub-pc2" style="top: 25%; left: 85%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC 2</div>
                        </div>
                        
                        <div class="network-device" id="hub-pc3" style="top: 75%; left: 15%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC 3</div>
                        </div>
                        
                        <div class="network-device" id="hub-pc4" style="top: 75%; left: 85%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC 4</div>
                        </div>
                    </div>
                    
                    <div class="explanation-box" id="hub-explanation">
                        Hub mengirim data ke semua perangkat yang terhubung, tidak peduli mana perangkat tujuan, sehingga kurang efisien dibanding Switch.
                    </div>
                    
                    <div class="text-center mt-4">
                        <button id="hub-btn" class="btn" style="background-color: #e67700;">
                            Mulai Simulasi
                        </button>
                    </div>
                </div>
            </div>

            <!-- Bridge Simulation -->
            <div class="sim-container">
                <div class="sim-header bg-indigo-100">
                    <div class="device-icon" style="background-color: #dbe4ff; color: #4c6ef5;">🌉</div>
                    <div class="sim-title">Bridge</div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">
                        Bridge adalah perangkat yang menghubungkan dua segmen jaringan yang menggunakan protokol yang sama. Bridge bekerja pada layer Data Link dan dapat menyaring lalu lintas data berdasarkan alamat MAC.
                    </p>
                    
                    <div class="sim-area" id="bridge-sim">
                        <!-- Bridge device -->
                        <div class="network-device" id="bridge-device" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="device-icon" style="background-color: #dbe4ff; color: #4c6ef5;">🌉</div>
                            <div class="network-device-label">Bridge</div>
                        </div>
                        
                        <!-- Network segment 1 -->
                        <div class="network-device" id="segment1-pc1" style="top: 20%; left: 20%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">Segmen 1</div>
                        </div>
                        
                        <div class="network-device" id="segment1-pc2" style="top: 60%; left: 20%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">Segmen 1</div>
                        </div>
                        
                        <!-- Network segment 2 -->
                        <div class="network-device" id="segment2-pc1" style="top: 20%; left: 80%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">Segmen 2</div>
                        </div>
                        
                        <div class="network-device" id="segment2-pc2" style="top: 60%; left: 80%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">Segmen 2</div>
                        </div>
                    </div>
                    
                    <div class="explanation-box" id="bridge-explanation">
                        Bridge menghubungkan dua segmen jaringan dan menyaring lalu lintas, memungkinkan data yang relevan saja yang melewati antar segmen.
                    </div>
                    
                    <div class="text-center mt-4">
                        <button id="bridge-btn" class="btn" style="background-color: #4c6ef5;">
                            Mulai Simulasi
                        </button>
                    </div>
                </div>
            </div>

            <!-- Firewall Simulation -->
            <div class="sim-container">
                <div class="sim-header bg-red-100">
                    <div class="device-icon" style="background-color: #ffe3e3; color: #e03131;">🔥</div>
                    <div class="sim-title">Firewall</div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">
                        Firewall adalah perangkat atau program yang memeriksa dan memfilter lalu lintas jaringan masuk dan keluar. Firewall melindungi jaringan dari akses tidak sah dan serangan dari luar.
                    </p>
                    
                    <div class="sim-area" id="firewall-sim">
                        <!-- Firewall device -->
                        <div class="network-device" id="firewall-device" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="device-icon" style="background-color: #ffe3e3; color: #e03131;">🔥</div>
                            <div class="network-device-label">Firewall</div>
                        </div>
                        
                        <!-- Internal network -->
                        <div class="network-device" id="internal-pc1" style="top: 30%; left: 20%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC Internal</div>
                        </div>
                        
                        <div class="network-device" id="internal-pc2" style="top: 70%; left: 20%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">PC Internal</div>
                        </div>
                        
                        <!-- External/Internet -->
                        <div class="network-device" id="external-server" style="top: 30%; left: 80%;">
                            <div class="device-icon" style="font-size: 1.8rem; background-color: #f1f3f5;">🖥️</div>
                            <div class="network-device-label">Server Web</div>
                        </div>
                        
                        <div class="network-device" id="hacker" style="top: 70%; left: 80%;">
                            <div class="device-icon" style="font-size: 1.8rem; background-color: #f1f3f5; color: #e03131;">👾</div>
                            <div class="network-device-label">Penyerang</div>
                        </div>
                    </div>
                    
                    <div class="explanation-box" id="firewall-explanation">
                        Firewall memeriksa semua lalu lintas jaringan dan mengizinkan atau memblokir berdasarkan aturan keamanan, melindungi jaringan internal dari akses tidak sah.
                    </div>
                    
                    <div class="text-center mt-4">
                        <button id="firewall-btn" class="btn" style="background-color: #e03131;">
                            Mulai Simulasi
                        </button>
                    </div>
                </div>
            </div>

            <!-- Repeater Simulation -->
            <div class="sim-container">
                <div class="sim-header bg-cyan-100">
                    <div class="device-icon" style="background-color: #c5f6fa; color: #0c8599;">📡</div>
                    <div class="sim-title">Repeater</div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">
                        Repeater adalah perangkat yang memperkuat sinyal jaringan untuk memperluas jangkauan transmisi. Repeater menerima sinyal yang lemah, memperkuatnya, dan kemudian memancarkannya kembali, sehingga memungkinkan data berjalan jarak lebih jauh.
                    </p>
                    
                    <div class="sim-area" id="repeater-sim">
                        <!-- Long network path with repeater -->
                        <div class="network-device" id="source-device" style="top: 50%; left: 10%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">Sumber</div>
                        </div>
                        
                        <div class="network-device" id="repeater-device" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="device-icon" style="background-color: #c5f6fa; color: #0c8599;">📡</div>
                            <div class="network-device-label">Repeater</div>
                        </div>
                        
                        <div class="network-device" id="destination-device" style="top: 50%; left: 90%;">
                            <div class="computer">💻</div>
                            <div class="network-device-label">Tujuan</div>
                        </div>
                        
                        <!-- Signal strength indicators -->
                        <div id="weak-signal" style="position: absolute; top: 35%; left: 30%; font-size: 0.8rem; color: #adb5bd; display: none;">
                            Sinyal Lemah
                        </div>
                        
                        <div id="strong-signal" style="position: absolute; top: 35%; left: 70%; font-size: 0.8rem; color: #0c8599; display: none;">
                            Sinyal Kuat
                        </div>
                    </div>
                    
                    <div class="explanation-box" id="repeater-explanation">
                        Repeater memperkuat sinyal yang lemah, memungkinkan data ditransmisikan jarak jauh tanpa kehilangan kualitas.
                    </div>
                    
                    <div class="text-center mt-4">
                        <button id="repeater-btn" class="btn" style="background-color: #0c8599;">
                            Mulai Simulasi
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <footer class="text-center mt-12 mb-8">
            <p class="text-sm text-gray-600">
                &copy; 2024 Simulasi Perangkat Jaringan. Dibuat untuk tujuan edukasi.
            </p>
        </footer>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Fungsi untuk membuat koneksi garis antar node
                function createConnection(fromNode, toNode, simId) {
                    if (!fromNode || !toNode) return;
                    
                    const simArea = document.getElementById(simId);
                    const simRect = simArea.getBoundingClientRect();
                    const fromRect = fromNode.getBoundingClientRect();
                    const toRect = toNode.getBoundingClientRect();
                    
                    // Posisi relatif
                    const fromX = fromRect.left + fromRect.width/2 - simRect.left;
                    const fromY = fromRect.top + fromRect.height/2 - simRect.top;
                    const toX = toRect.left + toRect.width/2 - simRect.left;
                    const toY = toRect.top + toRect.height/2 - simRect.top;
                    
                    // Panjang dan sudut
                    const dx = toX - fromX;
                    const dy = toY - fromY;
                    const length = Math.sqrt(dx*dx + dy*dy);
                    const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                    
                    // Buat garis
                    const line = document.createElement('div');
                    line.className = 'connection-line';
                    line.style.width = `${length}px`;
                    line.style.left = `${fromX}px`;
                    line.style.top = `${fromY}px`;
                    line.style.transform = `rotate(${angle}deg)`;
                    
                    simArea.appendChild(line);
                    return line;
                }
                
                // Inisialisasi koneksi untuk semua simulasi
                const simAreas = [
                    {id: 'router-sim', central: 'router-device'},
                    {id: 'ap-sim', central: 'ap-device'},
                    {id: 'switch-sim', central: 'switch-device'},
                    {id: 'hub-sim', central: 'hub-device'},
                    {id: 'bridge-sim', central: 'bridge-device'},
                    {id: 'firewall-sim', central: 'firewall-device'},
                    {id: 'repeater-sim', central: 'repeater-device'}
                ];
                
                simAreas.forEach(sim => {
                    const simArea = document.getElementById(sim.id);
                    if (!simArea) return;
                    
                    const central = document.getElementById(sim.central);
                    if (!central) return;
                    
                    // Temukan semua node dalam simulasi ini
                    const nodes = simArea.querySelectorAll('.network-device:not(#' + sim.central + ')');
                    
                    // Buat koneksi dari setiap node ke node pusat
                    nodes.forEach(node => {
                        createConnection(node, central, sim.id);
                    });
                });
                
                // Fungsi untuk animasi dasar saat tombol diklik
                function setupSimulation(simId, explanation) {
                    const simArea = document.getElementById(simId);
                    const centralNode = document.getElementById(simId.replace('-sim', '-device'));
                    const explanationBox = document.getElementById(simId.replace('-sim', '-explanation'));
                    const button = document.getElementById(simId.replace('-sim', '-btn'));
                    
                    if (!simArea || !centralNode || !explanationBox || !button) return;
                    
                    // Fungsi untuk membuat paket data
                    function createDataPacket(fromNode, toNode, className = '') {
                        if (!fromNode || !toNode) return;
                        
                        const simRect = simArea.getBoundingClientRect();
                        const fromRect = fromNode.getBoundingClientRect();
                        
                        const packet = document.createElement('div');
                        packet.className = `data-packet ${className}`;
                        
                        // Posisi awal
                        packet.style.left = `${fromRect.left + fromRect.width/2 - simRect.left - 6}px`;
                        packet.style.top = `${fromRect.top + fromRect.height/2 - simRect.top - 4}px`;
                        
                        simArea.appendChild(packet);
                        
                        // Animasi
                        setTimeout(() => {
                            const toRect = toNode.getBoundingClientRect();
                            packet.style.left = `${toRect.left + toRect.width/2 - simRect.left - 6}px`;
                            packet.style.top = `${toRect.top + toRect.height/2 - simRect.top - 4}px`;
                            
                            // Hapus paket setelah animasi selesai
                            setTimeout(() => {
                                packet.remove();
                            }, 800);
                        }, 50);
                    }
                    
                    // Efek khusus per perangkat
                    const deviceEffects = {
                        'router-sim': function() {
                            const pcA1 = document.querySelector('#network-a-pc1');
                            const pcB1 = document.querySelector('#network-b-pc1');
                            const internet = document.querySelector('#internet-cloud');
                            
                            if (pcA1 && centralNode && internet) {
                                // Animasi lalu lintas internet
                                pcA1.classList.add('active');
                                setTimeout(() => {
                                    createDataPacket(pcA1, centralNode);
                                    setTimeout(() => {
                                        createDataPacket(centralNode, internet);
                                        setTimeout(() => {
                                            pcA1.classList.remove('active');
                                        }, 800);
                                    }, 1000);
                                }, 1500);
                            }
                            
                            if (pcB1 && centralNode) {
                                // Animasi antar jaringan
                                setTimeout(() => {
                                    pcB1.classList.add('active');
                                    createDataPacket(pcB1, centralNode);
                                    setTimeout(() => {
                                        pcB1.classList.remove('active');
                                    }, 1500);
                                }, 4000);
                            }
                            
                            explanationBox.textContent = 'Router mengontrol lalu lintas data antar jaringan berbeda dan internet.';
                        },
                        
                        'ap-sim': function() {
                            const devices = document.querySelectorAll('#ap-sim .computer');
                            const router = document.querySelector('#router-connection');
                            
                            devices.forEach((device, index) => {
                                const deviceNode = device.parentNode;
                                setTimeout(() => {
                                    deviceNode.classList.add('active');
                                    setTimeout(() => {
                                        createDataPacket(deviceNode, centralNode);
                                        setTimeout(() => {
                                            deviceNode.classList.remove('active');
                                        }, 800);
                                    }, index * 500);
                                }, 1000 + index * 800);
                            });
                            
                            if (router) {
                                setTimeout(() => {
                                    createDataPacket(centralNode, router);
                                }, 3500);
                            }
                            
                            explanationBox.textContent = 'Access Point menghubungkan perangkat nirkabel ke jaringan kabel.';
                        },
                        
                        'switch-sim': function() {
                            const pc1 = document.querySelector('#switch-pc1');
                            const pc4 = document.querySelector('#switch-pc4');
                            
                            if (pc1 && pc4 && centralNode) {
                                setTimeout(() => {
                                    pc1.classList.add('active');
                                    createDataPacket(pc1, centralNode);
                                    setTimeout(() => {
                                        pc1.classList.remove('active');
                                        createDataPacket(centralNode, pc4);
                                        setTimeout(() => {
                                            pc4.classList.add('active');
                                            setTimeout(() => {
                                                pc4.classList.remove('active');
                                            }, 800);
                                        }, 800);
                                    }, 1000);
                                }, 1500);
                            }
                            
                            explanationBox.textContent = 'Switch mengirim data hanya ke perangkat tujuan, meningkatkan efisiensi jaringan.';
                        },
                        
                        'hub-sim': function() {
                            const pc2 = document.querySelector('#hub-pc2');
                            const allPCs = document.querySelectorAll('#hub-sim .computer');
                            
                            if (pc2 && centralNode) {
                                setTimeout(() => {
                                    pc2.parentNode.classList.add('active');
                                    createDataPacket(pc2.parentNode, centralNode);
                                    
                                    setTimeout(() => {
                                        pc2.parentNode.classList.remove('active');
                                        
                                        // Hub mengirim ke semua perangkat
                                        allPCs.forEach(pc => {
                                            if (pc !== pc2) {
                                                setTimeout(() => {
                                                    createDataPacket(centralNode, pc.parentNode);
                                                    pc.parentNode.classList.add('active');
                                                    setTimeout(() => {
                                                        pc.parentNode.classList.remove('active');
                                                    }, 800);
                                                }, 500);
                                            }
                                        });
                                    }, 1000);
                                }, 1500);
                            }
                            
                            explanationBox.textContent = 'Hub mengirim data ke SEMUA perangkat yang terhubung, tidak hanya ke tujuan.';
                        },
                        
                        'bridge-sim': function() {
                            const segment1PC = document.querySelector('#segment1-pc1');
                            const segment2PC = document.querySelector('#segment2-pc2');
                            
                            if (segment1PC && segment2PC && centralNode) {
                                setTimeout(() => {
                                    segment1PC.classList.add('active');
                                    createDataPacket(segment1PC, centralNode);
                                    
                                    setTimeout(() => {
                                        segment1PC.classList.remove('active');
                                        createDataPacket(centralNode, segment2PC);
                                        
                                        setTimeout(() => {
                                            segment2PC.classList.add('active');
                                            setTimeout(() => {
                                                segment2PC.classList.remove('active');
                                            }, 800);
                                        }, 800);
                                    }, 1000);
                                }, 1500);
                            }
                            
                            explanationBox.textContent = 'Bridge menghubungkan segmen jaringan dan menyaring lalu lintas antar segmen.';
                        },
                        
                        'firewall-sim': function() {
                            const internal = document.querySelector('#internal-pc1');
                            const external = document.querySelector('#external-server');
                            const hacker = document.querySelector('#hacker');
                            
                            if (internal && external && hacker && centralNode) {
                                // Lalu lintas yang sah
                                setTimeout(() => {
                                    internal.classList.add('active');
                                    createDataPacket(internal, centralNode);
                                    
                                    setTimeout(() => {
                                        createDataPacket(centralNode, external);
                                        setTimeout(() => {
                                            external.classList.add('active');
                                            setTimeout(() => {
                                                internal.classList.remove('active');
                                                external.classList.remove('active');
                                            }, 800);
                                        }, 800);
                                    }, 1000);
                                }, 1000);
                                
                                // Lalu lintas berbahaya
                                setTimeout(() => {
                                    hacker.classList.add('active');
                                    createDataPacket(hacker, centralNode, 'blocked');
                                    
                                    setTimeout(() => {
                                        hacker.classList.remove('active');
                                    }, 1200);
                                }, 4000);
                            }
                            
                            explanationBox.textContent = 'Firewall mengizinkan lalu lintas yang sah dan memblokir akses tidak sah.';
                        },
                        
                        'repeater-sim': function() {
                            const source = document.querySelector('#source-device');
                            const dest = document.querySelector('#destination-device');
                            const weakSignal = document.querySelector('#weak-signal');
                            const strongSignal = document.querySelector('#strong-signal');
                            
                            if (source && dest && centralNode) {
                                // Tampilkan indikator sinyal
                                setTimeout(() => {
                                    weakSignal.style.display = 'block';
                                    source.classList.add('active');
                                    
                                    setTimeout(() => {
                                        createDataPacket(source, centralNode);
                                        
                                        setTimeout(() => {
                                            weakSignal.style.display = 'none';
                                            strongSignal.style.display = 'block';
                                            
                                            setTimeout(() => {
                                                createDataPacket(centralNode, dest);
                                                setTimeout(() => {
                                                    dest.classList.add('active');
                                                    setTimeout(() => {
                                                        source.classList.remove('active');
                                                        dest.classList.remove('active');
                                                        strongSignal.style.display = 'none';
                                                    }, 1000);
                                                }, 800);
                                            }, 800);
                                        }, 1000);
                                    }, 800);
                                }, 1000);
                            }
                            
                            explanationBox.textContent = 'Repeater memperkuat sinyal jaringan yang lemah agar dapat menjangkau jarak lebih jauh.';
                        }
                    };
                    
                    // Inisialisasi tombol simulasi
                    button.addEventListener('click', function() {
                        // Tampilkan kotak penjelasan
                        explanationBox.style.display = 'block';
                        
                        // Aktifkan node pusat
                        centralNode.classList.add('active');
                        
                        // Aktifkan semua koneksi secara berurutan
                        const connections = simArea.querySelectorAll('.connection-line');
                        let delay = 0;
                        connections.forEach(conn => {
                            setTimeout(() => {
                                conn.classList.add('active');
                            }, delay);
                            delay += 100;
                        });
                        
                        // Jalankan efek khusus untuk perangkat ini
                        if (deviceEffects[simId]) {
                            deviceEffects[simId]();
                        }
                        
                        // Reset setelah 8 detik
                        setTimeout(() => {
                            centralNode.classList.remove('active');
                            connections.forEach(conn => {
                                conn.classList.remove('active');
                            });
                            
                            // Reset semua node aktif yang mungkin tersisa
                            simArea.querySelectorAll('.network-device.active').forEach(node => {
                                node.classList.remove('active');
                            });
                        }, 8000);
                    });
                }
                
                // Setup animasi dasar untuk semua simulasi
                simAreas.forEach(sim => {
                    setupSimulation(sim.id);
                });
            });
        </script>
    </body>
</html> 