<!doctype html>
<html lang="id">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Arsitektur Jaringan: Client-Server vs Peer-to-Peer</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family: "Inter", sans-serif;
                background-color: #f8f9fa;
                color: #343a40;
            }
            .simulation-area {
                border: 1px solid #dee2e6;
                border-radius: 10px;
                padding: 20px;
                background-color: #ffffff;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);
                position: relative;
                min-height: 300px;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }
            .node {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                position: absolute;
                font-size: 1.5rem;
                transition: all 0.3s ease;
                cursor: pointer;
                z-index: 10;
            }
            .node:hover {
                transform: scale(1.1);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            }
            .node-label {
                position: absolute;
                font-size: 0.75rem;
                font-weight: 600;
                background-color: white;
                padding: 2px 6px;
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                white-space: nowrap;
                z-index: 20;
            }
            .node.server {
                background-color: #3b82f6;
                color: white;
            }
            .node.client {
                background-color: #f1f3f5;
                color: #343a40;
                border: 1px solid #ced4da;
            }
            .node.peer {
                background-color: #38b2ac;
                color: white;
            }
            .node.active {
                animation: pulse 1s infinite alternate;
            }
            .node.server.active {
                animation: server-pulse 1s infinite alternate;
            }
            @keyframes pulse {
                0% {
                    transform: scale(1);
                }
                100% {
                    transform: scale(1.15);
                    box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
                }
            }
            @keyframes server-pulse {
                0% {
                    transform: translate(-50%, -50%) scale(1);
                }
                100% {
                    transform: translate(-50%, -50%) scale(1.15);
                    box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
                }
            }
            .connection-line {
                position: absolute;
                height: 3px;
                background-color: #adb5bd;
                transform-origin: left center;
                z-index: 5;
            }
            .connection-line.active {
                background-color: #3b82f6;
                height: 4px;
            }
            .connection-line.p2p-line {
                background-color: #38b2ac;
            }
            .connection-line.p2p-line.active {
                background-color: #0d9488;
                height: 4px;
            }
            .data-packet {
                position: absolute;
                width: 12px;
                height: 8px;
                border-radius: 4px;
                background-color: #fd7e14;
                z-index: 15;
                transition: left 0.8s ease, top 0.8s ease;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            }
            .message-box {
                padding: 12px;
                border-radius: 8px;
                margin-top: 15px;
                font-size: 0.9rem;
                text-align: center;
                display: none;
            }
            .message-info {
                background-color: #e7f5ff;
                color: #1864ab;
                border: 1px solid #74c0fc;
            }
            .btn {
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: 500;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            .btn-primary {
                background-color: #3b82f6;
                color: white;
            }
            .btn-primary:hover {
                background-color: #2563eb;
                transform: translateY(-2px);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .btn-secondary {
                background-color: #38b2ac;
                color: white;
            }
            .btn-secondary:hover {
                background-color: #2c9a94;
                transform: translateY(-2px);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .feature-table {
                border-collapse: separate;
                border-spacing: 0;
                width: 100%;
                border-radius: 10px;
                overflow: hidden;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);
            }
            .feature-table th,
            .feature-table td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #e9ecef;
            }
            .feature-table th:first-child,
            .feature-table td:first-child {
                width: 20%;
            }
            .feature-table th:not(:first-child),
            .feature-table td:not(:first-child) {
                width: 40%;
            }
            .feature-table th {
                background-color: #f8f9fa;
                font-weight: 600;
                color: #343a40;
            }
            .feature-table tr:last-child td {
                border-bottom: none;
            }
            .feature-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                font-size: 0.75rem;
                font-weight: 600;
                line-height: 1;
                text-align: center;
                white-space: nowrap;
                vertical-align: baseline;
            }
            .badge-blue {
                background-color: #e7f5ff;
                color: #1864ab;
            }
            .badge-green {
                background-color: #d3f9d8;
                color: #2b8a3e;
            }
            .badge-red {
                background-color: #ffe3e3;
                color: #c92a2a;
            }
        </style>
    </head>
    <body class="p-4 md:p-8">
        <header class="text-center mb-10">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800">
                Arsitektur Jaringan Komputer
            </h1>
            <p class="text-gray-600 mt-2">
                Perbandingan visual antara model Client-Server dan Peer-to-Peer
            </p>
        </header>

        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
                <!-- Client-Server Simulation -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="bg-blue-600 text-white p-4">
                        <h2 class="text-xl font-semibold">Client-Server</h2>
                        <p class="text-sm text-blue-100">
                            Satu server melayani banyak klien
                        </p>
                    </div>
                    <div class="p-5">
                        <div class="simulation-area" id="client-server-sim">
                            <div class="node server" id="server-node" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                🖥️
                            </div>
                            <div class="node-label" style="top: 25%; left: 50%; transform: translate(-50%, -100%);">
                                Server
                            </div>
                            
                            <div class="node client" id="client1-node" style="top: 25%; left: 20%;">
                                👩‍💼
                            </div>
                            <div class="node-label" style="top: 25%; left: 20%; transform: translate(-50%, -100%);">
                                Klien 1
                            </div>
                            
                            <div class="node client" id="client2-node" style="top: 75%; left: 20%;">
                                👨‍💼
                            </div>
                            <div class="node-label" style="top: 75%; left: 20%; transform: translate(-50%, -100%);">
                                Klien 2
                            </div>
                            
                            <div class="node client" id="client3-node" style="top: 25%; left: 80%;">
                                👩‍🏫
                            </div>
                            <div class="node-label" style="top: 25%; left: 80%; transform: translate(-50%, -100%);">
                                Klien 3
                            </div>
                            
                            <div class="node client" id="client4-node" style="top: 75%; left: 80%;">
                                👨‍🔧
                            </div>
                            <div class="node-label" style="top: 75%; left: 80%; transform: translate(-50%, -100%);">
                                Klien 4
                            </div>

                            <!-- Connection lines will be created via JavaScript -->
                        </div>
                        
                        <div class="mt-4 text-center">
                            <button class="btn btn-primary" id="client-server-demo-btn">
                                Mulai Simulasi
                            </button>
                            <div id="client-server-message" class="message-box message-info"></div>
                        </div>
                        
                        <div class="mt-4 text-sm text-gray-600">
                            <p><strong>Cara Kerja:</strong> Dalam model Client-Server, server menjadi pusat penyimpanan dan pemrosesan data. Klien harus terhubung dengan server untuk berbagi atau mendapatkan data.</p>
                        </div>
                    </div>
                </div>

                <!-- Peer-to-Peer Simulation -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="bg-teal-600 text-white p-4">
                        <h2 class="text-xl font-semibold">Peer-to-Peer (P2P)</h2>
                        <p class="text-sm text-teal-100">
                            Semua komputer saling terhubung langsung
                        </p>
                    </div>
                    <div class="p-5">
                        <div class="simulation-area" id="p2p-sim">
                            <div class="node peer" id="peer1-node" style="top: 20%; left: 30%;">
                                👩‍💼
                            </div>
                            <div class="node-label" style="top: 20%; left: 30%; transform: translate(-50%, -100%);">
                                Peer 1
                            </div>
                            
                            <div class="node peer" id="peer2-node" style="top: 20%; left: 70%;">
                                👨‍💼
                            </div>
                            <div class="node-label" style="top: 20%; left: 70%; transform: translate(-50%, -100%);">
                                Peer 2
                            </div>
                            
                            <div class="node peer" id="peer3-node" style="top: 75%; left: 25%;">
                                👩‍🏫
                            </div>
                            <div class="node-label" style="top: 75%; left: 25%; transform: translate(-50%, -100%);">
                                Peer 3
                            </div>
                            
                            <div class="node peer" id="peer4-node" style="top: 75%; left: 75%;">
                                👨‍🔧
                            </div>
                            <div class="node-label" style="top: 75%; left: 75%; transform: translate(-50%, -100%);">
                                Peer 4
                            </div>

                            <!-- Connection lines will be created via JavaScript -->
                        </div>
                        
                        <div class="mt-4 text-center">
                            <button class="btn btn-secondary" id="p2p-demo-btn">
                                Mulai Simulasi
                            </button>
                            <div id="p2p-message" class="message-box message-info"></div>
                        </div>
                        
                        <div class="mt-4 text-sm text-gray-600">
                            <p><strong>Cara Kerja:</strong> Dalam model Peer-to-Peer, tidak ada server pusat. Setiap komputer (peer) dapat langsung berbagi data dengan komputer lain tanpa perantara.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comparison Table -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden p-6 mb-10">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Perbandingan Kedua Arsitektur</h2>
                
                <div class="overflow-x-auto">
                    <table class="feature-table">
                        <thead>
                            <tr>
                                <th>Fitur</th>
                                <th>Client-Server</th>
                                <th>Peer-to-Peer</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Struktur</td>
                                <td>Terpusat, satu server melayani banyak klien</td>
                                <td>Terdesentralisasi, antar komputer saling terhubung</td>
                            </tr>
                            <tr>
                                <td>Keterbergantungan</td>
                                <td>
                                    <span class="badge badge-red">Tinggi</span> - Jika server mati, seluruh sistem tidak berfungsi
                                </td>
                                <td>
                                    <span class="badge badge-green">Rendah</span> - Sistem tetap berjalan meski beberapa peer mati
                                </td>
                            </tr>
                            <tr>
                                <td>Skalabilitas</td>
                                <td>
                                    <span class="badge badge-blue">Sedang</span> - Terbatas oleh kapasitas server
                                </td>
                                <td>
                                    <span class="badge badge-green">Tinggi</span> - Mudah bertambah seiring jumlah peer
                                </td>
                            </tr>
                            <tr>
                                <td>Keamanan</td>
                                <td>
                                    <span class="badge badge-green">Baik</span> - Pengamanan terpusat di server
                                </td>
                                <td>
                                    <span class="badge badge-red">Menantang</span> - Sulit mengontrol keamanan di semua peer
                                </td>
                            </tr>
                            <tr>
                                <td>Pemeliharaan</td>
                                <td>
                                    <span class="badge badge-green">Mudah</span> - Cukup perbaiki di server pusat
                                </td>
                                <td>
                                    <span class="badge badge-red">Sulit</span> - Perlu perbaikan di banyak peer
                                </td>
                            </tr>
                            <tr>
                                <td>Biaya</td>
                                <td>
                                    <span class="badge badge-red">Tinggi</span> - Perlu server berperforma tinggi
                                </td>
                                <td>
                                    <span class="badge badge-green">Rendah</span> - Memanfaatkan resource yang ada
                                </td>
                            </tr>
                            <tr>
                                <td>Contoh Aplikasi</td>
                                <td>Web server (Apache, Nginx), Application Server (JBoss, Tomcat), Database Server (MySQL, PostgreSQL), Mail Server (Postfix, Exchange), File Server, DNS (menerjemahkan domain ke IP Address)</td>
                                <td>BitTorrent, Aplikasi chat P2P, Cryptocurrency</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Conclusion -->
            <div class="bg-gray-100 rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-3">Kesimpulan</h2>
                <p class="text-gray-700">
                    Kedua arsitektur memiliki kelebihan dan kekurangan. Client-Server ideal untuk aplikasi yang membutuhkan kontrol terpusat, keamanan tinggi, dan konsistensi data. Sementara itu, Peer-to-Peer cocok untuk berbagi resource, ketahanan terhadap gangguan, dan aplikasi terdesentralisasi.
                </p>
                <p class="text-gray-700 mt-2">
                    Di dunia nyata, banyak sistem yang menggunakan arsitektur hibrida, menggabungkan kelebihan dari kedua model untuk kebutuhan spesifik.
                </p>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Client-Server Elements
                const clientServerSim = document.getElementById('client-server-sim');
                const clientServerBtn = document.getElementById('client-server-demo-btn');
                const clientServerMsg = document.getElementById('client-server-message');
                const serverNode = document.getElementById('server-node');
                const clientNodes = [
                    document.getElementById('client1-node'),
                    document.getElementById('client2-node'),
                    document.getElementById('client3-node'),
                    document.getElementById('client4-node')
                ];

                // P2P Elements
                const p2pSim = document.getElementById('p2p-sim');
                const p2pBtn = document.getElementById('p2p-demo-btn');
                const p2pMsg = document.getElementById('p2p-message');
                const peerNodes = [
                    document.getElementById('peer1-node'),
                    document.getElementById('peer2-node'),
                    document.getElementById('peer3-node'),
                    document.getElementById('peer4-node')
                ];

                // Create Client-Server connections
                function setupClientServerConnections() {
                    const serverRect = serverNode.getBoundingClientRect();
                    const simRect = clientServerSim.getBoundingClientRect();
                    
                    clientNodes.forEach((clientNode, index) => {
                        const clientRect = clientNode.getBoundingClientRect();
                        
                        // Calculate positions relative to simulation area
                        const serverX = serverRect.left + serverRect.width / 2 - simRect.left;
                        const serverY = serverRect.top + serverRect.height / 2 - simRect.top;
                        const clientX = clientRect.left + clientRect.width / 2 - simRect.left;
                        const clientY = clientRect.top + clientRect.height / 2 - simRect.top;
                        
                        // Calculate line length and angle
                        const dx = serverX - clientX;
                        const dy = serverY - clientY;
                        const length = Math.sqrt(dx * dx + dy * dy);
                        const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                        
                        // Create connection line
                        const line = document.createElement('div');
                        line.className = 'connection-line';
                        line.id = `cs-line-${index}`;
                        line.style.width = `${length}px`;
                        line.style.left = `${clientX}px`;
                        line.style.top = `${clientY}px`;
                        line.style.transform = `rotate(${angle}deg)`;
                        
                        clientServerSim.appendChild(line);
                    });
                }

                // Create P2P connections (fully connected mesh)
                function setupP2PConnections() {
                    const simRect = p2pSim.getBoundingClientRect();
                    let lineIndex = 0;
                    
                    for (let i = 0; i < peerNodes.length; i++) {
                        for (let j = i + 1; j < peerNodes.length; j++) {
                            const peer1Rect = peerNodes[i].getBoundingClientRect();
                            const peer2Rect = peerNodes[j].getBoundingClientRect();
                            
                            // Calculate positions relative to simulation area
                            const peer1X = peer1Rect.left + peer1Rect.width / 2 - simRect.left;
                            const peer1Y = peer1Rect.top + peer1Rect.height / 2 - simRect.top;
                            const peer2X = peer2Rect.left + peer2Rect.width / 2 - simRect.left;
                            const peer2Y = peer2Rect.top + peer2Rect.height / 2 - simRect.top;
                            
                            // Calculate line length and angle
                            const dx = peer2X - peer1X;
                            const dy = peer2Y - peer1Y;
                            const length = Math.sqrt(dx * dx + dy * dy);
                            const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                            
                            // Create connection line
                            const line = document.createElement('div');
                            line.className = 'connection-line p2p-line';
                            line.id = `p2p-line-${lineIndex++}`;
                            line.style.width = `${length}px`;
                            line.style.left = `${peer1X}px`;
                            line.style.top = `${peer1Y}px`;
                            line.style.transform = `rotate(${angle}deg)`;
                            
                            p2pSim.appendChild(line);
                        }
                    }
                }

                // Function to create and animate data packet
                function createDataPacket(sourceNode, targetNode, container, onComplete = null) {
                    const packet = document.createElement('div');
                    packet.className = 'data-packet';
                    
                    const containerRect = container.getBoundingClientRect();
                    const sourceRect = sourceNode.getBoundingClientRect();
                    
                    // Set initial position at source node
                    packet.style.left = `${sourceRect.left + sourceRect.width / 2 - containerRect.left - 6}px`;
                    packet.style.top = `${sourceRect.top + sourceRect.height / 2 - containerRect.top - 4}px`;
                    
                    container.appendChild(packet);
                    
                    // Animate to target after a small delay
                    setTimeout(() => {
                        const targetRect = targetNode.getBoundingClientRect();
                        packet.style.left = `${targetRect.left + targetRect.width / 2 - containerRect.left - 6}px`;
                        packet.style.top = `${targetRect.top + targetRect.height / 2 - containerRect.top - 4}px`;
                        
                        // Remove packet and call completion handler
                        setTimeout(() => {
                            packet.remove();
                            if (onComplete) onComplete();
                        }, 800);
                    }, 50);
                }

                // Client-Server demonstration
                let clientServerDemoRunning = false;
                clientServerBtn.addEventListener('click', function() {
                    if (clientServerDemoRunning) return;
                    clientServerDemoRunning = true;
                    
                    clientServerMsg.style.display = 'none';
                    
                    // Reset active states
                    document.querySelectorAll('.node').forEach(node => node.classList.remove('active'));
                    document.querySelectorAll('.connection-line').forEach(line => line.classList.remove('active'));
                    
                    clientServerMsg.textContent = 'Semua klien terhubung ke server pusat.';
                    clientServerMsg.style.display = 'block';
                    
                    const runDemo = async () => {
                        // Step 1: Client 1 requests data from server
                        clientNodes[0].classList.add('active');
                        document.getElementById('cs-line-0').classList.add('active');
                        clientServerMsg.textContent = 'Klien 1 meminta data dari server.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        createDataPacket(clientNodes[0], serverNode, clientServerSim);
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        serverNode.classList.add('active');
                        clientServerMsg.textContent = 'Server memproses permintaan...';
                        
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        createDataPacket(serverNode, clientNodes[0], clientServerSim);
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        clientServerMsg.textContent = 'Klien 1 menerima respon dari server.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        clientNodes[0].classList.remove('active');
                        document.getElementById('cs-line-0').classList.remove('active');
                        
                        // Step 2: Multiple clients request data
                        clientNodes[2].classList.add('active');
                        clientNodes[3].classList.add('active');
                        document.getElementById('cs-line-2').classList.add('active');
                        document.getElementById('cs-line-3').classList.add('active');
                        clientServerMsg.textContent = 'Klien 3 dan 4 meminta data secara bersamaan.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        createDataPacket(clientNodes[2], serverNode, clientServerSim);
                        createDataPacket(clientNodes[3], serverNode, clientServerSim);
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        clientServerMsg.textContent = 'Server menangani banyak permintaan sekaligus.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        createDataPacket(serverNode, clientNodes[2], clientServerSim);
                        createDataPacket(serverNode, clientNodes[3], clientServerSim);
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        clientServerMsg.textContent = 'Server mengirim data yang berbeda ke masing-masing klien.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        clientNodes[2].classList.remove('active');
                        clientNodes[3].classList.remove('active');
                        document.getElementById('cs-line-2').classList.remove('active');
                        document.getElementById('cs-line-3').classList.remove('active');
                        serverNode.classList.remove('active');
                        
                        clientServerMsg.textContent = 'Dalam model Client-Server, semua komunikasi harus melalui server pusat.';
                        
                        // End demo
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        clientServerMsg.style.display = 'none';
                        clientServerDemoRunning = false;
                    };
                    
                    runDemo();
                });

                // P2P demonstration
                let p2pDemoRunning = false;
                p2pBtn.addEventListener('click', function() {
                    if (p2pDemoRunning) return;
                    p2pDemoRunning = true;
                    
                    p2pMsg.style.display = 'none';
                    
                    // Reset active states
                    document.querySelectorAll('.node').forEach(node => node.classList.remove('active'));
                    document.querySelectorAll('.connection-line').forEach(line => line.classList.remove('active'));
                    
                    p2pMsg.textContent = 'Dalam jaringan P2P, tiap peer terhubung langsung dengan peer lainnya.';
                    p2pMsg.style.display = 'block';
                    
                    const runDemo = async () => {
                        // Step 1: Peer 1 shares with Peer 2
                        peerNodes[0].classList.add('active');
                        peerNodes[1].classList.add('active');
                        document.getElementById('p2p-line-0').classList.add('active');
                        p2pMsg.textContent = 'Peer 1 berbagi file langsung dengan Peer 2.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        createDataPacket(peerNodes[0], peerNodes[1], p2pSim);
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        p2pMsg.textContent = 'Tidak ada server yang diperlukan untuk pertukaran data.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        peerNodes[0].classList.remove('active');
                        peerNodes[1].classList.remove('active');
                        document.getElementById('p2p-line-0').classList.remove('active');
                        
                        // Step 2: Multiple sharing at once
                        peerNodes[1].classList.add('active');
                        peerNodes[2].classList.add('active');
                        peerNodes[3].classList.add('active');
                        document.getElementById('p2p-line-3').classList.add('active');
                        document.getElementById('p2p-line-5').classList.add('active');
                        p2pMsg.textContent = 'Beberapa peer dapat berbagi data secara bersamaan.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        createDataPacket(peerNodes[1], peerNodes[2], p2pSim);
                        createDataPacket(peerNodes[2], peerNodes[3], p2pSim);
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        p2pMsg.textContent = 'Setiap peer bertindak sebagai client dan server sekaligus.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        peerNodes[1].classList.remove('active');
                        peerNodes[2].classList.remove('active');
                        peerNodes[3].classList.remove('active');
                        document.getElementById('p2p-line-3').classList.remove('active');
                        document.getElementById('p2p-line-5').classList.remove('active');
                        
                        // Step 3: Show resilience - Peer 4 directly communicates with Peer 1
                        peerNodes[0].classList.add('active');
                        peerNodes[3].classList.add('active');
                        document.getElementById('p2p-line-2').classList.add('active');
                        p2pMsg.textContent = 'Jika beberapa peer tidak tersedia, komunikasi tetap bisa berlangsung.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        createDataPacket(peerNodes[3], peerNodes[0], p2pSim);
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        p2pMsg.textContent = 'Ini menjadikan jaringan P2P lebih tangguh terhadap kegagalan.';
                        
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        peerNodes[0].classList.remove('active');
                        peerNodes[3].classList.remove('active');
                        document.getElementById('p2p-line-2').classList.remove('active');
                        
                        p2pMsg.textContent = 'Dalam P2P, semua peer memiliki peran setara dan dapat berkomunikasi langsung.';
                        
                        // End demo
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        p2pMsg.style.display = 'none';
                        p2pDemoRunning = false;
                    };
                    
                    runDemo();
                });

                // Set up connections on page load
                setupClientServerConnections();
                setupP2PConnections();
            });
        </script>
    </body>
</html> 