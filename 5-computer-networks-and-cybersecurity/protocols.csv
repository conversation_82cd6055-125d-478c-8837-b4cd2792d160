<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>kat
Protokol Dasar Internet,TCP/IP,"Kumpulan protokol dasar internet yang mengatur pengalamatan dan transmisi data yang andal antar perangkat."
Protokol Dasar Internet,IP,"Protokol yang memberikan alamat unik pada perangkat dan merutekan paket data ke tujuan."
Protokol Dasar Internet,TCP,"Memastikan pengiriman data yang andal dengan memecah, mengirim, dan menyusun kembali paket data secara berurutan."
Protokol Dasar Internet,UDP,"Protokol cepat tanpa konfirmasi pengiriman, ideal untuk streaming dan game online yang mengutamakan kecepatan."
Protokol Dasar Internet,ICMP,"Digunakan perangkat jaringan untuk mengirim pesan kesalahan dan informasi operasional, contohnya perintah ping."
Protokol Web dan Aplikasi,HTTP,"Protokol dasar untuk komunikasi browser dan server web, mengirimkan permintaan dan menerima konten halaman."
Protokol Web dan <PERSON>,HTTPS,"Versi aman HTTP yang mengenkripsi data antara browser dan server web."
Protokol Web dan Aplikasi,WebSockets,"Memungkinkan komunikasi dua arah secara terus-menerus antara browser dan server untuk aplikasi real-time."
Protokol Web dan Aplikasi,FTP,"Protokol untuk mentransfer file antar komputer melalui jaringan."
Protokol Web dan Aplikasi,SFTP,"Versi aman dari FTP yang menggunakan SSH untuk enkripsi saat mentransfer file."
Protokol Email,SMTP,"Protokol untuk mengirim email dari aplikasi pengirim ke server penerima."
Protokol Email,POP3,"Protokol untuk mengunduh email dari server ke perangkat, biasanya menghapus email dari server."
Protokol Email,IMAP,"Protokol untuk menyinkronkan email antar perangkat dengan menyimpan salinan di server."
Protokol Pendukung Jaringan,DNS,"Menerjemahkan nama domain yang mudah diingat (seperti google.com) menjadi alamat IP numerik."
Protokol Pendukung Jaringan,DHCP,"Secara otomatis memberikan alamat IP sementara ke perangkat saat terhubung ke jaringan."
Protokol Pendukung Jaringan,NTP,"Menyinkronkan waktu semua perangkat di jaringan dengan server waktu standar."
Protokol Pendukung Jaringan,ARP,"Menerjemahkan alamat IP menjadi alamat MAC fisik di jaringan lokal."
Protokol Keamanan,SSL/TLS,"Protokol enkripsi yang mengamankan komunikasi di jaringan, digunakan dalam HTTPS dan layanan lainnya."
Protokol Keamanan,SSH,"Protokol untuk mengakses dan mengelola perangkat jarak jauh secara aman melalui jaringan."
Protokol Keamanan,IPsec,"Protokol keamanan jaringan yang mengenkripsi komunikasi di tingkat IP."
Protokol Keamanan,WPA/WPA2/WPA3,"Standar keamanan untuk jaringan Wi-Fi yang mengenkripsi lalu lintas nirkabel."
Protokol Berbagi Sumber Daya,SMB/CIFS,"Memungkinkan berbagi file, printer, dan sumber daya lain antar perangkat di jaringan lokal."
Protokol Berbagi Sumber Daya,RDP,"Memungkinkan pengguna mengakses dan mengendalikan komputer lain dari jarak jauh."
Protokol Berbagi Sumber Daya,NFS,"Protokol berbagi file untuk sistem Unix/Linux yang memungkinkan akses file di jaringan."
Protokol Komunikasi Real-time,SIP,"Mengelola membangun dan mengakhiri panggilan suara/video melalui internet (VoIP)."
Protokol Komunikasi Real-time,RTP,"Mengirimkan data suara dan video secara real-time selama panggilan berlangsung."
Protokol Komunikasi Real-time,WebRTC,"Memungkinkan komunikasi audio/video real-time langsung di browser tanpa plugin tambahan."
Protokol Nirkabel,Wi-Fi (IEEE 802.11),"Standar untuk koneksi nirkabel yang menentukan bagaimana perangkat berkomunikasi melalui WLAN."
Protokol Nirkabel,Bluetooth,"Protokol komunikasi nirkabel jarak pendek untuk menghubungkan perangkat seperti headphone dan speaker."
Protokol Nirkabel,Zigbee,"Protokol nirkabel hemat daya untuk jaringan mesh perangkat IoT dengan jangkauan pendek."
Protokol Nirkabel,Z-Wave,"Protokol nirkabel untuk otomasi rumah dengan konsumsi daya rendah dan jangkauan terbatas."
Protokol IoT,MQTT,"Protokol ringan untuk perangkat dengan sumber daya terbatas, populer dalam aplikasi IoT."
Protokol IoT,CoAP,"Protokol khusus untuk perangkat IoT dengan daya rendah yang memungkinkan komunikasi di jaringan terbatas."
Protokol IoT,AMQP,"Protokol pertukaran pesan yang andal untuk aplikasi IoT dan enterprise yang membutuhkan jaminan pengiriman."
Protokol IoT,LoRaWAN,"Protokol jaringan untuk komunikasi IoT jarak jauh dengan daya rendah."
Protokol Routing,BGP,"Protokol routing utama internet yang menghubungkan sistem otonom berbeda dan menentukan jalur terbaik."
Protokol Routing,OSPF,"Protokol routing internal yang menghitung jalur terpendek dalam jaringan berdasarkan bandwidth jaringan."
Protokol Routing,EIGRP,"Protokol routing Cisco yang menggunakan metrik kompleks untuk menentukan jalur terbaik."
Protokol Manajemen Jaringan,SNMP,"Memantau dan mengelola perangkat jaringan seperti router dan switch dari jarak jauh."
Protokol Manajemen Jaringan,LDAP,"Protokol untuk mengakses dan mengelola informasi direktori seperti data pengguna dalam organisasi."
Protokol Manajemen Jaringan,RADIUS,"Protokol otentikasi, otorisasi, dan akunting untuk mengontrol akses jaringan."
Protokol Berbagi File,BitTorrent,"Protokol berbagi file peer-to-peer yang mendistribusikan beban unduhan di antara banyak pengguna."
Protokol Berbagi File,IPFS,"Protokol sistem file terdistribusi untuk menyimpan dan berbagi data dalam jaringan peer-to-peer."
Protokol Berbagi File,rsync,"Protokol sinkronisasi file yang efisien dengan hanya mentransfer perubahan antara file sumber dan tujuan."