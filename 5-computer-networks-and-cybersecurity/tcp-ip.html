<!doctype html>
<html lang="id">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Model TCP/IP</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family: "Inter", sans-serif;
                background-color: #f8f9fa;
                overflow-x: hidden;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                color: #343a40;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 1.5rem;
                flex: 1;
                display: flex;
                flex-direction: column;
            }
            .layer-box {
                border: 1px solid #dee2e6;
                border-radius: 10px;
                padding: 12px;
                margin-bottom: 15px;
                position: relative;
                transition: all 0.3s ease;
                background-color: white;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            }
            .layer-box.active {
                background-color: #e6f7ff;
                transform: scale(1.03);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12);
                border-color: #90cdf4;
                z-index: 10;
            }
            .data-box {
                background-color: #f1f3f5;
                border-radius: 6px;
                padding: 10px;
                margin-top: 8px;
                text-align: center;
                font-size: 0.875rem;
            }
            .header-row {
                background-color: #e9ecef;
                border-radius: 4px;
                padding: 5px 8px;
                margin-bottom: 5px;
                font-size: 0.85rem;
                color: #495057;
            }
            .network-line {
                height: 8px;
                background-color: #adb5bd;
                border-radius: 4px;
                position: relative;
                margin: 0 10px;
            }
            .packet {
                position: absolute;
                width: 22px;
                height: 16px;
                background-color: #007bff;
                border: 1px solid #0056b3;
                border-radius: 4px;
                top: -4px;
                left: 0;
                transition: left 2s linear;
                box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            }
            .arrow {
                position: absolute;
                height: 12px;
                width: 20px;
                left: calc(50% - 10px);
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 12'%3E%3Cpath d='M10 12L0 0h20z' fill='%236c757d'/%3E%3C/svg%3E");
            }
            .arrow-down {
                top: 100%;
                margin-top: 2px;
            }
            .arrow-up {
                bottom: 100%;
                margin-bottom: 2px;
                transform: rotate(180deg);
            }
            .step {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background-color: #ced4da;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #495057;
                font-weight: 600;
                margin-bottom: 6px;
                transition: all 0.3s ease;
                border: 2px solid transparent;
            }
            .step.active {
                background-color: #007bff;
                color: white;
                transform: scale(1.15);
                border-color: #0056b3;
            }
            .step.completed {
                background-color: #28a745;
                color: white;
                border-color: #1e7e34;
            }
            h1, h2, h3 {
                color: #212529;
            }
            .font-medium {
                font-weight: 500;
            }
            .text-blue-600 {
                color: #0056b3 !important;
            }
            .text-gray-600 {
                color: #6c757d !important;
            }
            #start-button {
                font-weight: 600;
                padding-top: 0.65rem;
                padding-bottom: 0.65rem;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: all 0.2s ease-out;
            }
            #start-button:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }
            #message {
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 500;
            }
            #message.bg-blue-100 {
                background-color: #cfe2ff !important;
                color: #084298 !important;
                border: 1px solid #b6d4fe;
            }
            #message.bg-green-100 {
                background-color: #d1e7dd !important;
                color: #0f5132 !important;
                border: 1px solid #badbcc;
            }
            .bg-white.p-3.rounded-lg.shadow.mb-4 {
                border-radius: 10px;
                box-shadow: 0 3px 10px rgba(0,0,0,0.07);
                padding: 1rem;
            }
            .col-span-2.bg-gray-100.rounded-lg.p-3.border-2.border-gray-300 {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 10px;
                padding: 1rem;
                box-shadow: 0 3px 10px rgba(0,0,0,0.07);
            }
            .font-bold.text-blue-600.border-l-4.border-blue-500 {
                color: #0056b3 !important;
                border-left-width: 5px;
                border-color: #007bff !important;
                padding-left: 0.75rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header class="text-center mb-10">
                <h1 class="text-4xl font-bold text-gray-800">Model TCP/IP</h1>
                <p class="text-sm text-gray-600">
                    Simulasi visual bagaimana data bergerak melalui jaringan
                </p>
            </header>

            <div class="grid grid-cols-6 gap-4 flex-1">
                <!-- Left Info Panel -->
                <div class="col-span-1">
                    <div class="bg-white p-3 rounded-lg shadow mb-4">
                        <h2
                            class="font-bold text-blue-600 border-l-4 border-blue-500 pl-2 mb-2"
                        >
                            4 Lapisan TCP/IP
                        </h2>
                        <ul class="space-y-1 pl-2">
                            <li>
                                <span class="font-medium text-blue-600"
                                    >Aplikasi:</span
                                >
                                HTTP, FTP, SMTP
                            </li>
                            <li>
                                <span class="font-medium text-blue-600"
                                    >Transport:</span
                                >
                                TCP, UDP
                            </li>
                            <li>
                                <span class="font-medium text-blue-600"
                                    >Internet:</span
                                >
                                IP
                            </li>
                            <li>
                                <span class="font-medium text-blue-600"
                                    >N. Interface:</span
                                >
                                Ethernet, WiFi
                            </li>
                        </ul>
                    </div>

                    <div class="flex justify-around mb-4">
                        <div class="flex flex-col items-center">
                            <div class="step" id="step-1">1</div>
                            <div class="text-sm">Enkapsulasi</div>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="step" id="step-2">2</div>
                            <div class="text-sm">Transmisi</div>
                        </div>
                        <div class="flex flex-col items-center">
                            <div class="step" id="step-3">3</div>
                            <div class="text-sm">Dekapsulasi</div>
                        </div>
                    </div>

                    <button
                        id="start-button"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium transition-colors"
                    >
                        Mulai Simulasi
                    </button>

                    <div
                        id="message"
                        class="mt-3 p-2 rounded bg-blue-100 text-blue-800 text-sm hidden"
                    ></div>
                </div>

                <!-- Main Simulation Area -->
                <div class="col-span-5 grid grid-cols-5 gap-4">
                    <!-- Sender Computer -->
                    <div
                        class="col-span-2 bg-gray-100 rounded-lg p-3 border-2 border-gray-300"
                    >
                        <h3 class="font-semibold text-center mb-2">
                            Komputer Pengirim
                        </h3>

                        <div class="layer-box" id="sender-app">
                            <div class="flex justify-between">
                                <span class="font-medium">Aplikasi</span>
                                <span class="text-blue-600"
                                    >HTTP, FTP, SMTP</span
                                >
                            </div>
                            <div class="data-box">
                                <div>GET /index.html</div>
                            </div>
                            <div class="arrow arrow-down"></div>
                        </div>

                        <div class="layer-box" id="sender-transport">
                            <div class="flex justify-between">
                                <span class="font-medium">Transport</span>
                                <span class="text-blue-600">TCP, UDP</span>
                            </div>
                            <div class="data-box">
                                <div class="header-row bg-blue-100">
                                    TCP: Port 80, Seq 1001
                                </div>
                                <div>GET /index.html</div>
                            </div>
                            <div class="arrow arrow-down"></div>
                        </div>

                        <div class="layer-box" id="sender-internet">
                            <div class="flex justify-between">
                                <span class="font-medium">Internet</span>
                                <span class="text-blue-600">IP</span>
                            </div>
                            <div class="data-box">
                                <div class="header-row bg-teal-100">
                                    IP: ***********→*************
                                </div>
                                <div class="header-row bg-blue-100">
                                    TCP: Port 80, Seq 1001
                                </div>
                                <div>GET /index.html</div>
                            </div>
                            <div class="arrow arrow-down"></div>
                        </div>

                        <div class="layer-box" id="sender-network">
                            <div class="flex justify-between">
                                <span class="font-medium"
                                    >Network Interface</span
                                >
                                <span class="text-blue-600"
                                    >Ethernet, WiFi</span
                                >
                            </div>
                            <div class="data-box">
                                <div class="header-row bg-green-100">
                                    Ethernet: MAC addresses
                                </div>
                                <div class="header-row bg-teal-100">
                                    IP: ***********→*************
                                </div>
                                <div class="header-row bg-blue-100">
                                    TCP: Port 80, Seq 1001
                                </div>
                                <div>GET /index.html</div>
                            </div>
                        </div>
                    </div>

                    <!-- Network -->
                    <div class="col-span-1 flex items-center">
                        <div class="w-full">
                            <div class="network-line">
                                <div
                                    class="packet"
                                    id="packet"
                                    style="display: none"
                                ></div>
                            </div>
                            <div class="text-center mt-2 text-sm font-medium">
                                Internet
                            </div>
                        </div>
                    </div>

                    <!-- Receiver Computer -->
                    <div
                        class="col-span-2 bg-gray-100 rounded-lg p-3 border-2 border-gray-300"
                    >
                        <h3 class="font-semibold text-center mb-2">
                            Komputer Penerima
                        </h3>

                        <div class="layer-box" id="receiver-network">
                            <div class="flex justify-between">
                                <span class="font-medium"
                                    >Network Interface</span
                                >
                                <span class="text-blue-600"
                                    >Ethernet, WiFi</span
                                >
                            </div>
                            <div
                                class="data-box"
                                id="receiver-network-data"
                                style="display: none"
                            >
                                <div class="header-row bg-green-100">
                                    Ethernet: MAC addresses
                                </div>
                                <div class="header-row bg-teal-100">
                                    IP: ***********→*************
                                </div>
                                <div class="header-row bg-blue-100">
                                    TCP: Port 80, Seq 1001
                                </div>
                                <div>GET /index.html</div>
                            </div>
                            <div class="arrow arrow-up"></div>
                        </div>

                        <div class="layer-box" id="receiver-internet">
                            <div class="flex justify-between">
                                <span class="font-medium">Internet</span>
                                <span class="text-blue-600">IP</span>
                            </div>
                            <div
                                class="data-box"
                                id="receiver-internet-data"
                                style="display: none"
                            >
                                <div class="header-row bg-teal-100">
                                    IP: ***********→*************
                                </div>
                                <div class="header-row bg-blue-100">
                                    TCP: Port 80, Seq 1001
                                </div>
                                <div>GET /index.html</div>
                            </div>
                            <div class="arrow arrow-up"></div>
                        </div>

                        <div class="layer-box" id="receiver-transport">
                            <div class="flex justify-between">
                                <span class="font-medium">Transport</span>
                                <span class="text-blue-600">TCP, UDP</span>
                            </div>
                            <div
                                class="data-box"
                                id="receiver-transport-data"
                                style="display: none"
                            >
                                <div class="header-row bg-blue-100">
                                    TCP: Port 80, Seq 1001
                                </div>
                                <div>GET /index.html</div>
                            </div>
                            <div class="arrow arrow-up"></div>
                        </div>

                        <div class="layer-box" id="receiver-app">
                            <div class="flex justify-between">
                                <span class="font-medium">Aplikasi</span>
                                <span class="text-blue-600"
                                    >HTTP, FTP, SMTP</span
                                >
                            </div>
                            <div
                                class="data-box"
                                id="receiver-app-data"
                                style="display: none"
                            >
                                <div>GET /index.html</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener("DOMContentLoaded", function () {
                // Elements
                const startButton = document.getElementById("start-button");
                const message = document.getElementById("message");
                const packet = document.getElementById("packet");
                const steps = [
                    document.getElementById("step-1"),
                    document.getElementById("step-2"),
                    document.getElementById("step-3"),
                ];

                // Sender layers
                const senderApp = document.getElementById("sender-app");
                const senderTransport =
                    document.getElementById("sender-transport");
                const senderInternet =
                    document.getElementById("sender-internet");
                const senderNetwork = document.getElementById("sender-network");

                // Receiver data
                const receiverNetworkData = document.getElementById(
                    "receiver-network-data",
                );
                const receiverInternetData = document.getElementById(
                    "receiver-internet-data",
                );
                const receiverTransportData = document.getElementById(
                    "receiver-transport-data",
                );
                const receiverAppData =
                    document.getElementById("receiver-app-data");

                // Receiver layers
                const receiverNetwork =
                    document.getElementById("receiver-network");
                const receiverInternet =
                    document.getElementById("receiver-internet");
                const receiverTransport =
                    document.getElementById("receiver-transport");
                const receiverApp = document.getElementById("receiver-app");

                let simulationRunning = false;

                function resetSimulation() {
                    document.querySelectorAll(".layer-box").forEach((layer) => {
                        layer.classList.remove("active");
                    });

                    packet.style.left = "0";
                    packet.style.display = "none";

                    receiverNetworkData.style.display = "none";
                    receiverInternetData.style.display = "none";
                    receiverTransportData.style.display = "none";
                    receiverAppData.style.display = "none";

                    steps.forEach((step) => {
                        step.classList.remove("active", "completed");
                    });

                    message.style.display = "none";
                    message.classList.remove("bg-green-100", "text-green-800");
                    message.classList.add("bg-blue-100", "text-blue-800");
                }

                startButton.addEventListener("click", function () {
                    if (simulationRunning) return;
                    simulationRunning = true;

                    resetSimulation();
                    runSimulation();
                });

                function runSimulation() {
                    // Step 1: Encapsulation
                    steps[0].classList.add("active");
                    message.textContent =
                        "Langkah 1: Enkapsulasi data pada pengirim";
                    message.style.display = "block";

                    // Application Layer
                    setTimeout(() => {
                        senderApp.classList.add("active");
                        message.textContent =
                            "Aplikasi membuat permintaan HTTP";
                    }, 400);

                    // Transport Layer
                    setTimeout(() => {
                        senderApp.classList.remove("active");
                        senderTransport.classList.add("active");
                        message.textContent =
                            "Transport menambahkan header TCP";
                    }, 1400);

                    // Internet Layer
                    setTimeout(() => {
                        senderTransport.classList.remove("active");
                        senderInternet.classList.add("active");
                        message.textContent = "Internet menambahkan header IP";
                    }, 2400);

                    // Network Layer
                    setTimeout(() => {
                        senderInternet.classList.remove("active");
                        senderNetwork.classList.add("active");
                        message.textContent =
                            "Network Interface menambahkan header Ethernet";
                    }, 3400);

                    // Complete Step 1
                    setTimeout(() => {
                        senderNetwork.classList.remove("active");
                        steps[0].classList.remove("active");
                        steps[0].classList.add("completed");
                    }, 4400);

                    // Step 2: Transmission
                    setTimeout(() => {
                        steps[1].classList.add("active");
                        message.textContent =
                            "Langkah 2: Transmisi data melalui jaringan";

                        packet.style.display = "block";
                        setTimeout(() => {
                            packet.style.left = "calc(100% - 20px)";
                        }, 100);
                    }, 4500);

                    // Complete Step 2
                    setTimeout(() => {
                        steps[1].classList.remove("active");
                        steps[1].classList.add("completed");
                    }, 6800);

                    // Step 3: Decapsulation
                    setTimeout(() => {
                        steps[2].classList.add("active");
                        message.textContent =
                            "Langkah 3: Dekapsulasi data pada penerima";

                        receiverNetworkData.style.display = "block";
                        receiverNetwork.classList.add("active");
                    }, 7000);

                    // Internet Layer
                    setTimeout(() => {
                        receiverNetwork.classList.remove("active");
                        receiverInternetData.style.display = "block";
                        receiverInternet.classList.add("active");
                        message.textContent =
                            "Network Interface menghapus header Ethernet";
                    }, 8000);

                    // Transport Layer
                    setTimeout(() => {
                        receiverInternet.classList.remove("active");
                        receiverTransportData.style.display = "block";
                        receiverTransport.classList.add("active");
                        message.textContent = "Internet menghapus header IP";
                    }, 9000);

                    // Application Layer
                    setTimeout(() => {
                        receiverTransport.classList.remove("active");
                        receiverAppData.style.display = "block";
                        receiverApp.classList.add("active");
                        message.textContent = "Transport menghapus header TCP";
                    }, 10000);

                    // Complete Step 3
                    setTimeout(() => {
                        receiverApp.classList.remove("active");
                        steps[2].classList.remove("active");
                        steps[2].classList.add("completed");

                        message.textContent =
                            "Selesai! Data berhasil dikirim ke tujuan";
                        message.classList.remove(
                            "bg-blue-100",
                            "text-blue-800",
                        );
                        message.classList.add("bg-green-100", "text-green-800");

                        simulationRunning = false;
                    }, 11000);
                }
            });
        </script>
    </body>
</html>
