<!doctype html>
<html lang="id">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Si<PERSON>lasi <PERSON> IT</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family: "Inter", sans-serif;
                background-color: #f0f4f8;
            }
            .threat-card {
                background-color: white;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transition:
                    transform 0.3s ease,
                    box-shadow 0.3s ease;
                overflow: hidden;
            }
            .threat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }
            .simulation-area {
                border: 2px dashed #cbd5e1;
                border-radius: 8px;
                padding: 20px;
                min-height: 200px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                text-align: center;
                background-color: #f9fafb;
                position: relative; /* Untuk elemen absolut di dalamnya */
            }
            .btn {
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: 500;
                transition:
                    background-color 0.3s ease,
                    transform 0.2s ease;
                cursor: pointer;
            }
            .btn-primary {
                background-color: #3b82f6;
                color: white;
            }
            .btn-primary:hover {
                background-color: #2563eb;
                transform: translateY(-2px);
            }
            .btn-danger {
                background-color: #ef4444;
                color: white;
            }
            .btn-danger:hover {
                background-color: #dc2626;
                transform: translateY(-2px);
            }
            .message-box {
                padding: 12px;
                border-radius: 8px;
                margin-top: 15px;
                font-size: 0.9rem;
                text-align: center;
            }
            .message-box code {
                background-color: #e0e0e0;
                padding: 2px 4px;
                border-radius: 4px;
                font-family: monospace;
            }
            .message-success {
                background-color: #d1fae5;
                color: #065f46;
                border: 1px solid #6ee7b7;
            }
            .message-error {
                background-color: #fee2e2;
                color: #991b1b;
                border: 1px solid #fca5a5;
            }
            .message-warning {
                background-color: #ffedd5;
                color: #9a3412;
                border: 1px solid #fdba74;
            }
            .message-info {
                background-color: #e0e7ff;
                color: #3730a3;
                border: 1px solid #a5b4fc;
            }
            .message-box.text-left {
                text-align: left;
            }

            /* Style untuk DNS Spoofing */
            .dns-flow-container {
                width: 100%;
                padding: 10px;
            }
            .dns-route {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .dns-node {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 5px;
            }
            .dns-node .icon {
                font-size: 1.5rem;
                margin-bottom: 5px;
            }
            .dns-node span:last-child {
                font-size: 0.7rem;
            }
            .dns-path {
                height: 3px;
                background-color: #9ca3af;
                flex-grow: 1;
                margin: 0 5px;
            }
            .dns-node.web-server.spoofed {
                position: absolute;
                right: 0;
                bottom: -40px;
                color: #ef4444;
            }
            .dns-packet {
                position: absolute;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background-color: #3b82f6;
                z-index: 10;
            }

            /* Style untuk Carding */
            .credit-card {
                width: 100%;
                max-width: 300px;
                height: 180px;
                background: linear-gradient(135deg, #3a7bd5, #00d2ff);
                border-radius: 16px;
                padding: 20px;
                color: white;
                position: relative;
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                font-family: "Courier New", monospace;
            }
            .card-chip {
                width: 50px;
                height: 40px;
                background-color: #ffda7a;
                border-radius: 8px;
                margin-bottom: 20px;
            }
            .card-number {
                font-size: 1.2rem;
                letter-spacing: 2px;
                margin-bottom: 15px;
            }
            .card-details {
                display: flex;
                justify-content: space-between;
                font-size: 0.8rem;
                text-transform: uppercase;
            }
            .stolen-data {
                position: absolute;
                padding: 10px;
                border-radius: 8px;
                background-color: #fee2e2;
                border: 1px solid #ef4444;
                font-size: 0.8rem;
                z-index: 50;
                opacity: 0;
                transition: opacity 0.5s ease;
            }

            /* Style untuk SIM Swap */
            .phone-container {
                position: relative;
                width: 80px;
                height: 150px;
                background-color: #1f2937;
                border-radius: 12px;
                padding: 8px;
                display: flex;
                flex-direction: column;
            }
            .phone-screen {
                flex-grow: 1;
                background-color: #374151;
                border-radius: 4px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: white;
                font-size: 1.5rem;
                overflow: hidden;
            }
            .phone-button {
                height: 12px;
                width: 12px;
                background-color: #9ca3af;
                border-radius: 50%;
                margin: 8px auto 0;
            }
            .sim-card {
                position: absolute;
                width: 30px;
                height: 20px;
                background-color: #fbbf24;
                border-radius: 4px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 0.5rem;
                color: #78350f;
                font-weight: bold;
                z-index: 20;
                transition: transform 1s ease;
            }

            /* Style untuk Data Forgery */
            .document-container {
                background-color: #fff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                width: 100%;
                max-width: 280px;
                padding: 15px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            }
            .document-header {
                border-bottom: 2px solid #3b82f6;
                padding-bottom: 10px;
                margin-bottom: 10px;
                font-weight: bold;
                color: #1e40af;
            }
            .document-field {
                display: flex;
                margin-bottom: 8px;
            }
            .document-field-label {
                width: 100px;
                font-size: 0.8rem;
                color: #6b7280;
            }
            .document-field-value {
                flex-grow: 1;
                font-size: 0.9rem;
            }
            .modified-field {
                background-color: #fef9c3;
                border-radius: 4px;
                padding: 2px 4px;
                position: relative;
            }
            .original-value {
                position: absolute;
                top: -20px;
                left: 0;
                background-color: #e0f2fe;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 0.7rem;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 20;
            }
            .modified-field:hover .original-value {
                opacity: 1;
            }

            /* Style untuk Skimming */
            .atm-container {
                width: 180px;
                height: 240px;
                background-color: #e5e7eb;
                border-radius: 8px;
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .atm-screen {
                width: 140px;
                height: 80px;
                background-color: #1e293b;
                color: #10b981;
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: monospace;
                margin-bottom: 10px;
                font-size: 0.8rem;
                border-radius: 4px;
            }
            .atm-keypad {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-gap: 5px;
                margin-bottom: 10px;
            }
            .atm-key {
                width: 25px;
                height: 25px;
                background-color: #9ca3af;
                color: white;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 4px;
                font-size: 0.7rem;
                font-weight: bold;
            }
            .atm-cardslot {
                width: 100px;
                height: 10px;
                background-color: #1f2937;
                position: relative;
                border-radius: 2px;
            }
            .atm-card {
                width: 90px;
                height: 6px;
                background-color: #3b82f6;
                position: absolute;
                top: 2px;
                left: 5px;
                border-radius: 1px;
                transform-origin: left center;
                transition: transform 1s ease;
            }
            .skimmer-device {
                position: absolute;
                top: 129px; /* Posisikan di atas card slot */
                width: 104px;
                height: 14px;
                background-color: #ef4444;
                border-radius: 3px;
                opacity: 0;
                transition: opacity 0.5s ease;
                z-index: 10;
            }

            /* Style untuk Cyber Terrorism */
            .infrastructure-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-gap: 10px;
                width: 100%;
                max-width: 300px;
            }
            .infrastructure-node {
                height: 60px;
                background-color: #f3f4f6;
                border-radius: 8px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                font-size: 0.7rem;
                position: relative;
                transition: background-color 0.5s ease;
            }
            .infrastructure-node .icon {
                font-size: 1.5rem;
                margin-bottom: 4px;
            }
            .infrastructure-node.affected {
                background-color: #fee2e2;
                color: #b91c1c;
                animation: shake 0.5s ease-in-out;
            }
            .attack-line {
                position: absolute;
                height: 2px;
                background-color: #ef4444;
                transform-origin: left center;
                z-index: 5;
                opacity: 0;
            }
            .attack-pulse {
                position: absolute;
                width: 10px;
                height: 10px;
                background-color: #ef4444;
                border-radius: 50%;
                transform: translate(-50%, -50%);
                z-index: 6;
                opacity: 0;
            }
            @keyframes pulse {
                0% {
                    transform: translate(-50%, -50%) scale(0.5);
                    opacity: 1;
                }
                100% {
                    transform: translate(-50%, -50%) scale(1.5);
                    opacity: 0;
                }
            }
            @keyframes shake {
                0%,
                100% {
                    transform: translateX(0);
                }
                25% {
                    transform: translateX(-3px);
                }
                50% {
                    transform: translateX(3px);
                }
                75% {
                    transform: translateX(-3px);
                }
            }
        </style>
    </head>
    <body class="p-4 md:p-8">
        <header class="text-center mb-10">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800">
                Simulasi Lanjutan Ancaman Keamanan IT
            </h1>
            <p class="text-gray-600 mt-2">
                Pelajari tentang berbagai ancaman keamanan siber modern secara
                visual dan interaktif.
            </p>
            <div class="mt-6">
                <a
                    href="threats.html"
                    class="text-blue-600 hover:text-blue-800 font-medium"
                    >← Kembali ke Simulasi Dasar</a
                >
                <span class="mx-3">|</span>
                <a
                    href="threats-3.html"
                    class="text-blue-600 hover:text-blue-800 font-medium"
                    >Lanjut ke Simulasi Lanjutan (Bagian 3) →</a
                >
            </div>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- DNS Spoofing Simulation Card -->
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        🔄 DNS Spoofing
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        DNS Spoofing mengubah cache DNS untuk mengarahkan korban
                        ke situs palsu yang tampak identik dengan situs asli,
                        mengakibatkan pencurian data sensitif.
                    </p>
                    <div class="simulation-area" id="dnsspoof-sim">
                        <div class="dns-flow-container mb-4 relative">
                            <div id="dns-route" class="dns-route">
                                <div class="dns-node user">
                                    <span class="icon">🧑‍💻</span>
                                    <span>Anda</span>
                                </div>
                                <div class="dns-path"></div>
                                <div class="dns-node dns-server">
                                    <span class="icon">🔍</span>
                                    <span>DNS Server</span>
                                </div>
                                <div class="dns-path"></div>
                                <div class="dns-node web-server original">
                                    <span class="icon">🏦</span>
                                    <span>bankasli.com</span>
                                </div>
                            </div>
                            <div
                                id="dns-spoofed-server"
                                class="dns-node web-server spoofed"
                                style="display: none"
                            >
                                <span class="icon">🕵️</span>
                                <span>bankasli-palsu.com</span>
                            </div>
                        </div>
                        <button
                            class="btn btn-primary"
                            onclick="simulateDNSSpoofing()"
                        >
                            Akses bankasli.com
                        </button>
                        <div
                            id="dnsspoof-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Carding Simulation Card -->
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        💳 Carding
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Carding adalah pencurian dan penyalahgunaan informasi
                        kartu kredit untuk melakukan penipuan finansial, baik
                        melalui kebocoran data online atau perangkat skimming.
                    </p>
                    <div class="simulation-area" id="carding-sim">
                        <div class="credit-card mb-4">
                            <div class="card-chip"></div>
                            <div class="card-number" id="card-number">
                                4539 1488 0000 0001
                            </div>
                            <div class="card-details">
                                <div>
                                    <div class="text-xs mb-1">CARD HOLDER</div>
                                    <div>JOHN DOE</div>
                                </div>
                                <div>
                                    <div class="text-xs mb-1">EXPIRES</div>
                                    <div>05/25</div>
                                </div>
                            </div>
                        </div>
                        <div id="card-stolen-data" class="stolen-data">
                            Number: 4539 1488 0000 0001<br />
                            CVV: 123<br />
                            Exp: 05/25
                        </div>
                        <button
                            class="btn btn-danger"
                            onclick="simulateCarding()"
                        >
                            Kunjungi Situs Tidak Aman
                        </button>
                        <div
                            id="carding-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- SIM Swap Simulation Card -->
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        📱 SIM Swap
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        SIM Swap adalah penipuan di mana penyerang mengelabui
                        operator untuk memindahkan nomor telepon korban ke kartu
                        SIM yang dikontrol penyerang, mengakali 2FA dan
                        mengakses akun.
                    </p>
                    <div class="simulation-area" id="simswap-sim">
                        <div
                            class="flex justify-between items-center w-full max-w-xs mb-4"
                        >
                            <div class="phone-container" id="user-phone">
                                <div class="phone-screen">📱</div>
                                <div class="phone-button"></div>
                                <div class="sim-card" id="original-sim">
                                    SIM
                                </div>
                            </div>
                            <div class="mx-4 text-gray-400">→</div>
                            <div class="text-center">
                                <div class="text-4xl mb-2">🏪</div>
                                <div class="text-xs">Mobile Provider</div>
                            </div>
                            <div class="mx-4 text-gray-400">→</div>
                            <div class="phone-container" id="attacker-phone">
                                <div class="phone-screen">😈</div>
                                <div class="phone-button"></div>
                            </div>
                        </div>
                        <button
                            class="btn btn-primary"
                            onclick="simulateSIMSwap()"
                        >
                            Lakukan SIM Swap
                        </button>
                        <div
                            id="simswap-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Data Forgery Simulation Card -->
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        📝 Data Forgery
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Data Forgery adalah pemalsuan data digital seperti
                        dokumen atau catatan untuk tujuan penipuan, termasuk
                        pemalsuan identitas dan dokumen resmi.
                    </p>
                    <div class="simulation-area" id="forgery-sim">
                        <div class="document-container">
                            <div class="document-header">KARTU IDENTITAS</div>
                            <div class="document-field">
                                <div class="document-field-label">Nama:</div>
                                <div class="document-field-value">John Doe</div>
                            </div>
                            <div class="document-field">
                                <div class="document-field-label">NIK:</div>
                                <div class="document-field-value">
                                    3271046504870002
                                </div>
                            </div>
                            <div class="document-field">
                                <div class="document-field-label">
                                    Tempat/Tgl Lahir:
                                </div>
                                <div class="document-field-value">
                                    Jakarta, 05-04-1987
                                </div>
                            </div>
                            <div class="document-field">
                                <div class="document-field-label">
                                    Pekerjaan:
                                </div>
                                <div
                                    class="document-field-value"
                                    id="forgery-occupation"
                                >
                                    Karyawan Swasta
                                </div>
                            </div>
                            <div class="document-field">
                                <div class="document-field-label">
                                    Penghasilan:
                                </div>
                                <div
                                    class="document-field-value"
                                    id="forgery-income"
                                >
                                    Rp 5.000.000
                                </div>
                            </div>
                        </div>
                        <button
                            class="btn btn-danger mt-4"
                            onclick="simulateDataForgery()"
                        >
                            Palsukan Data
                        </button>
                        <div
                            id="forgery-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Skimming Simulation Card -->
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        🏧 Skimming
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Skimming adalah pemasangan perangkat ilegal pada mesin
                        ATM atau POS untuk mencuri informasi kartu pembayaran
                        ketika korban melakukan transaksi.
                    </p>
                    <div class="simulation-area" id="skimming-sim">
                        <div class="atm-container mb-4">
                            <div class="atm-screen" id="atm-screen">
                                MASUKKAN KARTU
                            </div>
                            <div class="atm-keypad">
                                <div class="atm-key">1</div>
                                <div class="atm-key">2</div>
                                <div class="atm-key">3</div>
                                <div class="atm-key">4</div>
                                <div class="atm-key">5</div>
                                <div class="atm-key">6</div>
                                <div class="atm-key">7</div>
                                <div class="atm-key">8</div>
                                <div class="atm-key">9</div>
                                <div class="atm-key">*</div>
                                <div class="atm-key">0</div>
                                <div class="atm-key">#</div>
                            </div>
                            <div class="atm-cardslot">
                                <div class="atm-card" id="atm-card"></div>
                            </div>
                            <div
                                class="skimmer-device"
                                id="skimmer-device"
                            ></div>
                        </div>
                        <div class="flex gap-4">
                            <button
                                class="btn btn-danger"
                                onclick="installSkimmer()"
                            >
                                Pasang Skimmer
                            </button>
                            <button
                                class="btn btn-primary"
                                onclick="simulateSkimming()"
                            >
                                Gunakan ATM
                            </button>
                        </div>
                        <div
                            id="skimming-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Cyber Terrorism Simulation Card -->
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        💥 Cyber Terrorism
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Cyber Terrorism adalah serangan berbahaya terhadap
                        infrastruktur kritis suatu negara melalui internet,
                        bertujuan menciptakan kerusakan, ketakutan, dan
                        kekacauan massal.
                    </p>
                    <div class="simulation-area" id="cyberterror-sim">
                        <div class="infrastructure-grid mb-4">
                            <div
                                class="infrastructure-node"
                                data-target="power"
                            >
                                <span class="icon">⚡</span>
                                <span>Pembangkit Listrik</span>
                            </div>
                            <div
                                class="infrastructure-node"
                                data-target="water"
                            >
                                <span class="icon">💧</span>
                                <span>Pasokan Air</span>
                            </div>
                            <div
                                class="infrastructure-node"
                                data-target="hospital"
                            >
                                <span class="icon">🏥</span>
                                <span>Rumah Sakit</span>
                            </div>
                            <div
                                class="infrastructure-node"
                                data-target="traffic"
                            >
                                <span class="icon">🚦</span>
                                <span>Lalu Lintas</span>
                            </div>
                            <div
                                class="infrastructure-node"
                                data-target="finance"
                            >
                                <span class="icon">🏦</span>
                                <span>Sistem Keuangan</span>
                            </div>
                            <div
                                class="infrastructure-node"
                                data-target="comms"
                            >
                                <span class="icon">📡</span>
                                <span>Telekomunikasi</span>
                            </div>
                        </div>
                        <button
                            class="btn btn-danger"
                            onclick="simulateCyberTerrorism()"
                        >
                            Luncurkan Serangan
                        </button>
                        <div
                            id="cyberterror-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="text-center mt-12 py-6 border-t border-gray-300">
            <p class="text-sm text-gray-600">
                &copy; 2024 Simulasi Keamanan IT. Dibuat untuk tujuan edukasi.
            </p>
        </footer>

        <script>
            // Simulasi DNS Spoofing
            function simulateDNSSpoofing() {
                const dnsRoute = document.getElementById("dns-route");
                const dnsNodes = dnsRoute.querySelectorAll(".dns-node");
                const originalServer = dnsRoute.querySelector(
                    ".dns-node.web-server.original",
                );
                const spoofedServer =
                    document.getElementById("dns-spoofed-server");
                const messageDiv = document.getElementById("dnsspoof-message");
                const simArea = document.getElementById("dnsspoof-sim");

                messageDiv.style.display = "none";
                spoofedServer.style.display = "none";

                // Packet animation function
                function createPacket(
                    start,
                    end,
                    color = "#3b82f6",
                    callback = null,
                ) {
                    const packet = document.createElement("div");
                    packet.className = "dns-packet";
                    packet.style.backgroundColor = color;

                    const startRect = start.getBoundingClientRect();
                    const endRect = end.getBoundingClientRect();
                    const simRect = simArea.getBoundingClientRect();

                    // Posisi awal relatif terhadap simulation area
                    packet.style.left = `${startRect.left - simRect.left + startRect.width / 2 - 6}px`;
                    packet.style.top = `${startRect.top - simRect.top + startRect.height / 2 - 6}px`;
                    simArea.appendChild(packet);

                    // Animate
                    packet.style.transition = "left 1s, top 1s";
                    setTimeout(() => {
                        packet.style.left = `${endRect.left - simRect.left + endRect.width / 2 - 6}px`;
                        packet.style.top = `${endRect.top - simRect.top + endRect.height / 2 - 6}px`;

                        setTimeout(() => {
                            packet.remove();
                            if (callback) callback();
                        }, 1000);
                    }, 50);
                }

                // Start simulation
                messageDiv.innerHTML = "Anda mencoba mengakses bankasli.com...";
                messageDiv.className = "message-box message-info";
                messageDiv.style.display = "block";

                // Step 1: User to DNS server
                setTimeout(() => {
                    createPacket(dnsNodes[0], dnsNodes[1]);
                    messageDiv.innerHTML =
                        'Mengirim permintaan DNS: "Di mana alamat bankasli.com?"';
                }, 500);

                // Step 2: Attacker intercepts
                setTimeout(() => {
                    spoofedServer.style.display = "flex";
                    messageDiv.innerHTML =
                        "⚠️ Penyerang telah mengubah cache DNS!";
                    messageDiv.className = "message-box message-warning";
                }, 2000);

                // Step 3: DNS server responds with spoofed address
                setTimeout(() => {
                    createPacket(dnsNodes[1], dnsNodes[0], "#ef4444");
                    messageDiv.innerHTML =
                        '🚨 DNS server mengirim alamat palsu: "bankasli.com ada di 203.0.113.x" (padahal itu alamat server palsu)';
                    messageDiv.className = "message-box message-error";
                }, 3000);

                // Step 4: User connects to fake server
                setTimeout(() => {
                    originalServer.style.opacity = "0.5";
                    createPacket(dnsNodes[0], spoofedServer, "#ef4444");
                    messageDiv.innerHTML =
                        "🚨 Anda terhubung ke situs palsu yang terlihat seperti bankasli.com, tetapi sebenarnya dikendalikan oleh penyerang!";
                }, 4500);

                // Reset
                setTimeout(() => {
                    spoofedServer.style.display = "none";
                    originalServer.style.opacity = "1";
                    messageDiv.style.display = "none";
                }, 10000);
            }

            // Simulasi Carding
            function simulateCarding() {
                const messageDiv = document.getElementById("carding-message");
                const stolenDataDiv =
                    document.getElementById("card-stolen-data");
                const creditCard = document.querySelector(".credit-card");

                messageDiv.style.display = "none";
                stolenDataDiv.style.opacity = "0";

                messageDiv.innerHTML =
                    "Mengunjungi situs e-commerce yang tidak aman...";
                messageDiv.className = "message-box message-info";
                messageDiv.style.display = "block";

                setTimeout(() => {
                    messageDiv.innerHTML =
                        "⚠️ Website ini tidak menggunakan enkripsi yang aman untuk transaksi!";
                    messageDiv.className = "message-box message-warning";

                    // Animasi data kartu yang dicuri
                    const cardRect = creditCard.getBoundingClientRect();
                    const simArea = document
                        .getElementById("carding-sim")
                        .getBoundingClientRect();

                    stolenDataDiv.style.top = `${cardRect.top - simArea.top + 50}px`;
                    stolenDataDiv.style.left = `${cardRect.left - simArea.left + 50}px`;
                    stolenDataDiv.style.opacity = "1";

                    setTimeout(() => {
                        // Animasi data kartu berpindah ke "hacker"
                        stolenDataDiv.style.transition = "all 1s ease";
                        stolenDataDiv.style.transform =
                            "translate(80px, 80px) scale(0.8)";

                        // Ubah pesan
                        messageDiv.innerHTML =
                            "🚨 Penyerang telah mencuri informasi kartu kredit Anda melalui situs yang tidak aman!";
                        messageDiv.className = "message-box message-error";
                    }, 1500);

                    // Reset simulasi
                    setTimeout(() => {
                        stolenDataDiv.style.opacity = "0";
                        stolenDataDiv.style.transform =
                            "translate(0, 0) scale(1)";
                        messageDiv.style.display = "none";
                    }, 8000);
                }, 1000);
            }

            // Simulasi SIM Swap
            function simulateSIMSwap() {
                const messageDiv = document.getElementById("simswap-message");
                const originalSim = document.getElementById("original-sim");
                const attackerPhone = document.getElementById("attacker-phone");

                messageDiv.style.display = "none";

                // Reset state
                originalSim.style.transform = "";
                if (document.getElementById("attacker-sim")) {
                    document.getElementById("attacker-sim").remove();
                }

                // Start simulation
                messageDiv.innerHTML =
                    "Penyerang menghubungi operator dengan informasi pribadi korban yang dicuri...";
                messageDiv.className = "message-box message-info";
                messageDiv.style.display = "block";

                setTimeout(() => {
                    messageDiv.innerHTML =
                        "⚠️ Operator tertipu dan menyetujui perpindahan nomor!";
                    messageDiv.className = "message-box message-warning";

                    // Animasi SIM card berpindah
                    originalSim.style.transition = "transform 1.5s ease";
                    originalSim.style.transform = "translateX(200px)";

                    setTimeout(() => {
                        // SIM card sampai di phone attacker
                        originalSim.style.opacity = "0";

                        // Buat SIM baru di phone attacker
                        const attackerSim = document.createElement("div");
                        attackerSim.id = "attacker-sim";
                        attackerSim.className = "sim-card";
                        attackerSim.textContent = "SIM";
                        attackerSim.style.backgroundColor = "#fbbf24";
                        attackerPhone.appendChild(attackerSim);

                        messageDiv.innerHTML =
                            "🚨 Penyerang sekarang menerima semua SMS dan panggilan Anda, termasuk kode 2FA!";
                        messageDiv.className = "message-box message-error";
                    }, 1500);

                    // Reset simulasi
                    setTimeout(() => {
                        if (document.getElementById("attacker-sim")) {
                            document.getElementById("attacker-sim").remove();
                        }
                        originalSim.style.transform = "";
                        originalSim.style.opacity = "1";
                        messageDiv.style.display = "none";
                    }, 8000);
                }, 1500);
            }

            // Simulasi Data Forgery
            function simulateDataForgery() {
                const messageDiv = document.getElementById("forgery-message");
                const occupationField =
                    document.getElementById("forgery-occupation");
                const incomeField = document.getElementById("forgery-income");

                messageDiv.style.display = "none";

                // Reset state
                if (occupationField.classList.contains("modified-field")) {
                    occupationField.classList.remove("modified-field");
                    occupationField.textContent = "Karyawan Swasta";
                    occupationField.innerHTML = "Karyawan Swasta";
                }

                if (incomeField.classList.contains("modified-field")) {
                    incomeField.classList.remove("modified-field");
                    incomeField.textContent = "Rp 5.000.000";
                    incomeField.innerHTML = "Rp 5.000.000";
                }

                // Start simulation
                messageDiv.innerHTML = "Memulai proses pemalsuan dokumen...";
                messageDiv.className = "message-box message-info";
                messageDiv.style.display = "block";

                setTimeout(() => {
                    // Modifikasi dokumen
                    occupationField.classList.add("modified-field");
                    occupationField.innerHTML =
                        'Direktur Perusahaan<span class="original-value">Asli: Karyawan Swasta</span>';

                    messageDiv.innerHTML =
                        "⚠️ Data pekerjaan telah dipalsukan!";
                    messageDiv.className = "message-box message-warning";

                    setTimeout(() => {
                        incomeField.classList.add("modified-field");
                        incomeField.innerHTML =
                            'Rp 50.000.000<span class="original-value">Asli: Rp 5.000.000</span>';

                        messageDiv.innerHTML =
                            "🚨 Dokumen resmi telah dipalsukan untuk kepentingan penipuan kredit!";
                        messageDiv.className = "message-box message-error";
                    }, 1500);

                    // Reset simulation after delay
                    setTimeout(() => {
                        messageDiv.style.display = "none";
                    }, 8000);
                }, 1000);
            }

            // Variable untuk tracking status skimmer
            let skimmerInstalled = false;

            // Fungsi untuk memasang skimmer
            function installSkimmer() {
                const skimmerDevice = document.getElementById("skimmer-device");
                const messageDiv = document.getElementById("skimming-message");

                if (skimmerInstalled) {
                    messageDiv.innerHTML = "Skimmer sudah terpasang!";
                    messageDiv.className = "message-box message-warning";
                    messageDiv.style.display = "block";

                    setTimeout(() => {
                        messageDiv.style.display = "none";
                    }, 2000);
                    return;
                }

                messageDiv.innerHTML = "Memasang perangkat skimmer pada ATM...";
                messageDiv.className = "message-box message-info";
                messageDiv.style.display = "block";

                setTimeout(() => {
                    skimmerDevice.style.opacity = "0.7";
                    skimmerInstalled = true;

                    messageDiv.innerHTML =
                        "⚠️ Skimmer terpasang dan siap mencuri data kartu!";
                    messageDiv.className = "message-box message-warning";

                    setTimeout(() => {
                        messageDiv.style.display = "none";
                    }, 3000);
                }, 1000);
            }

            // Simulasi Skimming
            function simulateSkimming() {
                const messageDiv = document.getElementById("skimming-message");
                const atmCard = document.getElementById("atm-card");
                const atmScreen = document.getElementById("atm-screen");

                messageDiv.style.display = "none";

                // Kartu masuk ke ATM
                atmCard.style.transform = "translateX(25px)";
                atmScreen.textContent = "MEMBACA KARTU...";

                setTimeout(() => {
                    atmScreen.textContent = "MASUKKAN PIN";

                    if (skimmerInstalled) {
                        messageDiv.innerHTML =
                            "⚠️ Skimmer sedang mencuri data magnetic stripe kartu Anda!";
                        messageDiv.className = "message-box message-warning";
                        messageDiv.style.display = "block";

                        setTimeout(() => {
                            messageDiv.innerHTML =
                                "🚨 Data kartu Anda telah dicuri! Penyerang dapat membuat kartu duplikat untuk transaksi penipuan.";
                            messageDiv.className = "message-box message-error";
                        }, 2000);
                    } else {
                        messageDiv.innerHTML =
                            "Transaksi aman, tidak ada skimmer terdeteksi.";
                        messageDiv.className = "message-box message-success";
                        messageDiv.style.display = "block";
                    }

                    // Kartu keluar dari ATM
                    setTimeout(() => {
                        atmCard.style.transform = "";
                        atmScreen.textContent = "MASUKKAN KARTU";

                        setTimeout(() => {
                            messageDiv.style.display = "none";
                        }, 3000);
                    }, 3000);
                }, 1500);
            }

            // Simulasi Cyber Terrorism
            function simulateCyberTerrorism() {
                const messageDiv = document.getElementById(
                    "cyberterror-message",
                );
                const nodes = document.querySelectorAll(".infrastructure-node");
                const simArea = document.getElementById("cyberterror-sim");

                messageDiv.style.display = "none";

                // Reset state
                nodes.forEach((node) => {
                    node.classList.remove("affected");
                });

                const attackLines = document.querySelectorAll(
                    ".attack-line, .attack-pulse",
                );
                attackLines.forEach((line) => line.remove());

                // Mulai simulasi
                messageDiv.innerHTML =
                    "Memulai serangan terhadap infrastruktur kritis...";
                messageDiv.className = "message-box message-info";
                messageDiv.style.display = "block";

                // Fungsi untuk menambahkan garis serangan
                function addAttackLine(sourceNode, targetNode) {
                    const sourceRect = sourceNode.getBoundingClientRect();
                    const targetRect = targetNode.getBoundingClientRect();
                    const simRect = simArea.getBoundingClientRect();

                    const x1 =
                        sourceRect.left + sourceRect.width / 2 - simRect.left;
                    const y1 =
                        sourceRect.top + sourceRect.height / 2 - simRect.top;
                    const x2 =
                        targetRect.left + targetRect.width / 2 - simRect.left;
                    const y2 =
                        targetRect.top + targetRect.height / 2 - simRect.top;

                    // Hitung panjang dan sudut
                    const length = Math.sqrt(
                        Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2),
                    );
                    const angle =
                        (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI;

                    // Buat garis serangan
                    const line = document.createElement("div");
                    line.className = "attack-line";
                    line.style.width = `${length}px`;
                    line.style.left = `${x1}px`;
                    line.style.top = `${y1}px`;
                    line.style.transform = `rotate(${angle}deg)`;

                    simArea.appendChild(line);

                    // Animasi garis muncul
                    setTimeout(() => {
                        line.style.opacity = "1";

                        // Tambahkan efek pulse
                        const pulse = document.createElement("div");
                        pulse.className = "attack-pulse";
                        pulse.style.left = `${x1 + length / 2}px`;
                        pulse.style.top = `${y1}px`;
                        pulse.style.animation = "pulse 1s infinite";
                        simArea.appendChild(pulse);
                        pulse.style.opacity = "1";

                        // Serang target
                        setTimeout(() => {
                            targetNode.classList.add("affected");
                        }, 500);
                    }, 100);
                }

                // Serangan bertahap
                setTimeout(() => {
                    // Serangan pertama: power grid
                    const powerNode = document.querySelector(
                        '.infrastructure-node[data-target="power"]',
                    );
                    addAttackLine(
                        document.querySelector(
                            '.infrastructure-node[data-target="comms"]',
                        ),
                        powerNode,
                    );

                    messageDiv.innerHTML =
                        "⚠️ Sistem pembangkit listrik diserang!";
                    messageDiv.className = "message-box message-warning";

                    setTimeout(() => {
                        // Serangan kedua: lebih banyak target
                        addAttackLine(
                            powerNode,
                            document.querySelector(
                                '.infrastructure-node[data-target="water"]',
                            ),
                        );
                        addAttackLine(
                            powerNode,
                            document.querySelector(
                                '.infrastructure-node[data-target="traffic"]',
                            ),
                        );

                        messageDiv.innerHTML =
                            "🚨 Sistem lalu lintas dan pasokan air terganggu! Terjadi pemadaman listrik di beberapa wilayah!";
                        messageDiv.className = "message-box message-error";

                        setTimeout(() => {
                            // Serangan ketiga: semua sistem
                            addAttackLine(
                                document.querySelector(
                                    '.infrastructure-node[data-target="water"]',
                                ),
                                document.querySelector(
                                    '.infrastructure-node[data-target="hospital"]',
                                ),
                            );
                            addAttackLine(
                                document.querySelector(
                                    '.infrastructure-node[data-target="traffic"]',
                                ),
                                document.querySelector(
                                    '.infrastructure-node[data-target="finance"]',
                                ),
                            );

                            messageDiv.innerHTML =
                                "🚨 SERANGAN MASIF! Sistem rumah sakit dan keuangan lumpuh! Terjadi kepanikan masyarakat dan kerugian ekonomi!";
                            messageDiv.className = "message-box message-error";
                        }, 2000);
                    }, 2000);
                }, 1000);

                // Reset simulation
                setTimeout(() => {
                    nodes.forEach((node) => {
                        node.classList.remove("affected");
                    });

                    const attackElements = document.querySelectorAll(
                        ".attack-line, .attack-pulse",
                    );
                    attackElements.forEach((el) => el.remove());

                    messageDiv.style.display = "none";
                }, 10000);
            }
        </script>
    </body>
</html>
