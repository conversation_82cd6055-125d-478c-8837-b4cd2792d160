<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Lanjutan Ancaman Keamanan IT (Bagian 3)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
        }
        .threat-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        .threat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        .simulation-area {
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            padding: 20px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            background-color: #f9fafb;
            position: relative; /* Untuk elemen absolut di dalamnya */
        }
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.3s ease, transform 0.2s ease;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2563eb;
            transform: translateY(-2px);
        }
        .btn-danger {
            background-color: #ef4444;
            color: white;
        }
        .btn-danger:hover {
            background-color: #dc2626;
            transform: translateY(-2px);
        }
        .message-box {
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
            text-align: center; 
        }
        .message-box code {
            background-color: #e0e0e0;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: monospace;
        }
        .message-success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #6ee7b7;
        }
        .message-error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .message-warning {
            background-color: #ffedd5;
            color: #9a3412;
            border: 1px solid #fdba74;
        }
        .message-info {
            background-color: #e0e7ff;
            color: #3730a3;
            border: 1px solid #a5b4fc;
        }
        .message-box.text-left {
            text-align: left;
        }

        /* Style untuk Web Defacement */
        .website-container {
            width: 100%;
            max-width: 320px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            background-color: white;
            position: relative;
        }
        .website-header {
            background-color: #1e40af;
            color: white;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .website-content {
            padding: 15px;
            min-height: 200px;
            transition: all 0.8s ease;
        }
        .website-item {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            background-color: #f3f4f6;
        }
        .defaced-content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            color: #ff2d20;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 50;
            opacity: 0;
            transition: opacity 0.5s ease;
            padding: 20px;
            pointer-events: none;
        }
        .defaced-content img {
            max-width: 80%;
            margin-bottom: 15px;
        }
        .skull-icon {
            font-size: 3rem;
            margin-bottom: 10px;
            animation: pulse 1.5s infinite alternate;
        }
        @keyframes pulse {
            from { transform: scale(0.8); }
            to { transform: scale(1.2); }
        }

        /* Style untuk Data Theft */
        .server-database {
            width: 180px;
            height: 200px;
            background-color: #e5e7eb;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .database-icon {
            font-size: 4rem;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        .data-packet {
            position: absolute;
            width: 40px;
            height: 25px;
            background-color: #3b82f6;
            color: white;
            font-size: 0.7rem;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            z-index: 10;
            transition: transform 1.5s ease, opacity 0.3s ease;
        }
        .hacker-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }
        .hacker-icon {
            font-size: 3rem;
            margin-top: 20px;
        }
        .data-collection {
            width: 200px;
            min-height: 80px;
            background-color: #fee2e2;
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.8rem;
            text-align: left;
            display: flex;
            flex-direction: column;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        .data-item {
            background-color: white;
            padding: 4px 8px;
            border-radius: 4px;
            color: #1f2937;
        }

        /* Style untuk Data Leak */
        .data-leak-container {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        .leak-stages {
            display: flex;
            justify-content: space-between;
            width: 100%;
            max-width: 300px;
        }
        .leak-stage {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 80px;
        }
        .leak-stage-icon {
            font-size: 2rem;
            margin-bottom: 5px;
            opacity: 0.3;
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        .leak-stage-name {
            font-size: 0.7rem;
            text-align: center;
            color: #6b7280;
        }
        .leak-stage.active .leak-stage-icon {
            opacity: 1;
            transform: scale(1.2);
        }
        .leak-stage.active .leak-stage-name {
            color: #1f2937;
            font-weight: bold;
        }
        .data-breach-display {
            width: 100%;
            max-width: 280px;
            min-height: 120px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 10px;
            background-color: white;
            font-family: monospace;
            font-size: 0.8rem;
            text-align: left;
            overflow: hidden;
        }
        .data-row {
            padding: 3px 0;
            border-bottom: 1px solid #f3f4f6;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        .data-row.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body class="p-4 md:p-8">
    <header class="text-center mb-10">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-800">Simulasi Lanjutan Ancaman Keamanan IT (Bagian 3)</h1>
        <p class="text-gray-600 mt-2">Halaman 3: Pelajari tentang ancaman keamanan siber modern lainnya secara visual dan interaktif.</p>
        <div class="mt-6">
            <a href="threats-2.html" class="text-blue-600 hover:text-blue-800 font-medium">← Kembali ke Simulasi Lanjutan</a>
        </div>
    </header>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Web Defacement Simulation Card -->
        <div class="threat-card">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-3">🌐 Web Defacement</h2>
                <p class="text-sm text-gray-600 mb-4">Web Defacement adalah serangan yang mengubah tampilan visual website tanpa izin pemilik, biasanya untuk menyebarkan pesan politik, religius, atau hanya untuk pamer kemampuan.</p>
                <div class="simulation-area" id="defacement-sim">
                    <div class="website-container relative">
                        <div class="website-header">
                            <div>mywebsite.com</div>
                            <div>≡</div>
                        </div>
                        <div class="website-content" id="original-content">
                            <h3 class="font-bold text-lg mb-3">Selamat Datang di Website Kami</h3>
                            <p class="text-sm mb-4">Kami menyediakan layanan terbaik untuk kebutuhan Anda.</p>
                            <div class="website-item">Produk Unggulan</div>
                            <div class="website-item">Tentang Kami</div>
                            <div class="website-item">Hubungi Kami</div>
                        </div>
                        <div class="defaced-content" id="defaced-content">
                            <div class="skull-icon">☠️</div>
                            <h3 class="text-xl font-bold mb-2">HACKED BY HACKERSGROUP</h3>
                            <p class="mb-3">Your security is weak! We own your system now.</p>
                            <small>All your data belongs to us</small>
                        </div>
                    </div>
                    <button class="btn btn-danger mt-4" onclick="simulateDefacement()">Lakukan Defacement</button>
                    <div id="defacement-message" class="message-box" style="display:none;"></div>
                </div>
            </div>
        </div>

        <!-- Data Theft Simulation Card -->
        <div class="threat-card">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-3">🕵️ Data Theft</h2>
                <p class="text-sm text-gray-600 mb-4">Data Theft adalah pencurian informasi rahasia atau sensitif secara digital, sering kali dilakukan melalui akses tidak sah ke database, sistem, atau jaringan komputer.</p>
                <div class="simulation-area" id="theft-sim">
                    <div class="flex justify-between w-full max-w-md">
                        <div class="server-database">
                            <div class="database-icon">🖥️</div>
                            <div class="text-sm">Database</div>
                            <div id="data-packet-1" class="data-packet">User1</div>
                            <div id="data-packet-2" class="data-packet">Pass1</div>
                            <div id="data-packet-3" class="data-packet">CC#</div>
                        </div>
                        <div class="hacker-container">
                            <div class="hacker-icon">🦹‍♂️</div>
                            <div class="data-collection" id="stolen-data">
                                <div class="text-xs font-bold mb-1">Stolen Data:</div>
                                <!-- Data items will be added here by JavaScript -->
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-danger mt-4" onclick="simulateDataTheft()">Mulai Pencurian Data</button>
                    <div id="theft-message" class="message-box" style="display:none;"></div>
                </div>
            </div>
        </div>

        <!-- Data Leak Simulation Card -->
        <div class="threat-card">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-3">📦 Data Leak</h2>
                <p class="text-sm text-gray-600 mb-4">Data Leak adalah pemaparan informasi ke lingkungan yang tidak sah, bisa disengaja maupun tidak, yang mengakibatkan data pribadi atau rahasia tersebar ke publik.</p>
                <div class="simulation-area" id="leak-sim">
                    <div class="data-leak-container">
                        <div class="leak-stages">
                            <div class="leak-stage" id="leak-stage-1">
                                <div class="leak-stage-icon">🔐</div>
                                <div class="leak-stage-name">Data Tersimpan</div>
                            </div>
                            <div class="leak-stage" id="leak-stage-2">
                                <div class="leak-stage-icon">🚨</div>
                                <div class="leak-stage-name">Kebocoran</div>
                            </div>
                            <div class="leak-stage" id="leak-stage-3">
                                <div class="leak-stage-icon">🌐</div>
                                <div class="leak-stage-name">Penyebaran</div>
                            </div>
                        </div>
                        <div class="data-breach-display" id="data-breach-display">
                            <div class="data-row">name: "John Doe", email: "<EMAIL>", phone: "555-1234"</div>
                            <div class="data-row">name: "Jane Smith", email: "<EMAIL>", phone: "555-5678"</div>
                            <div class="data-row">name: "Bob Johnson", email: "<EMAIL>", phone: "555-9012"</div>
                            <div class="data-row">name: "Alice Brown", email: "<EMAIL>", phone: "555-3456"</div>
                            <div class="data-row">name: "Charlie Davis", email: "<EMAIL>", phone: "555-7890"</div>
                        </div>
                    </div>
                    <button class="btn btn-danger mt-4" onclick="simulateDataLeak()">Simulasikan Kebocoran Data</button>
                    <div id="leak-message" class="message-box" style="display:none;"></div>
                </div>
            </div>
        </div>
    </div>

    <footer class="text-center mt-12 py-6 border-t border-gray-300">
        <p class="text-sm text-gray-600">&copy; 2024 Simulasi Keamanan IT. Dibuat untuk tujuan edukasi.</p>
    </footer>

    <script>
        // Simulasi Web Defacement
        function simulateDefacement() {
            const originalContent = document.getElementById('original-content');
            const defacedContent = document.getElementById('defaced-content');
            const messageDiv = document.getElementById('defacement-message');
            
            messageDiv.style.display = 'none';
            defacedContent.style.opacity = '0'; // Reset ke awal untuk memastikan
            
            // Mulai simulasi
            messageDiv.innerHTML = 'Penyerang mencari kerentanan untuk akses ke server web...';
            messageDiv.className = 'message-box message-info';
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.innerHTML = '⚠️ Ditemukan celah keamanan melalui form upload yang tidak divalidasi!';
                messageDiv.className = 'message-box message-warning';
                
                setTimeout(() => {
                    // Menampilkan konten yang telah diganti (defaced)
                    defacedContent.style.opacity = '1';
                    defacedContent.style.pointerEvents = 'auto'; // Aktifkan interaksi
                    
                    messageDiv.innerHTML = '🚨 Web telah dirusak (defaced)! Penyerang mengganti konten website dengan pesan provokasi mereka.';
                    messageDiv.className = 'message-box message-error';
                }, 1500);
                
                // Reset simulasi
                setTimeout(() => {
                    defacedContent.style.opacity = '0';
                    defacedContent.style.pointerEvents = 'none'; // Nonaktifkan interaksi
                    
                    setTimeout(() => {
                        messageDiv.innerHTML = 'Website telah dipulihkan dari backup. Penting untuk selalu memperbarui sistem, memvalidasi semua input, dan melakukan backup reguler.';
                        messageDiv.className = 'message-box message-success';
                        
                        setTimeout(() => {
                            messageDiv.style.display = 'none';
                        }, 3000);
                    }, 500);
                }, 6000);
            }, 1500);
        }

        // Simulasi Data Theft
        function simulateDataTheft() {
            const dataPacket1 = document.getElementById('data-packet-1');
            const dataPacket2 = document.getElementById('data-packet-2');
            const dataPacket3 = document.getElementById('data-packet-3');
            const stolenDataContainer = document.getElementById('stolen-data');
            const messageDiv = document.getElementById('theft-message');
            
            // Reset state
            messageDiv.style.display = 'none';
            stolenDataContainer.innerHTML = '<div class="text-xs font-bold mb-1">Stolen Data:</div>';
            stolenDataContainer.style.opacity = '0';
            
            dataPacket1.style.opacity = '0';
            dataPacket1.style.transform = '';
            dataPacket2.style.opacity = '0';
            dataPacket2.style.transform = '';
            dataPacket3.style.opacity = '0';
            dataPacket3.style.transform = '';
            
            // Mulai simulasi
            messageDiv.innerHTML = 'Penyerang menyusup ke dalam sistem...';
            messageDiv.className = 'message-box message-info';
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.innerHTML = '⚠️ Akses database diperoleh melalui SQL Injection!';
                messageDiv.className = 'message-box message-warning';
                
                // Tampilkan dan animasikan packet data pertama
                setTimeout(() => {
                    dataPacket1.style.opacity = '1';
                    dataPacket1.style.transform = 'translateX(150px)';
                    
                    setTimeout(() => {
                        stolenDataContainer.style.opacity = '1';
                        stolenDataContainer.innerHTML += '<div class="data-item">username: admin1</div>';
                        dataPacket1.style.opacity = '0';
                        
                        // Packet data kedua
                        setTimeout(() => {
                            dataPacket2.style.opacity = '1';
                            dataPacket2.style.transform = 'translateX(150px)';
                            
                            setTimeout(() => {
                                stolenDataContainer.innerHTML += '<div class="data-item">password: p@ssw0rd!</div>';
                                dataPacket2.style.opacity = '0';
                                
                                // Packet data ketiga
                                setTimeout(() => {
                                    dataPacket3.style.opacity = '1';
                                    dataPacket3.style.transform = 'translateX(150px)';
                                    
                                    setTimeout(() => {
                                        stolenDataContainer.innerHTML += '<div class="data-item">credit_card: 4539********0001</div>';
                                        dataPacket3.style.opacity = '0';
                                        
                                        messageDiv.innerHTML = '🚨 Data sensitif telah dicuri! Penyerang mendapatkan akses ke username, password, dan informasi kartu kredit.';
                                        messageDiv.className = 'message-box message-error';
                                    }, 1000);
                                }, 1000);
                            }, 1000);
                        }, 1000);
                    }, 1000);
                }, 1000);
                
                // Reset simulasi
                setTimeout(() => {
                    stolenDataContainer.style.opacity = '0';
                    
                    setTimeout(() => {
                        messageDiv.innerHTML = 'Sebaiknya gunakan enkripsi data, otentikasi multi-faktor, dan monitoring aktivitas untuk mendeteksi dan mencegah pencurian data.';
                        messageDiv.className = 'message-box message-info';
                        
                        setTimeout(() => {
                            messageDiv.style.display = 'none';
                            stolenDataContainer.innerHTML = '<div class="text-xs font-bold mb-1">Stolen Data:</div>';
                        }, 4000);
                    }, 1000);
                }, 9000);
            }, 1500);
        }

        // Simulasi Data Leak
        function simulateDataLeak() {
            const stage1 = document.getElementById('leak-stage-1');
            const stage2 = document.getElementById('leak-stage-2');
            const stage3 = document.getElementById('leak-stage-3');
            const dataRows = document.querySelectorAll('.data-row');
            const messageDiv = document.getElementById('leak-message');
            
            // Reset state
            messageDiv.style.display = 'none';
            stage1.classList.remove('active');
            stage2.classList.remove('active');
            stage3.classList.remove('active');
            
            dataRows.forEach(row => {
                row.classList.remove('visible');
            });
            
            // Mulai simulasi
            stage1.classList.add('active');
            messageDiv.innerHTML = 'Database perusahaan menyimpan data sensitif pelanggan...';
            messageDiv.className = 'message-box message-info';
            messageDiv.style.display = 'block';
            
            // Tunjukkan beberapa data sebagai "tersimpan"
            setTimeout(() => {
                dataRows[0].classList.add('visible');
                setTimeout(() => {
                    dataRows[1].classList.add('visible');
                }, 300);
            }, 1000);
            
            // Tahap kebocoran
            setTimeout(() => {
                stage1.classList.remove('active');
                stage2.classList.add('active');
                
                messageDiv.innerHTML = '⚠️ Terjadi kebocoran data akibat konfigurasi cloud yang salah!';
                messageDiv.className = 'message-box message-warning';
                
                // Tunjukkan lebih banyak data yang bocor
                setTimeout(() => {
                    dataRows[2].classList.add('visible');
                    setTimeout(() => {
                        dataRows[3].classList.add('visible');
                    }, 300);
                }, 500);
            }, 3000);
            
            // Tahap penyebaran
            setTimeout(() => {
                stage2.classList.remove('active');
                stage3.classList.add('active');
                
                messageDiv.innerHTML = '🚨 Data pelanggan tersebar luas di internet dan forum hacker!';
                messageDiv.className = 'message-box message-error';
                
                // Tampilkan semua data
                dataRows[4].classList.add('visible');
            }, 6000);
            
            // Pesan edukasi
            setTimeout(() => {
                messageDiv.innerHTML = 'Kebocoran data dapat berdampak serius terhadap privasi dan keamanan pengguna, serta reputasi dan keuangan perusahaan. Lindungi data dengan enkripsi, kontrol akses, dan audit keamanan secara rutin.';
                messageDiv.className = 'message-box message-info';
                
                // Reset simulasi setelah delay yang lama
                setTimeout(() => {
                    stage3.classList.remove('active');
                    stage1.classList.add('active');
                    
                    dataRows.forEach(row => {
                        row.classList.remove('visible');
                    });
                    
                    setTimeout(() => {
                        stage1.classList.remove('active');
                        messageDiv.style.display = 'none';
                    }, 1000);
                }, 5000);
            }, 9000);
        }
    </script>
</body>
</html>