<!doctype html>
<html lang="id">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title><PERSON><PERSON><PERSON><PERSON> IT</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family: "Inter", sans-serif;
                background-color: #f0f4f8;
            }
            .threat-card {
                background-color: white;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transition:
                    transform 0.3s ease,
                    box-shadow 0.3s ease;
                overflow: hidden;
            }
            .threat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }
            .simulation-area {
                border: 2px dashed #cbd5e1;
                border-radius: 8px;
                padding: 20px;
                min-height: 200px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                text-align: center;
                background-color: #f9fafb;
                position: relative; /* Untuk elemen absolut di dalamnya */
            }
            .btn {
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: 500;
                transition:
                    background-color 0.3s ease,
                    transform 0.2s ease;
                cursor: pointer;
            }
            .btn-primary {
                background-color: #3b82f6;
                color: white;
            }
            .btn-primary:hover {
                background-color: #2563eb;
                transform: translateY(-2px);
            }
            .btn-danger {
                background-color: #ef4444;
                color: white;
            }
            .btn-danger:hover {
                background-color: #dc2626;
                transform: translateY(-2px);
            }
            .icon {
                font-size: 3rem;
                margin-bottom: 10px;
            }
            .computer-icon.infected .screen {
                fill: #ef4444;
            }
            .computer-icon.infected .body {
                fill: #fca5a5;
            }
            .message-box {
                padding: 12px;
                border-radius: 8px;
                margin-top: 15px;
                font-size: 0.9rem;
                text-align: center;
            }
            .message-box code {
                background-color: #e0e0e0;
                padding: 2px 4px;
                border-radius: 4px;
                font-family: monospace;
            }
            .message-success {
                background-color: #d1fae5;
                color: #065f46;
                border: 1px solid #6ee7b7;
            }
            .message-error {
                background-color: #fee2e2;
                color: #991b1b;
                border: 1px solid #fca5a5;
            }
            .message-warning {
                background-color: #ffedd5;
                color: #9a3412;
                border: 1px solid #fdba74;
            }
            .message-info {
                /* Kelas baru untuk pesan info netral */
                background-color: #e0e7ff; /* Biru muda */
                color: #3730a3; /* Biru tua */
                border: 1px solid #a5b4fc; /* Border biru */
            }

            /* Animasi untuk DoS */
            .server-icon {
                position: relative;
                width: 80px;
                height: 100px;
                background-color: #9ca3af;
                border-radius: 8px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-around;
                padding: 10px 0;
                border: 2px solid #4b5563;
            }
            .server-light {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: #22c55e;
                margin: 2px;
            }
            .server-icon.overloaded .server-light {
                background-color: #ef4444;
            }
            .server-icon.overloaded {
                animation: shake 0.5s infinite;
            }
            .request-packet {
                position: absolute;
                width: 15px;
                height: 10px;
                background-color: #3b82f6;
                border-radius: 3px;
                animation: flyToServer 1s linear;
            }
            @keyframes flyToServer {
                0% {
                    transform: translate(0, -100px);
                    opacity: 0;
                }
                50% {
                    opacity: 1;
                }
                100% {
                    transform: translate(0, 0px);
                    opacity: 0;
                }
            }
            @keyframes shake {
                0%,
                100% {
                    transform: translateX(0);
                }
                25% {
                    transform: translateX(-3px);
                }
                50% {
                    transform: translateX(3px);
                }
                75% {
                    transform: translateX(-3px);
                }
            }

            /* Styling untuk MitM */
            .mitm-container {
                display: flex;
                justify-content: space-around;
                align-items: center;
                width: 100%;
                max-width: 450px; /* Bisa disesuaikan jika perlu lebih lebar */
                margin-bottom: 15px;
            }
            .mitm-node {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                flex-shrink: 0; /* Agar node tidak menyusut */
            }
            .mitm-node .icon {
                font-size: 2.5rem;
                margin-bottom: 5px;
            }
            .mitm-node span:last-child {
                font-size: 0.8rem;
            }
            .mitm-line {
                height: 4px;
                background-color: #9ca3af;
                flex-grow: 1;
                margin: 0 5px; /* Sedikit margin antar elemen */
                position: relative;
                top: -20px; /* Sesuaikan dengan posisi ikon */
                min-width: 50px; /* Pastikan garis memiliki lebar minimum */
            }
            .mitm-message {
                position: absolute;
                top: -25px;
                left: 0;
                padding: 4px 8px;
                border-radius: 6px;
                font-size: 0.75rem;
                /* white-space: nowrap; */ /* DIHAPUS untuk memungkinkan wrap */
                opacity: 0;
                transition:
                    transform 2s linear,
                    opacity 0.5s linear,
                    background-color 0.3s linear;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                z-index: 10;
                max-width: 90%; /* Batasi lebar pesan agar tidak melebihi garis jika teks sangat panjang */
                box-sizing: border-box;
            }
            .mitm-message.original {
                background-color: #60a5fa;
                color: white;
            } /* Biru untuk asli */
            .mitm-message.modified {
                background-color: #f87171;
                color: white;
            } /* Merah untuk diubah */

            #mitm-attacker-icon.active {
                animation: pulseAttacker 0.6s infinite alternate;
            }
            @keyframes pulseAttacker {
                from {
                    transform: scale(1);
                }
                to {
                    transform: scale(1.15);
                    filter: drop-shadow(0 0 5px #ef4444);
                }
            }

            /* Styling untuk Phishing yang ditingkatkan */
            .phishing-form-container {
                background-color: #fffacd;
                padding: 15px;
                border-radius: 8px;
                border: 2px dashed #f97316;
                width: 100%;
                max-width: 300px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .phishing-form-container .logo-text {
                font-weight: bold;
                color: #ca8a04;
                font-size: 1.2rem;
                margin-bottom: 10px;
            }
            .attacker-got-data {
                position: absolute;
                bottom: 10px;
                right: 10px;
                opacity: 0;
                transition: opacity 0.5s ease-in-out;
                font-size: 2rem;
            }
            .attacker-got-data.visible {
                opacity: 1;
            }
            .credentials-packet {
                position: absolute;
                background-color: #fca5a5;
                color: #7f1d1d;
                padding: 5px 8px;
                border-radius: 4px;
                font-size: 0.7rem;
                z-index: 20;
                opacity: 0;
            }

            /* Styling untuk XSS yang ditingkatkan */
            .xss-popup-simulation {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: white;
                border: 3px solid #ef4444;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                padding: 25px;
                border-radius: 8px;
                z-index: 100;
                text-align: center;
                width: 80%;
                max-width: 380px;
            }
            .xss-popup-simulation h4 {
                font-size: 1.1rem;
                font-weight: bold;
                color: #dc2626;
                margin-bottom: 10px;
            }
            .xss-popup-simulation p {
                font-size: 0.9rem;
                margin-bottom: 15px;
            }
            .xss-popup-simulation code.script-display {
                display: inline-block;
                background-color: #f3f4f6;
                color: #1f2937;
                padding: 5px 8px;
                border-radius: 4px;
                font-family: "Courier New", Courier, monospace;
                font-size: 0.95rem;
                border: 1px solid #d1d5db;
                word-break: break-all;
            }
            .xss-popup-simulation button {
                background-color: #ef4444;
                color: white;
            }
            .xss-popup-simulation button:hover {
                background-color: #dc2626;
            }

            /* Styling untuk SQL Injection */
            .sql-query-display {
                font-family: monospace;
                background-color: #e9e9e9;
                padding: 10px;
                border-radius: 5px;
                margin-top: 10px;
                font-size: 0.85rem;
                text-align: left;
                white-space: pre-wrap;
                border: 1px solid #ccc;
            }
            .sql-query-display .injected {
                color: #ef4444;
                font-weight: bold;
            }
            .message-box.text-left {
                /* Digunakan untuk SQLi agar penjelasan lebih rapi */
                text-align: left;
            }

            /* Style untuk Ransomware */
            .file-icon {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                transition: all 0.3s ease;
            }
            .file-icon.encrypted {
                border-color: #ef4444;
                background-color: #fee2e2;
            }
            .ransomware-popup {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: #dc2626;
                color: white;
                padding: 15px;
                border-radius: 8px;
                width: 90%;
                max-width: 350px;
                z-index: 100;
                text-align: center;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            }

            /* Style untuk Zero Day Exploit */
            .software-interface {
                width: 100%;
                max-width: 300px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            .exploit-animation {
                position: absolute;
                background-color: rgba(239, 68, 68, 0.2);
                border: 2px dashed #ef4444;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                transform: translate(-50%, -50%);
                animation: expandRing 1s ease-out forwards;
            }
            @keyframes expandRing {
                0% {
                    width: 20px;
                    height: 20px;
                    opacity: 0;
                }
                50% {
                    opacity: 1;
                }
                100% {
                    width: 200px;
                    height: 200px;
                    opacity: 0;
                }
            }
        </style>
    </head>
    <body class="p-4 md:p-8">
        <header class="text-center mb-10">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800">
                Simulasi Interaktif Ancaman Keamanan IT
            </h1>
            <p class="text-gray-600 mt-2">
                Pelajari tentang berbagai ancaman keamanan siber secara visual
                dan interaktif.
            </p>
            <div class="mt-6">
                <a
                    href="threats-2.html"
                    class="text-blue-600 hover:text-blue-800 font-medium"
                    >Lihat Simulasi Lanjutan →</a
                >
            </div>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        🦠 Malware (Virus)
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Malware adalah perangkat lunak berbahaya yang dirancang
                        untuk merusak atau mendapatkan akses tidak sah ke sistem
                        komputer. Contoh: virus, worm, trojan, ransomware.
                    </p>
                    <div class="simulation-area" id="malware-sim">
                        <svg
                            id="computer-icon-malware"
                            class="computer-icon icon w-20 h-20 mb-4"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                class="body"
                                d="M4 6C4 4.89543 4.89543 4 6 4H18C19.1046 4 20 4.89543 20 6V16C20 17.1046 19.1046 18 18 18H6C4.89543 18 4 17.1046 4 16V6Z"
                                fill="#60a5fa"
                            />
                            <path
                                class="screen"
                                d="M6 6H18V14H6V6Z"
                                fill="#93c5fd"
                            />
                            <path
                                d="M10 20H14"
                                stroke="#4b5563"
                                stroke-width="2"
                                stroke-linecap="round"
                            />
                            <path
                                d="M12 18V20"
                                stroke="#4b5563"
                                stroke-width="2"
                                stroke-linecap="round"
                            />
                        </svg>
                        <button
                            class="btn btn-danger"
                            onclick="simulateMalware()"
                        >
                            Unduh File Mencurigakan
                        </button>
                        <div
                            id="malware-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        🎣 Phishing
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Phishing adalah upaya menipu pengguna untuk
                        mengungkapkan informasi sensitif dengan menyamar sebagai
                        entitas tepercaya.
                    </p>
                    <div class="simulation-area" id="phishing-sim">
                        <div class="phishing-form-container">
                            <div class="logo-text">BankPalsu Login</div>
                            <label
                                for="phishing-email"
                                class="block text-sm font-medium text-gray-700 mb-1 text-left"
                                >Email:</label
                            >
                            <input
                                type="email"
                                id="phishing-email"
                                class="w-full p-2 border border-gray-300 rounded-md mb-2 focus:ring-yellow-500 focus:border-yellow-500"
                                placeholder="<EMAIL>"
                            />
                            <label
                                for="phishing-password"
                                class="block text-sm font-medium text-gray-700 mb-1 text-left"
                                >Password:</label
                            >
                            <input
                                type="password"
                                id="phishing-password"
                                class="w-full p-2 border border-gray-300 rounded-md mb-3 focus:ring-yellow-500 focus:border-yellow-500"
                                placeholder="Kata Sandi"
                            />
                            <button
                                class="btn btn-primary w-full bg-yellow-500 hover:bg-yellow-600"
                                onclick="simulatePhishing()"
                            >
                                Login
                            </button>
                        </div>
                        <div
                            id="phishing-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                        <div
                            id="attacker-phishing-icon"
                            class="attacker-got-data"
                        >
                            🕵️
                        </div>
                        <div
                            id="credentials-phishing-packet"
                            class="credentials-packet"
                        ></div>
                    </div>
                </div>
            </div>

            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        👤<span class="text-blue-500">↔️</span
                        ><span class="text-red-500">🕵️</span
                        ><span class="text-blue-500">↔️</span>🏦
                        Man-in-the-Middle (MitM)
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Dalam serangan MitM, penyerang secara diam-diam menyadap
                        dan mungkin mengubah komunikasi antara dua pihak yang
                        mengira mereka berkomunikasi secara langsung.
                    </p>
                    <div
                        class="simulation-area"
                        id="mitm-sim"
                        style="min-height: 250px"
                    >
                        <div class="mitm-container mb-6">
                            <div class="mitm-node" id="mitm-user-node">
                                <span class="icon">😀</span>
                                <span>Anda</span>
                            </div>
                            <div class="mitm-line" id="mitm-line-1">
                                <div
                                    class="mitm-message original"
                                    id="mitm-message-user-to-attacker"
                                >
                                    Pesan Asli
                                </div>
                            </div>
                            <div class="mitm-node" id="mitm-attacker-node">
                                <span
                                    class="icon text-red-500"
                                    id="mitm-attacker-icon"
                                    >🕵️</span
                                >
                                <span>Penyadap</span>
                            </div>
                            <div class="mitm-line" id="mitm-line-2">
                                <div
                                    class="mitm-message modified"
                                    id="mitm-message-attacker-to-bank"
                                >
                                    Pesan Diubah
                                </div>
                            </div>
                            <div class="mitm-node" id="mitm-bank-node">
                                <span class="icon">🏦</span>
                                <span>Bank</span>
                            </div>
                        </div>
                        <button
                            class="btn btn-primary"
                            onclick="simulateMitM()"
                        >
                            Kirim Pesan Rahasia
                        </button>
                        <div
                            id="mitm-message-status"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        🚦 Denial of Service (DoS/DDoS)
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Serangan DoS/DDoS bertujuan membuat layanan online
                        (seperti website) tidak tersedia bagi pengguna dengan
                        membanjirinya dengan lalu lintas dari banyak sumber.
                    </p>
                    <div class="simulation-area" id="dos-sim">
                        <div id="server-dos-icon" class="server-icon mb-4">
                            <div class="server-light"></div>
                            <div class="server-light"></div>
                            <div class="server-light"></div>
                        </div>
                        <div
                            id="request-container"
                            class="relative w-full h-12"
                        ></div>
                        <button class="btn btn-danger" onclick="simulateDoS()">
                            Luncurkan Serangan DoS
                        </button>
                        <div
                            id="dos-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        💉 SQL Injection
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        SQL Injection terjadi ketika penyerang menyisipkan kode
                        SQL berbahaya ke dalam input query database, yang
                        memungkinkan mereka memanipulasi atau mencuri data.
                    </p>
                    <div
                        class="simulation-area"
                        id="sqli-sim"
                        style="min-height: 280px"
                    >
                        <div class="w-full max-w-xs">
                            <label
                                for="sqli-username"
                                class="block text-sm font-medium text-gray-700 mb-1 text-left"
                                >Username:</label
                            >
                            <input
                                type="text"
                                id="sqli-username"
                                class="w-full p-2 border border-gray-300 rounded-md mb-3 focus:ring-blue-500 focus:border-blue-500"
                                value="' OR '1'='1"
                            />
                            <button
                                class="btn btn-primary w-full"
                                onclick="simulateSQLi()"
                            >
                                Login
                            </button>
                            <div
                                id="sqli-query-display"
                                class="sql-query-display"
                                style="display: none"
                            ></div>
                            <div
                                id="sqli-message"
                                class="message-box text-left"
                                style="display: none"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        💬 Cross-Site Scripting (XSS)
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        XSS memungkinkan penyerang menyuntikkan skrip berbahaya
                        ke halaman web yang dilihat oleh pengguna lain. Skrip
                        ini kemudian berjalan di browser korban.
                    </p>
                    <div class="simulation-area" id="xss-sim">
                        <div class="w-full max-w-md">
                            <h3 class="font-semibold mb-2">
                                Bagian Komentar Blog:
                            </h3>
                            <div
                                id="xss-comments-display"
                                class="w-full p-3 border border-gray-200 rounded-md mb-3 h-24 bg-white overflow-y-auto"
                            >
                                <p class="text-sm text-gray-500">
                                    Belum ada komentar.
                                </p>
                            </div>
                            <input
                                type="text"
                                id="xss-comment-input"
                                class="w-full p-2 border border-gray-300 rounded-md mb-3 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Tulis komentar (coba: <script>alert('XSS!')</script>)"
                            />
                            <button
                                class="btn btn-primary w-full"
                                onclick="simulateXSS()"
                            >
                                Kirim Komentar
                            </button>
                            <div
                                id="xss-message"
                                class="message-box"
                                style="display: none"
                            ></div>
                            <div id="xss-popup-container"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        💸 Cross-Site Request Forgery (CSRF)
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        CSRF memaksa browser pengguna yang sudah terautentikasi
                        untuk mengirim permintaan palsu ke aplikasi web,
                        seringkali tanpa sepengetahuan pengguna.
                    </p>
                    <div class="simulation-area" id="csrf-sim">
                        <p class="mb-2 text-sm">
                            Anda sedang login di
                            <strong class="text-green-600">BankAman.com</strong
                            >.
                        </p>
                        <p class="mb-4 text-sm">
                            Kemudian, Anda mengunjungi
                            <strong class="text-red-600">SitusJahil.com</strong>
                            yang memiliki tombol ini:
                        </p>
                        <button
                            class="btn btn-primary"
                            onclick="simulateCSRF()"
                        >
                            Klik untuk Hadiah Gratis!
                        </button>
                        <div
                            id="csrf-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Ransomware Simulation Card -->
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        🔒 Ransomware
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Ransomware adalah malware yang mengenkripsi data korban
                        dan meminta tebusan untuk mendapatkan kunci dekripsi.
                        Penyerang mengancam akan menghapus data jika tebusan
                        tidak dibayar.
                    </p>
                    <div class="simulation-area" id="ransomware-sim">
                        <div
                            id="ransomware-files"
                            class="w-full grid grid-cols-3 gap-2 mb-4"
                        >
                            <div class="file-icon p-2 text-center">
                                <span class="text-2xl">📄</span>
                                <p class="text-xs">dokumen.txt</p>
                            </div>
                            <div class="file-icon p-2 text-center">
                                <span class="text-2xl">📊</span>
                                <p class="text-xs">laporan.xlsx</p>
                            </div>
                            <div class="file-icon p-2 text-center">
                                <span class="text-2xl">📷</span>
                                <p class="text-xs">foto.jpg</p>
                            </div>
                        </div>
                        <button
                            class="btn btn-danger"
                            onclick="simulateRansomware()"
                        >
                            Unduh File Terinfeksi
                        </button>
                        <div
                            id="ransomware-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                        <div
                            id="ransomware-popup"
                            class="ransomware-popup"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Zero Day Exploit Simulation Card -->
            <div class="threat-card">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-700 mb-3">
                        🕳️ Zero Day Exploit
                    </h2>
                    <p class="text-sm text-gray-600 mb-4">
                        Zero Day Exploit memanfaatkan kerentanan software yang
                        belum diketahui vendor dan belum memiliki patch,
                        memberikan kesempatan penyerang mengakses sistem tanpa
                        terdeteksi.
                    </p>
                    <div class="simulation-area" id="zeroday-sim">
                        <div
                            id="software-interface"
                            class="software-interface mb-4"
                        >
                            <div
                                class="software-header p-2 bg-blue-600 text-white text-sm rounded-t"
                            >
                                <span>BrowserX v12.5 - Up to date ✓</span>
                            </div>
                            <div
                                class="software-body p-4 bg-white border border-t-0 border-gray-300 rounded-b"
                            >
                                <p class="text-sm">Browsing securely...</p>
                            </div>
                        </div>
                        <button
                            class="btn btn-primary"
                            onclick="simulateZeroDay()"
                        >
                            Kunjungi Website Berbahaya
                        </button>
                        <div
                            id="zeroday-message"
                            class="message-box"
                            style="display: none"
                        ></div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="text-center mt-12 py-6 border-t border-gray-300">
            <p class="text-sm text-gray-600">
                &copy; 2024 Simulasi Keamanan IT. Dibuat untuk tujuan edukasi.
            </p>
        </footer>

        <script>
            // Helper function to calculate translate distance for MitM
            function getTranslateDistance(lineElement, messageElement) {
                if (!lineElement || !messageElement) return "0px";
                const lineWidth = lineElement.offsetWidth;
                const messageWidth = messageElement.offsetWidth;
                // Pesan bergerak sehingga tepi kanannya sejajar dengan tepi kanan garis
                // atau berhenti di awal jika pesan lebih lebar dari garis.
                return `${Math.max(0, lineWidth - messageWidth)}px`;
            }

            // Simulasi Malware
            function simulateMalware() {
                const computerIcon = document.getElementById(
                    "computer-icon-malware",
                );
                const messageDiv = document.getElementById("malware-message");

                computerIcon.classList.add("infected");
                messageDiv.innerHTML =
                    "😱 Komputer Anda terinfeksi malware! Sistem menjadi lambat dan data Anda mungkin dicuri.";
                messageDiv.className = "message-box message-error";
                messageDiv.style.display = "block";

                setTimeout(() => {
                    computerIcon.classList.remove("infected");
                    messageDiv.style.display = "none";
                }, 5000);
            }

            // Simulasi Phishing (Ditingkatkan)
            let phishingInProgress = false;
            function simulatePhishing() {
                if (phishingInProgress) return;
                phishingInProgress = true;

                const emailInput = document.getElementById("phishing-email");
                const passwordInput =
                    document.getElementById("phishing-password");
                const email = emailInput.value;
                const password = passwordInput.value;
                const messageDiv = document.getElementById("phishing-message");
                const attackerIcon = document.getElementById(
                    "attacker-phishing-icon",
                );
                const credentialsPacket = document.getElementById(
                    "credentials-phishing-packet",
                );
                const phishingForm = emailInput.closest(
                    ".phishing-form-container",
                );

                messageDiv.style.display = "none";
                attackerIcon.classList.remove("visible");
                credentialsPacket.style.opacity = "0";

                if (email && password) {
                    credentialsPacket.textContent = `Email: ${email}, Pass: ***`;
                    const formRect = phishingForm.getBoundingClientRect();
                    const simAreaRect = document
                        .getElementById("phishing-sim")
                        .getBoundingClientRect();
                    const attackerRect = attackerIcon.getBoundingClientRect();

                    credentialsPacket.style.top = `${formRect.top - simAreaRect.top + formRect.height / 2 - credentialsPacket.offsetHeight / 2}px`;
                    credentialsPacket.style.left = `${formRect.left - simAreaRect.left + formRect.width / 2 - credentialsPacket.offsetWidth / 2}px`;
                    credentialsPacket.style.opacity = "1";
                    credentialsPacket.style.transition =
                        "transform 1s ease-out, opacity 0.5s ease-out";

                    setTimeout(() => {
                        const targetX =
                            attackerRect.left -
                            simAreaRect.left +
                            attackerRect.width / 2 -
                            credentialsPacket.offsetWidth / 2;
                        const targetY =
                            attackerRect.top -
                            simAreaRect.top +
                            attackerRect.height / 2 -
                            credentialsPacket.offsetHeight / 2;
                        credentialsPacket.style.transform = `translate(${targetX - (formRect.left - simAreaRect.left + formRect.width / 2 - credentialsPacket.offsetWidth / 2)}px, ${targetY - (formRect.top - simAreaRect.top + formRect.height / 2 - credentialsPacket.offsetHeight / 2)}px)`;
                    }, 100);

                    setTimeout(() => {
                        credentialsPacket.style.opacity = "0";
                        attackerIcon.classList.add("visible");
                        messageDiv.innerHTML = `🚨 Data login Anda telah dicuri oleh penyerang! <br>Situs "BankPalsu" ini adalah jebakan phishing. Selalu periksa URL dan waspadai tampilan yang mencurigakan.`;
                        messageDiv.className = "message-box message-error";
                        messageDiv.style.display = "block";
                    }, 1200);

                    setTimeout(() => {
                        messageDiv.style.display = "none";
                        attackerIcon.classList.remove("visible");
                        emailInput.value = "";
                        passwordInput.value = "";
                        credentialsPacket.style.transform = "translate(0,0)";
                        phishingInProgress = false;
                    }, 7000);
                } else {
                    messageDiv.innerHTML =
                        "Mohon isi email dan password untuk simulasi.";
                    messageDiv.className = "message-box message-warning";
                    messageDiv.style.display = "block";
                    setTimeout(() => {
                        messageDiv.style.display = "none";
                        phishingInProgress = false;
                    }, 3000);
                }
            }

            // Simulasi Man-in-the-Middle (MitM) (Animasi diperlambat & arah diperjelas)
            let mitmInProgress = false;
            function simulateMitM() {
                if (mitmInProgress) return;
                mitmInProgress = true;

                const userToAttackerMessage = document.getElementById(
                    "mitm-message-user-to-attacker",
                ); // Pesan biru (asli)
                const attackerToBankMessage = document.getElementById(
                    "mitm-message-attacker-to-bank",
                ); // Pesan merah (diubah)
                const attackerIcon =
                    document.getElementById("mitm-attacker-icon");
                const statusMessage = document.getElementById(
                    "mitm-message-status",
                );
                const line1 = document.getElementById("mitm-line-1");
                const line2 = document.getElementById("mitm-line-2");

                // Reset states
                userToAttackerMessage.style.opacity = "0";
                userToAttackerMessage.style.transform = "translateX(0px)";
                attackerToBankMessage.style.opacity = "0";
                attackerToBankMessage.style.transform = "translateX(0px)";
                attackerIcon.classList.remove("active");
                statusMessage.style.display = "none";
                statusMessage.className = "message-box";

                // 1. User sends original message (BIRU)
                userToAttackerMessage.textContent = "Transfer Rp100rb ke Budi";
                userToAttackerMessage.classList.remove("modified");
                userToAttackerMessage.classList.add("original"); // Pastikan warna biru

                setTimeout(() => {
                    userToAttackerMessage.style.opacity = "1";
                    const distance1 = getTranslateDistance(
                        line1,
                        userToAttackerMessage,
                    );
                    userToAttackerMessage.style.transform = `translateX(${distance1})`;
                    statusMessage.innerHTML =
                        'Anda mengirim pesan asli: "Transfer Rp100rb ke Budi"';
                    statusMessage.className = "message-box message-info";
                    statusMessage.style.display = "block";
                }, 200);

                // 2. Attacker intercepts and reads original message
                setTimeout(() => {
                    attackerIcon.classList.add("active");
                    statusMessage.innerHTML =
                        "⚠️ Penyadap mencegat dan membaca pesan asli Anda!";
                    statusMessage.className = "message-box message-warning";
                }, 2300); // Setelah pesan pertama selesai bergerak (200ms + 2000ms transisi) + delay

                // 3. Attacker modifies message
                setTimeout(() => {
                    userToAttackerMessage.style.opacity = "0"; // Sembunyikan pesan asli biru

                    attackerToBankMessage.textContent =
                        "Transfer Rp1 JUTA ke Penipu!"; // Ini akan jadi pesan merah
                    attackerToBankMessage.classList.remove("original");
                    attackerToBankMessage.classList.add("modified"); // Pastikan warna merah

                    // Pesan yang diubah (merah) disiapkan di awal garis kedua, masih tersembunyi
                    attackerToBankMessage.style.opacity = "0";
                    attackerToBankMessage.style.transform = "translateX(0px)";

                    statusMessage.innerHTML =
                        '🚨 Penyadap mengubah isi pesan menjadi: "Transfer Rp1 JUTA ke Penipu!"';
                    statusMessage.className = "message-box message-error";
                }, 4800);

                // 4. Attacker sends modified message (MERAH)
                setTimeout(() => {
                    attackerIcon.classList.remove("active");
                    attackerToBankMessage.style.opacity = "1"; // Tampilkan pesan merah
                    const distance2 = getTranslateDistance(
                        line2,
                        attackerToBankMessage,
                    );
                    attackerToBankMessage.style.transform = `translateX(${distance2})`;
                    statusMessage.innerHTML =
                        "Penyadap mengirim pesan yang sudah diubah ke Bank...";
                    statusMessage.className = "message-box message-error";
                }, 5300);

                // 5. Bank receives modified message
                setTimeout(() => {
                    statusMessage.innerHTML =
                        '💥 Bank menerima pesan yang telah diubah oleh penyadap: "Transfer Rp1 JUTA ke Penipu!". Dana Anda salah terkirim.';
                    statusMessage.className = "message-box message-error";
                }, 7400);

                // 6. Reset simulation
                setTimeout(() => {
                    userToAttackerMessage.style.opacity = "0";
                    userToAttackerMessage.style.transform = "translateX(0px)";
                    attackerToBankMessage.style.opacity = "0";
                    attackerToBankMessage.style.transform = "translateX(0px)";
                    statusMessage.style.display = "none";
                    mitmInProgress = false;
                }, 11000);
            }

            // Simulasi Denial of Service (DoS)
            function simulateDoS() {
                const serverIcon = document.getElementById("server-dos-icon");
                const requestContainer =
                    document.getElementById("request-container");
                const messageDiv = document.getElementById("dos-message");

                serverIcon.classList.add("overloaded");
                messageDiv.innerHTML =
                    "🔥 Server kebanjiran permintaan! Layanan menjadi tidak tersedia.";
                messageDiv.className = "message-box message-error";
                messageDiv.style.display = "block";

                for (let i = 0; i < 20; i++) {
                    const packet = document.createElement("div");
                    packet.classList.add("request-packet");
                    packet.style.left = Math.random() * 80 + 10 + "%";
                    packet.style.animationDelay = Math.random() * 0.5 + "s";
                    requestContainer.appendChild(packet);
                    setTimeout(
                        () => packet.remove(),
                        1500 + Math.random() * 500,
                    );
                }

                setTimeout(() => {
                    serverIcon.classList.remove("overloaded");
                    messageDiv.style.display = "none";
                }, 5000);
            }

            // Simulasi SQL Injection (Ditingkatkan dengan visualisasi query)
            function simulateSQLi() {
                const usernameInput =
                    document.getElementById("sqli-username").value;
                const messageDiv = document.getElementById("sqli-message");
                const queryDisplayDiv =
                    document.getElementById("sqli-query-display");

                let actualQuery = `SELECT * FROM users WHERE username = '<strong>${usernameInput.replace(/'/g, "''")}</strong>' AND password = '***';`;

                queryDisplayDiv.innerHTML = `<strong>Query SQL yang Dijalankan (Contoh):</strong><br>`;

                if (usernameInput.toLowerCase().includes("' or '1'='1")) {
                    const injectedPart = usernameInput.match(/' OR '1'='1/i)[0];
                    const safeUsernameForDisplay = usernameInput
                        .replace(/</g, "&lt;")
                        .replace(/>/g, "&gt;");
                    let highlightedQuery =
                        `SELECT * FROM users WHERE username = '` +
                        safeUsernameForDisplay.replace(
                            injectedPart,
                            `<span class="injected">${injectedPart}</span>`,
                        ) +
                        `' AND password = '***';`;

                    queryDisplayDiv.innerHTML += highlightedQuery;
                    queryDisplayDiv.style.display = "block";

                    messageDiv.innerHTML = `🔓 <strong>Login Berhasil (Bypass)!</strong><br>
                                        Input <code>' OR '1'='1</code> membuat kondisi WHERE menjadi selalu benar (karena <code>1=1</code> adalah benar),
                                        sehingga query mengembalikan semua pengguna atau pengguna pertama, mengabaikan password.
                                        Penyerang mendapatkan akses tidak sah.`;
                    messageDiv.className =
                        "message-box message-error text-left";
                } else if (usernameInput.trim() === "") {
                    queryDisplayDiv.style.display = "none";
                    messageDiv.innerHTML = "Username tidak boleh kosong.";
                    messageDiv.className =
                        "message-box message-warning text-left";
                } else {
                    const safeUsernameForDisplay = usernameInput
                        .replace(/</g, "&lt;")
                        .replace(/>/g, "&gt;");
                    queryDisplayDiv.innerHTML += `SELECT * FROM users WHERE username = '<strong>${safeUsernameForDisplay}</strong>' AND password = '***';`;
                    queryDisplayDiv.style.display = "block";
                    messageDiv.innerHTML = `Login sebagai "${safeUsernameForDisplay}" gagal. Kondisi WHERE tidak terpenuhi.<br>Coba input: <code>' OR '1'='1</code> untuk melihat efeknya.`;
                    messageDiv.className =
                        "message-box message-warning text-left";
                }
                messageDiv.style.display = "block";

                setTimeout(() => {
                    messageDiv.style.display = "none";
                    queryDisplayDiv.style.display = "none";
                }, 10000);
            }

            // Simulasi Cross-Site Scripting (XSS) (Ditingkatkan dengan tampilan skrip lebih jelas)
            function simulateXSS() {
                const commentInput =
                    document.getElementById("xss-comment-input");
                const commentsDisplay = document.getElementById(
                    "xss-comments-display",
                );
                const xssMessageDiv = document.getElementById("xss-message");
                const popupContainer = document.getElementById(
                    "xss-popup-container",
                );
                const rawComment = commentInput.value;

                popupContainer.innerHTML = "";
                xssMessageDiv.style.display = "none";

                const sanitizedCommentForDisplay = rawComment
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;");

                if (commentsDisplay.innerHTML.includes("Belum ada komentar.")) {
                    commentsDisplay.innerHTML = "";
                }

                const newCommentP = document.createElement("p");
                newCommentP.innerHTML = `<strong>Pengguna Lain:</strong> ${sanitizedCommentForDisplay}`;
                newCommentP.className =
                    "text-sm mb-1 p-2 bg-gray-50 rounded text-left";
                commentsDisplay.appendChild(newCommentP);
                commentsDisplay.scrollTop = commentsDisplay.scrollHeight;

                const scriptTagMatch = rawComment
                    .toLowerCase()
                    .match(/<script\b[^>]*>(.*?)<\/script>/i);

                if (scriptTagMatch) {
                    const fullScriptTag = rawComment.match(
                        /<script\b[^>]*>.*?<\/script>/i,
                    )[0];
                    const scriptContent = scriptTagMatch[1];

                    xssMessageDiv.innerHTML =
                        "💬 Komentar Anda mengandung skrip. Di browser korban lain, ini bisa berbahaya!";
                    xssMessageDiv.className = "message-box message-warning";
                    xssMessageDiv.style.display = "block";

                    const popup = document.createElement("div");
                    popup.className = "xss-popup-simulation";

                    let popupContentHTML = `<h4>⚠️ Peringatan XSS!</h4>`;

                    const alertMatch = scriptContent
                        .toLowerCase()
                        .match(/alert\(['"](.*?)['"]\)/);
                    if (alertMatch) {
                        popupContentHTML += `<p>Skrip dari komentar mencoba menampilkan pesan: <br><strong>"${alertMatch[1].replace(/</g, "&lt;").replace(/>/g, "&gt;")}"</strong></p>
                                         <p>Skrip yang disuntikkan:</p>
                                         <code class="script-display">${fullScriptTag.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</code>`;
                    } else {
                        popupContentHTML += `<p>Skrip berbahaya dari komentar akan dieksekusi di browser pengguna lain!</p>
                                         <p>Konten skrip yang disuntikkan:</p>
                                         <code class="script-display">${fullScriptTag.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</code>`;
                    }

                    popupContentHTML += `<br><br><button class="btn" onclick="this.parentElement.remove()">Tutup Popup Ini</button>`;
                    popup.innerHTML = popupContentHTML;

                    setTimeout(() => {
                        popupContainer.appendChild(popup);
                    }, 500);

                    setTimeout(() => {
                        if (popupContainer.contains(popup)) popup.remove();
                        xssMessageDiv.style.display = "none";
                    }, 9000);
                } else {
                    xssMessageDiv.innerHTML = "Komentar Anda telah diposting.";
                    xssMessageDiv.className = "message-box message-success";
                    xssMessageDiv.style.display = "block";
                    setTimeout(() => {
                        xssMessageDiv.style.display = "none";
                    }, 3000);
                }
                commentInput.value = "";
            }

            // Simulasi Cross-Site Request Forgery (CSRF)
            function simulateCSRF() {
                const messageDiv = document.getElementById("csrf-message");
                messageDiv.innerHTML = `💸 Oops! Tanpa Anda sadari, tombol "Hadiah Gratis" tadi sebenarnya mengirim permintaan ke <strong>BankAman.com</strong> untuk mentransfer dana dari akun Anda. Ini adalah serangan CSRF!`;
                messageDiv.className = "message-box message-error";
                messageDiv.style.display = "block";

                setTimeout(() => {
                    messageDiv.style.display = "none";
                }, 7000);
            }

            // Simulasi Ransomware
            function simulateRansomware() {
                const fileIcons = document.querySelectorAll(
                    "#ransomware-files .file-icon",
                );
                const messageDiv =
                    document.getElementById("ransomware-message");
                const popup = document.getElementById("ransomware-popup");

                messageDiv.style.display = "none";

                // Animasi enkripsi file
                setTimeout(() => {
                    fileIcons.forEach((fileIcon) => {
                        fileIcon.classList.add("encrypted");
                        const fileNameElement = fileIcon.querySelector("p");
                        if (fileNameElement) {
                            const originalName = fileNameElement.textContent;
                            fileNameElement.textContent = `${originalName}.encrypted`;
                        }

                        const iconElement =
                            fileIcon.querySelector("span:first-child");
                        if (iconElement) {
                            iconElement.textContent = "🔒";
                        }
                    });

                    // Tampilkan pesan ransomware
                    popup.innerHTML = `
                    <h3 class="text-xl font-bold mb-2">⚠️ YOUR FILES HAVE BEEN ENCRYPTED! ⚠️</h3>
                    <p class="mb-4">Semua file Anda telah dienkripsi. Untuk mendapatkan kunci dekripsi, bayar 0.5 BTC dalam waktu 48 jam.</p>
                    <p class="text-sm mb-1">Jika tidak, data Anda akan hilang selamanya.</p>
                    <div class="mt-4 flex justify-center">
                        <button class="btn btn-sm bg-white text-red-600 hover:bg-gray-100" onclick="document.getElementById('ransomware-popup').style.display='none';">Tutup</button>
                    </div>
                `;
                    popup.style.display = "block";

                    messageDiv.innerHTML =
                        "🔒 Ransomware telah mengenkripsi semua file Anda dan meminta tebusan untuk mengembalikannya!";
                    messageDiv.className = "message-box message-error";
                    messageDiv.style.display = "block";
                }, 500);

                // Reset simulasi setelah beberapa detik (jika popup ditutup)
                setTimeout(() => {
                    if (popup.style.display !== "none") {
                        popup.style.display = "none";
                    }

                    fileIcons.forEach((fileIcon) => {
                        fileIcon.classList.remove("encrypted");
                        const fileNameElement = fileIcon.querySelector("p");
                        if (fileNameElement) {
                            const encryptedName = fileNameElement.textContent;
                            fileNameElement.textContent = encryptedName.replace(
                                ".encrypted",
                                "",
                            );
                        }

                        const iconElement =
                            fileIcon.querySelector("span:first-child");
                        if (iconElement && iconElement.textContent === "🔒") {
                            // Kembalikan ikon sesuai tipe file
                            const fileType =
                                fileIcon.querySelector("p").textContent;
                            if (fileType.includes(".txt"))
                                iconElement.textContent = "📄";
                            else if (fileType.includes(".xlsx"))
                                iconElement.textContent = "📊";
                            else if (fileType.includes(".jpg"))
                                iconElement.textContent = "📷";
                        }
                    });

                    messageDiv.style.display = "none";
                }, 10000);
            }

            // Simulasi Zero Day Exploit
            function simulateZeroDay() {
                const softwareBody = document.querySelector(
                    "#software-interface .software-body",
                );
                const messageDiv = document.getElementById("zeroday-message");
                const simArea = document.getElementById("zeroday-sim");

                messageDiv.style.display = "none";

                // Menampilkan proses eksploitasi
                softwareBody.innerHTML =
                    '<p class="text-sm">Membuka halaman...</p>';

                setTimeout(() => {
                    softwareBody.innerHTML =
                        '<p class="text-sm">Halaman dimuat</p>';

                    // Tambahkan efek animasi zero-day exploit
                    const exploitEffect = document.createElement("div");
                    exploitEffect.className = "exploit-animation";
                    exploitEffect.style.left = "50%";
                    exploitEffect.style.top = "30%";
                    simArea.appendChild(exploitEffect);

                    setTimeout(() => {
                        softwareBody.innerHTML =
                            '<p class="text-sm text-red-600">Menemukan kerentanan...</p>';
                        messageDiv.innerHTML =
                            "⚠️ Website berbahaya ini mengeksploitasi kerentanan zero-day pada browser Anda!";
                        messageDiv.className = "message-box message-warning";
                        messageDiv.style.display = "block";

                        // Tambahkan efek animasi lain
                        const exploitEffect2 = document.createElement("div");
                        exploitEffect2.className = "exploit-animation";
                        exploitEffect2.style.left = "70%";
                        exploitEffect2.style.top = "50%";
                        simArea.appendChild(exploitEffect2);
                    }, 1000);

                    setTimeout(() => {
                        softwareBody.innerHTML =
                            '<p class="text-sm text-red-600 font-bold">Vulnerability CVE-2023-XXXX exploited!</p>';
                        messageDiv.innerHTML =
                            '🚨 Penyerang telah mengeksploitasi kerentanan yang belum ditambal! Mereka mendapatkan akses penuh ke sistem Anda meski browser Anda "up-to-date".';
                        messageDiv.className = "message-box message-error";

                        // Hapus efek setelah selesai
                        setTimeout(() => {
                            simArea
                                .querySelectorAll(".exploit-animation")
                                .forEach((el) => el.remove());
                        }, 1000);
                    }, 2000);
                }, 500);

                // Reset simulasi
                setTimeout(() => {
                    softwareBody.innerHTML =
                        '<p class="text-sm">Browsing securely...</p>';
                    messageDiv.style.display = "none";
                }, 10000);
            }

            // Simulasi DNS Spoofing
            function simulateDNSSpoofing() {
                const dnsRoute = document.getElementById("dns-route");
                const dnsNodes = dnsRoute.querySelectorAll(".dns-node");
                const originalServer = dnsRoute.querySelector(
                    ".dns-node.web-server.original",
                );
                const spoofedServer =
                    document.getElementById("dns-spoofed-server");
                const messageDiv = document.getElementById("dnsspoof-message");
                const simArea = document.getElementById("dnsspoof-sim");

                messageDiv.style.display = "none";
                spoofedServer.style.display = "none";

                // Packet animation function
                function createPacket(
                    start,
                    end,
                    color = "#3b82f6",
                    callback = null,
                ) {
                    const packet = document.createElement("div");
                    packet.className = "dns-packet";
                    packet.style.backgroundColor = color;

                    const startRect = start.getBoundingClientRect();
                    const endRect = end.getBoundingClientRect();
                    const simRect = simArea.getBoundingClientRect();

                    // Posisi awal relatif terhadap simulation area
                    packet.style.left = `${startRect.left - simRect.left + startRect.width / 2 - 6}px`;
                    packet.style.top = `${startRect.top - simRect.top + startRect.height / 2 - 6}px`;
                    simArea.appendChild(packet);

                    // Animate
                    packet.style.transition = "left 1s, top 1s";
                    setTimeout(() => {
                        packet.style.left = `${endRect.left - simRect.left + endRect.width / 2 - 6}px`;
                        packet.style.top = `${endRect.top - simRect.top + endRect.height / 2 - 6}px`;

                        setTimeout(() => {
                            packet.remove();
                            if (callback) callback();
                        }, 1000);
                    }, 50);
                }

                // Start simulation
                messageDiv.innerHTML = "Anda mencoba mengakses bankasli.com...";
                messageDiv.className = "message-box message-info";
                messageDiv.style.display = "block";

                // Step 1: User to DNS server
                setTimeout(() => {
                    createPacket(dnsNodes[0], dnsNodes[1]);
                    messageDiv.innerHTML =
                        'Mengirim permintaan DNS: "Di mana alamat bankasli.com?"';
                }, 500);

                // Step 2: Attacker intercepts
                setTimeout(() => {
                    spoofedServer.style.display = "flex";
                    messageDiv.innerHTML =
                        "⚠️ Penyerang telah mengubah cache DNS!";
                    messageDiv.className = "message-box message-warning";
                }, 2000);

                // Step 3: DNS server responds with spoofed address
                setTimeout(() => {
                    createPacket(dnsNodes[1], dnsNodes[0], "#ef4444");
                    messageDiv.innerHTML =
                        '🚨 DNS server mengirim alamat palsu: "bankasli.com ada di 203.0.113.x" (padahal itu alamat server palsu)';
                    messageDiv.className = "message-box message-error";
                }, 3000);

                // Step 4: User connects to fake server
                setTimeout(() => {
                    originalServer.style.opacity = "0.5";
                    createPacket(dnsNodes[0], spoofedServer, "#ef4444");
                    messageDiv.innerHTML =
                        "🚨 Anda terhubung ke situs palsu yang terlihat seperti bankasli.com, tetapi sebenarnya dikendalikan oleh penyerang!";
                }, 4500);

                // Reset
                setTimeout(() => {
                    spoofedServer.style.display = "none";
                    originalServer.style.opacity = "1";
                    messageDiv.style.display = "none";
                }, 10000);
            }
        </script>
    </body>
</html>
