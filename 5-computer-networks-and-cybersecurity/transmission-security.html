<!doctype html>
<html lang="id">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>
            Si<PERSON>lasi Keamanan Transmisi Data: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Protokol
        </title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family: "Inter", sans-serif;
                background-color: #f0f4f8;
                color: #333;
                line-height: 1.6;
            }
            .container {
                max-width: 900px;
                margin: 20px auto;
                padding: 20px;
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
            .simulation-box {
                border: 2px dashed #cbd5e1;
                border-radius: 8px;
                padding: 20px;
                margin-top: 20px;
                background-color: #f9fafb;
                min-height: 200px;
            }
            .key-box {
                background-color: #e0e7ff;
                border: 1px solid #a5b4fc;
                color: #3730a3;
                padding: 10px;
                border-radius: 6px;
                margin-bottom: 10px;
                font-family: monospace;
                word-break: break-all;
            }
            .message-box {
                padding: 12px;
                border-radius: 8px;
                margin-top: 15px;
                font-size: 0.9rem;
            }
            .message-info {
                background-color: #e0e7ff;
                color: #3730a3;
                border: 1px solid #a5b4fc;
            }
            .message-success {
                background-color: #d1fae5;
                color: #065f46;
                border: 1px solid #6ee7b7;
            }
            .message-error {
                background-color: #fee2e2;
                color: #991b1b;
                border: 1px solid #fca5a5;
            }
            .btn {
                padding: 10px 15px;
                border-radius: 6px;
                font-weight: 500;
                cursor: pointer;
                transition:
                    background-color 0.3s ease,
                    transform 0.2s ease;
            }
            .btn-primary {
                background-color: #3b82f6;
                color: white;
            }
            .btn-primary:hover {
                background-color: #2563eb;
                transform: translateY(-2px);
            }
            .btn-secondary {
                background-color: #6b7280;
                color: white;
            }
            .btn-secondary:hover {
                background-color: #4b5563;
                transform: translateY(-2px);
            }
            textarea,
            input {
                width: 100%;
                padding: 10px 12px;
                border-radius: 6px;
                border: 1px solid #cbd5e1;
                margin-bottom: 15px;
                font-family: monospace;
                background-color: #fff;
                transition:
                    border-color 0.3s ease,
                    box-shadow 0.3s ease;
            }
            textarea:focus,
            input:focus {
                outline: none;
                border-color: #93c5fd;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            }
            .concept-section {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
            }
            .concept-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: #1e40af;
                margin-bottom: 10px;
            }
            .concept-explanation {
                background-color: #f3f4f6;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
            code {
                background-color: #e5e7eb;
                padding: 2px 5px;
                border-radius: 4px;
                font-family: monospace;
            }
            .certificate {
                background-color: #fef3c7;
                border: 1px solid #fcd34d;
                color: #92400e;
                padding: 10px;
                border-radius: 6px;
                margin-bottom: 10px;
                font-family: monospace;
                word-break: break-all;
            }
            .lock-icon,
            .key-icon,
            .server-icon,
            .client-icon,
            .ca-icon {
                width: 40px;
                height: 40px;
                margin: 0 auto 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                font-size: 20px;
            }
            .lock-icon {
                background-color: #bfdbfe;
                color: #1e40af;
            }
            .key-icon {
                background-color: #c7d2fe;
                color: #4338ca;
            }
            .server-icon {
                background-color: #a7f3d0;
                color: #065f46;
            }
            .client-icon {
                background-color: #fecaca;
                color: #991b1b;
            }
            .ca-icon {
                background-color: #fed7aa;
                color: #9a3412;
            }
            .animation-container {
                position: relative;
                height: 180px;
                margin: 20px 0;
                overflow: hidden;
                background-color: #f0f9ff;
                border-radius: 8px;
                border: 1px solid #bae6fd;
            }
            .data-packet {
                position: absolute;
                width: 40px;
                height: 30px;
                background-color: #93c5fd;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                color: #1e3a8a;
                transition: all 1.5s ease;
                left: 0;
                top: 45px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .encrypted-packet {
                background-color: #c7d2fe;
                color: #4338ca;
            }
            /* Tambahan untuk label */
            label {
                display: block;
                margin-bottom: 6px;
                font-weight: 500;
                color: #4b5563;
            }
            /* Tambahan untuk container simulasi */
            .simulation-container {
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                padding: 20px;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-800">
                    Keamanan Transmisi Data
                </h1>
                <p class="text-gray-600 mt-2">
                    Simulasi bagaimana data diamankan di internet menggunakan
                    kriptografi, sertifikat, dan protokol keamanan.
                </p>
            </header>

            <div class="concept-explanation mt-4">
                <p>
                    <strong>Kriptografi</strong> adalah ilmu dan seni
                    menyembunyikan informasi dengan mengubah data yang dapat
                    dibaca (plaintext) menjadi bentuk yang tidak dapat dipahami
                    (ciphertext) menggunakan algoritma matematis dan kunci.
                    Proses ini disebut enkripsi, di mana pesan asli dikodekan
                    sedemikian rupa sehingga hanya pihak yang memiliki kunci
                    yang sesuai dapat membacanya. Untuk mengembalikan ciphertext
                    ke bentuk aslinya, dilakukan proses dekripsi, yaitu proses
                    pemulihan data dari ciphertext menjadi plaintext menggunakan
                    kunci yang sesuai. Enkripsi dan dekripsi memastikan
                    kerahasiaan dan keamanan informasi saat dikirimkan atau
                    disimpan, terutama dalam lingkungan digital yang rentan
                    terhadap penyadapan dan penyalahgunaan data. Tujuan utamanya
                    adalah:
                </p>
                <ul class="list-disc list-inside ml-2 text-sm mt-2">
                    <li>
                        <strong>Kerahasiaan:</strong> Menjaga informasi agar
                        tidak dapat dibaca oleh pihak yang tidak berwenang
                    </li>
                    <li>
                        <strong>Integritas:</strong> Memastikan data tidak
                        diubah selama transmisi
                    </li>
                    <li>
                        <strong>Otentikasi:</strong> Memverifikasi identitas
                        pengirim dan penerima
                    </li>
                    <li>
                        <strong>Non-repudiasi:</strong> Memastikan pengirim
                        tidak dapat menyangkal telah mengirim pesan
                    </li>
                </ul>
                <p class="mt-2">
                    Dalam keamanan transmisi data, kriptografi menjadi fondasi
                    utama untuk mengamankan komunikasi digital melalui jaringan
                    yang tidak aman seperti internet.
                </p>
            </div>

            <!-- Simulasi Enkripsi Simetris -->
            <section>
                <h2 class="text-2xl font-semibold text-gray-700 mb-3">
                    Simulasi Enkripsi Simetris
                </h2>
                <p class="mb-4">
                    Enkripsi simetris menggunakan kunci yang sama untuk
                    mengenkripsi dan mendekripsi pesan. Ini sangat cepat, tapi
                    bagaimana cara berbagi kunci dengan aman?
                </p>

                <div class="simulation-box">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="simulation-container">
                            <h3
                                class="text-lg font-medium mb-4 text-indigo-700"
                            >
                                Pengirim
                            </h3>
                            <div class="client-icon mb-4">👤</div>
                            <label
                                for="symmetric-key"
                                class="block text-sm font-medium text-gray-700 mb-1"
                                >Kunci Rahasia:</label
                            >
                            <input
                                type="text"
                                id="symmetric-key"
                                value="rahasia123"
                                placeholder="Masukkan kunci rahasia..."
                                class="focus:ring-2 focus:ring-indigo-200"
                            />

                            <label
                                for="plain-text-symmetric"
                                class="block text-sm font-medium text-gray-700 mb-1"
                                >Pesan Asli:</label
                            >
                            <textarea
                                id="plain-text-symmetric"
                                rows="3"
                                placeholder="Ketik pesan rahasia Anda..."
                                class="focus:ring-2 focus:ring-indigo-200"
                            >
Halo! Ini adalah pesan rahasia.</textarea
                            >

                            <button
                                class="btn btn-primary mt-2 w-full"
                                onclick="encryptSymmetric()"
                            >
                                Enkripsi Pesan
                            </button>

                            <label
                                for="encrypted-text-symmetric"
                                class="block text-sm font-medium text-gray-700 mt-4 mb-1"
                                >Pesan Terenkripsi:</label
                            >
                            <textarea
                                id="encrypted-text-symmetric"
                                rows="3"
                                readonly
                                placeholder="Hasil enkripsi akan muncul di sini..."
                                class="bg-gray-50"
                            ></textarea>
                        </div>

                        <div class="simulation-container">
                            <h3 class="text-lg font-medium mb-4 text-green-700">
                                Penerima
                            </h3>
                            <div class="server-icon mb-4">👤</div>
                            <label
                                for="symmetric-key-receiver"
                                class="block text-sm font-medium text-gray-700 mb-1"
                                >Kunci Rahasia:</label
                            >
                            <input
                                type="text"
                                id="symmetric-key-receiver"
                                placeholder="Masukkan kunci yang sama..."
                                class="focus:ring-2 focus:ring-indigo-200"
                            />

                            <button
                                class="btn btn-primary mt-2 w-full"
                                onclick="decryptSymmetric()"
                            >
                                Dekripsi Pesan
                            </button>

                            <label
                                for="decrypted-text-symmetric"
                                class="block text-sm font-medium text-gray-700 mt-4 mb-1"
                                >Pesan Asli Diterima:</label
                            >
                            <textarea
                                id="decrypted-text-symmetric"
                                rows="3"
                                readonly
                                placeholder="Hasil dekripsi akan muncul di sini..."
                                class="bg-gray-50"
                            ></textarea>
                        </div>
                    </div>

                    <div class="animation-container mt-6">
                        <div id="data-packet" class="data-packet">Data</div>
                    </div>

                    <div
                        id="symmetric-message"
                        class="message-box"
                        style="display: none"
                    ></div>

                    <div
                        class="mt-4 text-sm text-gray-600 bg-blue-50 p-4 rounded-lg"
                    >
                        <p>
                            <strong>Catatan:</strong> Dalam enkripsi simetris,
                            kedua pihak harus memiliki kunci yang sama.
                            Tantangannya adalah bagaimana berbagi kunci ini
                            dengan aman melalui jaringan yang tidak aman.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Simulasi Enkripsi Asimetris -->
            <section class="mt-12">
                <h2 class="text-2xl font-semibold text-gray-700 mb-3">
                    Simulasi Enkripsi Asimetris (Public Private Key)
                </h2>
                <p class="mb-4">
                    Enkripsi asimetris menggunakan sepasang kunci berbeda: kunci
                    publik untuk mengenkripsi dan kunci privat untuk
                    mendekripsi. Ini memecahkan masalah berbagi kunci pada
                    enkripsi simetris.
                </p>

                <div class="simulation-box">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="simulation-container">
                            <h3
                                class="text-lg font-medium mb-4 text-indigo-700"
                            >
                                Pihak A (Pengirim)
                            </h3>
                            <div class="client-icon mb-4">👤</div>
                            <label
                                for="plain-text-a"
                                class="block text-sm font-medium text-gray-700 mb-1"
                                >Pesan Asli:</label
                            >
                            <textarea
                                id="plain-text-a"
                                rows="3"
                                placeholder="Ketik pesan rahasia Anda..."
                                class="focus:ring-2 focus:ring-indigo-200"
                            >
Ini pesan rahasia saya!</textarea
                            >

                            <label
                                for="public-key-b"
                                class="block text-sm font-medium text-gray-700 mb-1"
                                >Kunci Publik Penerima (Pihak B):</label
                            >
                            <div id="public-key-b" class="key-box">
                                PUBLIC_KEY_B_EXAMPLE_12345
                            </div>

                            <button
                                class="btn btn-primary mt-2 w-full"
                                onclick="encryptMessageA()"
                            >
                                Enkripsi dengan Kunci Publik B
                            </button>

                            <label
                                for="encrypted-text-a"
                                class="block text-sm font-medium text-gray-700 mt-4 mb-1"
                                >Pesan Terenkripsi:</label
                            >
                            <textarea
                                id="encrypted-text-a"
                                rows="3"
                                readonly
                                placeholder="Hasil enkripsi akan muncul di sini..."
                                class="bg-gray-50"
                            ></textarea>
                        </div>

                        <div class="simulation-container">
                            <h3 class="text-lg font-medium mb-4 text-green-700">
                                Pihak B (Penerima)
                            </h3>
                            <div class="server-icon mb-4">👤</div>
                            <label
                                for="private-key-b"
                                class="block text-sm font-medium text-gray-700 mb-1"
                                >Kunci Privat Pihak B:</label
                            >
                            <div id="private-key-b" class="key-box">
                                PRIVATE_KEY_B_SECRET_67890
                            </div>

                            <button
                                class="btn btn-primary mt-2 w-full"
                                onclick="decryptMessageB()"
                            >
                                Dekripsi dengan Kunci Privat B
                            </button>

                            <label
                                for="decrypted-text-b"
                                class="block text-sm font-medium text-gray-700 mt-4 mb-1"
                                >Pesan Asli Diterima:</label
                            >
                            <textarea
                                id="decrypted-text-b"
                                rows="3"
                                readonly
                                placeholder="Hasil dekripsi akan muncul di sini..."
                                class="bg-gray-50"
                            ></textarea>
                        </div>
                    </div>

                    <div class="animation-container mt-6">
                        <div id="asymmetric-data-packet" class="data-packet">
                            Data
                        </div>
                    </div>

                    <div
                        id="asymmetric-message"
                        class="message-box"
                        style="display: none"
                    ></div>

                    <div
                        class="mt-4 text-sm text-gray-600 bg-blue-50 p-4 rounded-lg"
                    >
                        <p>
                            <strong>Catatan:</strong> Dalam enkripsi asimetris,
                            kunci publik dapat dibagikan secara terbuka,
                            sementara kunci privat harus tetap rahasia. Pesan
                            yang dienkripsi dengan kunci publik hanya dapat
                            didekripsi dengan kunci privat yang sesuai.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Penjelasan Certificate Authority -->
            <section class="concept-section">
                <h2 class="concept-title">
                    Certificate Authority (CA) dan Keamanan Web
                </h2>
                <p class="mb-4">
                    Bagaimana kita bisa yakin bahwa kunci publik yang kita
                    terima benar-benar milik website yang kita kunjungi? Di
                    sinilah peran Certificate Authority (CA) menjadi sangat
                    penting.
                </p>

                <div class="concept-explanation">
                    <h3 class="text-xl font-semibold mb-2">
                        Apa itu Certificate Authority (CA)?
                    </h3>
                    <p>
                        Certificate Authority adalah pihak ketiga terpercaya
                        yang bertugas memverifikasi identitas entitas di
                        internet (seperti website) dan menerbitkan sertifikat
                        digital untuk mereka.
                    </p>

                    <div
                        class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 text-center"
                    >
                        <div>
                            <div class="ca-icon">🏛️</div>
                            <h4 class="font-semibold">Certificate Authority</h4>
                            <p class="text-sm">
                                Lembaga tepercaya yang memverifikasi identitas
                            </p>
                        </div>
                        <div>
                            <div class="server-icon">🖥️</div>
                            <h4 class="font-semibold">Website/Server</h4>
                            <p class="text-sm">
                                Meminta sertifikat untuk membuktikan identitas
                            </p>
                        </div>
                        <div>
                            <div class="client-icon">📱</div>
                            <h4 class="font-semibold">Pengguna/Browser</h4>
                            <p class="text-sm">
                                Memeriksa sertifikat untuk memastikan keamanan
                            </p>
                        </div>
                    </div>
                </div>

                <div class="concept-explanation">
                    <h3 class="text-xl font-semibold mb-2">
                        Bagaimana CA Bekerja?
                    </h3>
                    <ol class="list-decimal list-inside ml-4 space-y-2">
                        <li>
                            <strong>Permintaan Sertifikat:</strong> Website
                            (misalnya bank.com) membuat pasangan kunci
                            publik-privat dan mengirimkan kunci publik beserta
                            informasi identitas ke CA.
                        </li>
                        <li>
                            <strong>Verifikasi Identitas:</strong> CA
                            memverifikasi bahwa pemohon benar-benar pemilik
                            domain bank.com (biasanya melalui dokumen resmi,
                            email verifikasi, dll).
                        </li>
                        <li>
                            <strong>Penerbitan Sertifikat:</strong> Setelah
                            verifikasi berhasil, CA membuat sertifikat digital
                            yang berisi:
                            <ul class="list-disc list-inside ml-6">
                                <li>Kunci publik website</li>
                                <li>Informasi identitas website</li>
                                <li>Tanggal berlaku sertifikat</li>
                                <li>Tanda tangan digital CA</li>
                            </ul>
                        </li>
                        <li>
                            <strong>Penggunaan Sertifikat:</strong> Website
                            memasang sertifikat ini di server mereka.
                        </li>
                        <li>
                            <strong>Verifikasi oleh Browser:</strong> Ketika
                            Anda mengunjungi website, browser Anda menerima
                            sertifikat dan memeriksa:
                            <ul class="list-disc list-inside ml-6">
                                <li>
                                    Apakah sertifikat diterbitkan oleh CA
                                    tepercaya?
                                </li>
                                <li>Apakah sertifikat masih berlaku?</li>
                                <li>
                                    Apakah domain dalam sertifikat cocok dengan
                                    website yang dikunjungi?
                                </li>
                            </ul>
                        </li>
                    </ol>
                </div>

                <div class="concept-explanation">
                    <h3 class="text-xl font-semibold mb-2">
                        Contoh Sertifikat Digital
                    </h3>
                    <div class="certificate">
                        <p><strong>Certificate:</strong></p>
                        <p>Version: 3</p>
                        <p>Serial Number: ********</p>
                        <p>Signature Algorithm: sha256WithRSAEncryption</p>
                        <p>
                            Issuer: CN=DigiCert Global Root CA, O=DigiCert Inc,
                            C=US
                        </p>
                        <p>Valid From: Jan 1, 2023</p>
                        <p>Valid To: Jan 1, 2024</p>
                        <p>Subject: CN=bank.com, O=Bank Corp, C=ID</p>
                        <p>Public Key: [Kunci publik dalam format base64]</p>
                        <p>Signature: [Tanda tangan digital CA]</p>
                    </div>
                </div>

                <div class="concept-explanation">
                    <h3 class="text-xl font-semibold mb-2">
                        Jenis-Jenis Sertifikat SSL
                    </h3>
                    <p>
                        Berdasarkan tingkat validasi dan kepercayaan, sertifikat
                        SSL dibagi menjadi tiga jenis utama:
                    </p>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                        <div
                            class="bg-gray-50 p-4 rounded-lg border border-gray-200"
                        >
                            <h4 class="font-semibold text-gray-700">
                                Domain Validation (DV)
                            </h4>
                            <ul
                                class="list-disc list-inside ml-2 text-sm space-y-1"
                            >
                                <li>Validasi paling dasar</li>
                                <li>Hanya memverifikasi kepemilikan domain</li>
                                <li>Proses cepat dan biaya rendah</li>
                                <li>Cocok untuk blog dan website pribadi</li>
                            </ul>
                        </div>
                        <div
                            class="bg-gray-50 p-4 rounded-lg border border-gray-200"
                        >
                            <h4 class="font-semibold text-gray-700">
                                Organization Validation (OV)
                            </h4>
                            <ul
                                class="list-disc list-inside ml-2 text-sm space-y-1"
                            >
                                <li>Validasi tingkat menengah</li>
                                <li>
                                    Verifikasi kepemilikan domain dan organisasi
                                </li>
                                <li>
                                    Informasi organisasi ditampilkan di
                                    sertifikat
                                </li>
                                <li>Cocok untuk bisnis dan e-commerce</li>
                            </ul>
                        </div>
                        <div
                            class="bg-gray-50 p-4 rounded-lg border border-gray-200"
                        >
                            <h4 class="font-semibold text-gray-700">
                                Extended Validation (EV)
                            </h4>
                            <ul
                                class="list-disc list-inside ml-2 text-sm space-y-1"
                            >
                                <li>Tingkat validasi tertinggi</li>
                                <li>Verifikasi sangat ketat dan mendalam</li>
                                <li>Nama organisasi ditampilkan di URL bar</li>
                                <li>Cocok untuk perbankan dan finansial</li>
                            </ul>
                        </div>
                    </div>
                    <p class="mt-3 text-sm text-gray-600">
                        Semakin tinggi tingkat validasi, semakin besar jaminan
                        identitas dan keamanan website kepada pengunjung, namun
                        juga semakin tinggi biaya dan prosesnya.
                    </p>
                </div>

                <div class="concept-explanation">
                    <h3 class="text-xl font-semibold mb-2">
                        Mengapa CA Penting?
                    </h3>
                    <p>
                        Tanpa CA, kita akan menghadapi masalah yang disebut
                        <strong>"Man-in-the-Middle Attack"</strong>:
                    </p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div
                            class="bg-red-50 p-4 rounded-lg border border-red-200"
                        >
                            <h4 class="font-semibold text-red-700">
                                Tanpa CA:
                            </h4>
                            <ol
                                class="list-decimal list-inside ml-2 text-sm space-y-1"
                            >
                                <li>Anda ingin mengunjungi bank.com</li>
                                <li>
                                    Penyerang mengalihkan koneksi Anda ke server
                                    palsu
                                </li>
                                <li>
                                    Server palsu mengirimkan kunci publik palsu
                                </li>
                                <li>
                                    Anda tidak punya cara untuk memverifikasi
                                    keaslian kunci
                                </li>
                                <li>
                                    Anda mengenkripsi data sensitif dengan kunci
                                    palsu
                                </li>
                                <li>Penyerang bisa membaca data Anda</li>
                            </ol>
                        </div>
                        <div
                            class="bg-green-50 p-4 rounded-lg border border-green-200"
                        >
                            <h4 class="font-semibold text-green-700">
                                Dengan CA:
                            </h4>
                            <ol
                                class="list-decimal list-inside ml-2 text-sm space-y-1"
                            >
                                <li>Anda ingin mengunjungi bank.com</li>
                                <li>
                                    Penyerang mengalihkan koneksi Anda ke server
                                    palsu
                                </li>
                                <li>
                                    Server palsu mengirimkan sertifikat palsu
                                </li>
                                <li>
                                    Browser Anda memeriksa sertifikat dan
                                    mendeteksi bahwa tidak ditandatangani oleh
                                    CA tepercaya
                                </li>
                                <li>Browser menampilkan peringatan keamanan</li>
                                <li>Data Anda tetap aman</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="concept-explanation">
                    <h3 class="text-xl font-semibold mb-2">
                        Hierarki Kepercayaan
                    </h3>
                    <p>CA bekerja dalam sistem hierarki kepercayaan:</p>
                    <ul class="list-disc list-inside ml-4 space-y-2">
                        <li>
                            <strong>Root CA:</strong> CA tingkat tertinggi,
                            sertifikatnya sudah terpasang di browser/sistem
                            operasi Anda.
                        </li>
                        <li>
                            <strong>Intermediate CA:</strong> CA tingkat
                            menengah yang disertifikasi oleh Root CA.
                        </li>
                        <li>
                            <strong>End-entity Certificate:</strong> Sertifikat
                            yang diterbitkan untuk website/server.
                        </li>
                    </ul>
                    <p class="mt-2">
                        Sistem ini memungkinkan verifikasi rantai kepercayaan
                        dari sertifikat website hingga ke Root CA yang
                        terpercaya.
                    </p>
                </div>

                <div class="concept-explanation">
                    <h3 class="text-xl font-semibold mb-2">
                        Peran Hashing dalam Keamanan Digital
                    </h3>
                    <p>
                        Hashing adalah proses mengubah data menjadi string
                        karakter tetap (hash) melalui fungsi matematika:
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div
                            class="bg-blue-50 p-4 rounded-lg border border-blue-200"
                        >
                            <h4 class="font-semibold text-blue-700">
                                Karakteristik Fungsi Hash:
                            </h4>
                            <ul
                                class="list-disc list-inside ml-2 text-sm space-y-1"
                            >
                                <li>
                                    Selalu menghasilkan output dengan panjang
                                    tetap
                                </li>
                                <li>
                                    Perubahan kecil pada input menghasilkan hash
                                    yang sangat berbeda
                                </li>
                                <li>
                                    Tidak dapat dikembalikan ke data asli
                                    (one-way function)
                                </li>
                                <li>
                                    Sulit menemukan dua input berbeda dengan
                                    hash sama
                                </li>
                            </ul>
                        </div>
                        <div
                            class="bg-blue-50 p-4 rounded-lg border border-blue-200"
                        >
                            <h4 class="font-semibold text-blue-700">
                                Penggunaan Hash:
                            </h4>
                            <ul
                                class="list-disc list-inside ml-2 text-sm space-y-1"
                            >
                                <li>
                                    Integritas data: memastikan data tidak
                                    berubah
                                </li>
                                <li>
                                    Tanda tangan digital: CA menggunakan hash
                                    untuk "menandatangani" sertifikat
                                </li>
                                <li>
                                    Penyimpanan password: menyimpan hash, bukan
                                    password asli
                                </li>
                                <li>
                                    Blockchain: menjaga integritas rantai
                                    transaksi
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-4 text-sm">
                        <p>
                            <strong>Contoh:</strong> Pesan "Halo" dengan SHA-256
                            menghasilkan hash:
                            <code
                                >f7ff9e8b7bb2e09b70935a5d785e0cc5d9d0abf0</code
                            >
                        </p>
                        <p>
                            Mengubah sedikit menjadi "Halo!" menghasilkan hash
                            yang sangat berbeda:
                            <code
                                >24853b57f3536afddb7f8d83851bf5d5e089c839</code
                            >
                        </p>
                    </div>
                </div>

                <div class="concept-explanation">
                    <h3 class="text-xl font-semibold mb-2">
                        TLS/SSL: Keamanan Komunikasi Web
                    </h3>
                    <p>
                        TLS (Transport Layer Security) dan pendahulunya SSL
                        (Secure Sockets Layer) adalah protokol yang mengamankan
                        komunikasi melalui internet:
                    </p>

                    <div
                        class="bg-green-50 p-4 rounded-lg border border-green-200 mt-4"
                    >
                        <h4 class="font-semibold text-green-700">
                            Cara Kerja TLS/SSL:
                        </h4>
                        <ol
                            class="list-decimal list-inside ml-2 text-sm space-y-2"
                        >
                            <li>
                                <strong>Handshake:</strong> Client dan server
                                bertukar informasi untuk membangun koneksi aman
                                <ul class="list-disc list-inside ml-6">
                                    <li>
                                        Client mengirim versi TLS dan cipher
                                        suite yang didukung
                                    </li>
                                    <li>
                                        Server memilih versi TLS dan cipher
                                        suite, mengirim sertifikat digitalnya
                                    </li>
                                    <li>
                                        Client memverifikasi sertifikat server
                                        melalui CA
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <strong>Pertukaran Kunci:</strong> Client dan
                                server menyepakati kunci sesi
                                <ul class="list-disc list-inside ml-6">
                                    <li>
                                        Menggunakan enkripsi asimetris untuk
                                        bertukar kunci simetris sementara
                                    </li>
                                    <li>
                                        Kunci sesi ini lebih efisien untuk
                                        komunikasi selanjutnya
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <strong>Komunikasi Terenkripsi:</strong> Semua
                                data ditransmisikan dengan aman
                                <ul class="list-disc list-inside ml-6">
                                    <li>
                                        Data dienkripsi dengan kunci sesi
                                        (enkripsi simetris)
                                    </li>
                                    <li>
                                        Integritas data diverifikasi dengan kode
                                        MAC (Message Authentication Code)
                                    </li>
                                </ul>
                            </li>
                        </ol>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div
                            class="bg-gray-50 p-4 rounded-lg border border-gray-200"
                        >
                            <h4 class="font-semibold text-gray-700">
                                Indikator TLS/SSL di Browser:
                            </h4>
                            <ul
                                class="list-disc list-inside ml-2 text-sm space-y-1"
                            >
                                <li>
                                    URL dimulai dengan "https://" (bukan
                                    "http://")
                                </li>
                                <li>Ikon gembok di address bar</li>
                                <li>
                                    Sertifikat dapat diperiksa dengan mengklik
                                    gembok
                                </li>
                            </ul>
                        </div>
                        <div
                            class="bg-gray-50 p-4 rounded-lg border border-gray-200"
                        >
                            <h4 class="font-semibold text-gray-700">
                                Evolusi TLS/SSL:
                            </h4>
                            <ul
                                class="list-disc list-inside ml-2 text-sm space-y-1"
                            >
                                <li>
                                    SSL 2.0, 3.0: Versi awal, sekarang tidak
                                    aman
                                </li>
                                <li>
                                    TLS 1.0, 1.1: Perbaikan dari SSL, namun
                                    sudah usang
                                </li>
                                <li>TLS 1.2: Masih digunakan secara luas</li>
                                <li>
                                    TLS 1.3: Versi terbaru, lebih cepat dan aman
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Simulasi TLS/SSL Handshake -->
            <section class="mt-12">
                <h2 class="text-2xl font-semibold text-gray-700 mb-3">
                    Simulasi TLS/SSL Handshake
                </h2>
                <p class="mb-4">
                    Lihat bagaimana TLS/SSL bekerja untuk mengamankan komunikasi
                    antara client (browser) dan server melalui proses handshake.
                </p>

                <div class="simulation-box">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="simulation-container">
                            <h3
                                class="text-lg font-medium mb-4 text-indigo-700"
                            >
                                Client (Browser)
                            </h3>
                            <div class="client-icon mb-4">🌐</div>

                            <div class="mb-4">
                                <label
                                    for="website-url"
                                    class="block text-sm font-medium text-gray-700 mb-1"
                                    >URL yang akan dikunjungi:</label
                                >
                                <div class="flex">
                                    <select
                                        id="protocol-selector"
                                        class="w-24 rounded-l-md border-r-0"
                                    >
                                        <option value="https">https://</option>
                                        <option value="http">http://</option>
                                    </select>
                                    <input
                                        type="text"
                                        id="website-url"
                                        value="example.com"
                                        placeholder="Masukkan URL..."
                                        class="rounded-r-md flex-1 focus:ring-2 focus:ring-indigo-200"
                                    />
                                </div>
                            </div>

                            <button
                                class="btn btn-primary mt-2 w-full"
                                onclick="startTLSHandshake()"
                            >
                                Mulai Koneksi
                            </button>

                            <div class="mt-4 p-3 border rounded-md bg-gray-50">
                                <h4 class="text-sm font-semibold mb-2">
                                    Status Koneksi:
                                </h4>
                                <div id="connection-status" class="text-sm">
                                    Belum terhubung
                                </div>
                            </div>
                        </div>

                        <div class="simulation-container">
                            <h3
                                class="text-lg font-medium mb-4 text-orange-700"
                            >
                                Certificate Authority (CA)
                            </h3>
                            <div class="ca-icon mb-4">🏛️</div>

                            <div class="mb-4">
                                <label
                                    class="block text-sm font-medium text-gray-700 mb-1"
                                    >Status Validasi:</label
                                >
                                <div
                                    id="ca-validation-status"
                                    class="p-3 bg-gray-100 rounded text-sm"
                                >
                                    Menunggu permintaan validasi...
                                </div>
                            </div>

                            <div class="mt-4">
                                <label
                                    class="block text-sm font-medium text-gray-700 mb-1"
                                    >Daftar CA Tepercaya:</label
                                >
                                <ul
                                    class="text-xs p-2 bg-gray-100 rounded list-disc list-inside"
                                >
                                    <li>DigiCert Global Root CA</li>
                                    <li>GlobalSign Root CA</li>
                                    <li>Let's Encrypt</li>
                                    <li>Comodo RSA CA</li>
                                    <li>Sectigo RSA CA</li>
                                </ul>
                            </div>
                        </div>

                        <div class="simulation-container">
                            <h3 class="text-lg font-medium mb-4 text-green-700">
                                Server
                            </h3>
                            <div class="server-icon mb-4">🖥️</div>

                            <div class="mb-4">
                                <label
                                    class="block text-sm font-medium text-gray-700 mb-1"
                                    >Sertifikat Server:</label
                                >
                                <div
                                    id="server-certificate"
                                    class="certificate text-xs"
                                >
                                    <p><strong>Certificate:</strong></p>
                                    <p>Version: 3</p>
                                    <p>Serial Number: ********</p>
                                    <p>
                                        Signature Algorithm:
                                        sha256WithRSAEncryption
                                    </p>
                                    <p>
                                        Issuer: CN=DigiCert Global Root CA,
                                        O=DigiCert Inc, C=US
                                    </p>
                                    <p>Valid From: Jan 1, 2023</p>
                                    <p>Valid To: Jan 1, 2024</p>
                                    <p>
                                        Subject: CN=example.com, O=Example Inc,
                                        C=US
                                    </p>
                                    <p>
                                        Public Key: [Kunci publik dalam format
                                        base64]
                                    </p>
                                </div>
                            </div>

                            <div class="mt-4 p-3 border rounded-md bg-gray-50">
                                <h4 class="text-sm font-semibold mb-2">
                                    Status Server:
                                </h4>
                                <div id="server-status" class="text-sm">
                                    Menunggu koneksi...
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="animation-container mt-6">
                        <div id="tls-data-packet" class="data-packet">
                            Paket
                        </div>
                        <div
                            style="position: absolute; bottom: 20px; left: 15px"
                            class="text-sm text-blue-600 flex items-center"
                        >
                            <div
                                class="client-icon inline-flex mr-1"
                                style="
                                    background-color: #bfdbfe;
                                    width: 30px;
                                    height: 30px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    border-radius: 50%;
                                "
                            >
                                👤
                            </div>
                            <span style="font-weight: 500">Client</span>
                        </div>
                        <div
                            style="
                                position: absolute;
                                bottom: 20px;
                                right: 15px;
                            "
                            class="text-sm text-green-600 flex items-center"
                        >
                            <div
                                class="server-icon inline-flex mr-1"
                                style="
                                    background-color: #a7f3d0;
                                    width: 30px;
                                    height: 30px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    border-radius: 50%;
                                "
                            >
                                🖥️
                            </div>
                            <span style="font-weight: 500">Server</span>
                        </div>
                        <div
                            style="
                                position: absolute;
                                top: 20px;
                                left: 50%;
                                transform: translateX(-50%);
                            "
                            class="text-sm text-orange-600 flex items-center justify-center"
                        >
                            <div
                                class="ca-icon inline-flex mr-1"
                                style="
                                    background-color: #fed7aa;
                                    width: 30px;
                                    height: 30px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    border-radius: 50%;
                                "
                            >
                                🏛️
                            </div>
                            <span style="font-weight: 500">CA</span>
                        </div>
                    </div>

                    <div
                        id="tls-handshake-log"
                        class="mt-4 p-4 bg-black text-green-400 font-mono text-xs rounded-md h-40 overflow-y-auto"
                    >
                        <div>
                            # Log Handshake TLS/SSL akan muncul di sini...
                        </div>
                    </div>

                    <div
                        class="mt-4 text-sm text-gray-600 bg-blue-50 p-4 rounded-lg"
                    >
                        <p>
                            <strong>Catatan:</strong> Simulasi ini menunjukkan
                            proses TLS Handshake yang terjadi setiap kali Anda
                            mengakses website dengan HTTPS. Handshake ini
                            memastikan koneksi aman sebelum data sensitif
                            dikirimkan. Certificate Authority (CA) berperan
                            penting dalam memverifikasi keaslian sertifikat
                            server.
                        </p>
                    </div>
                </div>
            </section>

            <footer class="text-center mt-12 py-6 border-t border-gray-300">
                <p class="text-sm text-gray-600">
                    &copy; 2024 Simulasi Keamanan IT. Dibuat untuk tujuan
                    edukasi.
                </p>
            </footer>
        </div>

        <script>
            // Variabel untuk simulasi TLS/SSL
            const tlsHandshakeLogEl =
                document.getElementById("tls-handshake-log");
            const connectionStatusEl =
                document.getElementById("connection-status");
            const serverStatusEl = document.getElementById("server-status");
            const tlsDataPacketEl = document.getElementById("tls-data-packet");
            const protocolSelectorEl =
                document.getElementById("protocol-selector");
            const websiteUrlEl = document.getElementById("website-url");
            const serverCertificateEl =
                document.getElementById("server-certificate");
            let caValidationStatusEl;

            // Buat referensi ke elemen CA setelah DOM selesai dimuat
            document.addEventListener("DOMContentLoaded", function () {
                caValidationStatusEl = document.getElementById(
                    "ca-validation-status",
                );
            });

            // Fungsi untuk simulasi TLS Handshake
            function startTLSHandshake() {
                // Reset status
                tlsHandshakeLogEl.innerHTML = "";
                tlsDataPacketEl.style.transition = "none";
                tlsDataPacketEl.style.left = "0";
                tlsDataPacketEl.style.top = "110px"; // Reset posisi vertikal lebih rendah
                tlsDataPacketEl.textContent = "Paket";
                tlsDataPacketEl.classList.remove("encrypted-packet");

                // Reset status CA
                if (caValidationStatusEl) {
                    caValidationStatusEl.innerHTML =
                        "Menunggu permintaan validasi...";
                    caValidationStatusEl.className =
                        "p-3 bg-gray-100 rounded text-sm";
                }

                // Force reflow
                void tlsDataPacketEl.offsetWidth;

                // Aktifkan transisi
                tlsDataPacketEl.style.transition = "all 2s ease"; // Diperlambat menjadi 2 detik

                const protocol = protocolSelectorEl.value;
                const url = websiteUrlEl.value;

                // Update sertifikat server dengan domain yang dimasukkan
                const certificateSubject = document.querySelector(
                    "#server-certificate p:nth-child(8)",
                );
                certificateSubject.textContent = `Subject: CN=${url}, O=Example Inc, C=US`;

                // Jika protokol HTTP, tampilkan peringatan tidak aman
                if (protocol === "http") {
                    addTLSLog("🔴 Menggunakan HTTP (tidak aman)!");
                    addTLSLog(
                        "🔴 Koneksi tidak dienkripsi dan rentan terhadap serangan!",
                    );
                    connectionStatusEl.innerHTML =
                        '<span class="text-red-500 font-semibold">⚠️ TIDAK AMAN: Koneksi HTTP tanpa enkripsi</span>';
                    serverStatusEl.innerHTML =
                        '<span class="text-red-500 font-semibold">⚠️ Terhubung tanpa enkripsi</span>';
                    if (caValidationStatusEl) {
                        caValidationStatusEl.innerHTML =
                            '<span class="text-gray-500">Tidak ada verifikasi CA (HTTP)</span>';
                    }
                    return;
                }

                // Simulasi proses TLS Handshake
                connectionStatusEl.textContent = "Memulai koneksi...";
                serverStatusEl.textContent = "Menerima permintaan koneksi...";

                // Step 1: Client Hello
                setTimeout(() => {
                    addTLSLog("1️⃣ Client Hello");
                    addTLSLog("   ↳ Versi TLS yang didukung: TLS 1.2, TLS 1.3");
                    addTLSLog("   ↳ Cipher suites yang didukung");
                    addTLSLog("   ↳ Random data untuk pengamanan");

                    animatePacket("🔓 Hello", "client-to-server");

                    // Step 2: Server Hello & Certificate
                    setTimeout(() => {
                        addTLSLog("2️⃣ Server Hello");
                        addTLSLog("   ↳ Memilih TLS 1.3");
                        addTLSLog("   ↳ Memilih ECDHE-RSA-AES256-GCM-SHA384");
                        addTLSLog("   ↳ Mengirim sertifikat digital server");

                        animatePacket("🔐 Cert", "server-to-client");

                        // Step 3: Certificate Validation with CA
                        setTimeout(() => {
                            addTLSLog("3️⃣ Verifikasi Sertifikat dengan CA");
                            addTLSLog(
                                "   ↳ Client mengirim sertifikat ke CA untuk validasi",
                            );

                            if (caValidationStatusEl) {
                                caValidationStatusEl.innerHTML =
                                    '<span class="text-blue-500">Memverifikasi sertifikat...</span>';
                            }

                            // Animasi ke CA
                            animatePacket("📄 Cert", "client-to-ca");

                            setTimeout(() => {
                                addTLSLog(
                                    "   ↳ CA memeriksa tanda tangan digital",
                                );
                                addTLSLog(
                                    "   ↳ CA memverifikasi masa berlaku sertifikat",
                                );
                                addTLSLog("   ↳ CA memvalidasi domain name");

                                if (caValidationStatusEl) {
                                    caValidationStatusEl.innerHTML =
                                        '<span class="text-green-500 font-semibold">✅ Sertifikat valid dan tepercaya</span>';
                                }

                                // Animasi CA ke Client dengan hasil verifikasi
                                animatePacket("✓ Valid", "ca-to-client");

                                // Step 4: Key Exchange
                                setTimeout(() => {
                                    addTLSLog("4️⃣ Pertukaran Kunci");
                                    addTLSLog(
                                        "   ↳ Client membuat kunci sesi sementara",
                                    );
                                    addTLSLog(
                                        "   ↳ Mengenkripsi dengan kunci publik server",
                                    );

                                    // Animasi Client to Server dengan kunci
                                    animatePacket("🔑 Key", "client-to-server");

                                    // Step 5: Server mengirim konfirmasi
                                    setTimeout(() => {
                                        addTLSLog(
                                            "5️⃣ Server mengirim konfirmasi",
                                        );
                                        animatePacket(
                                            "✅ Confirm",
                                            "server-to-client",
                                        );

                                        // Step 6: Finished
                                        setTimeout(() => {
                                            addTLSLog("6️⃣ Handshake Selesai");
                                            addTLSLog(
                                                "   ↳ Koneksi terenkripsi berhasil dibuat",
                                            );

                                            connectionStatusEl.innerHTML =
                                                '<span class="text-green-500 font-semibold">✅ AMAN: Koneksi HTTPS terenkripsi</span>';
                                            serverStatusEl.innerHTML =
                                                '<span class="text-green-500 font-semibold">✅ Koneksi terenkripsi aktif</span>';

                                            // Komunikasi terenkripsi
                                            setTimeout(() => {
                                                addTLSLog(
                                                    "7️⃣ Pertukaran Data Terenkripsi",
                                                );
                                                animatePacket(
                                                    "🔒 Data",
                                                    "encrypted-communication",
                                                );
                                            }, 2500); // Diperlambat
                                        }, 2500); // Diperlambat
                                    }, 2500); // Diperlambat
                                }, 2000); // Diperlambat
                            }, 2000); // Diperlambat
                        }, 2000); // Diperlambat
                    }, 2000); // Diperlambat
                }, 1000); // Diperlambat
            }

            function addTLSLog(message) {
                const logLine = document.createElement("div");
                logLine.textContent = message;
                tlsHandshakeLogEl.appendChild(logLine);
                tlsHandshakeLogEl.scrollTop = tlsHandshakeLogEl.scrollHeight;
            }

            function animatePacket(label, direction) {
                tlsDataPacketEl.textContent = label;
                tlsDataPacketEl.style.transition = "all 2s ease"; // Pastikan transisi konsisten

                if (direction === "client-to-server") {
                    tlsDataPacketEl.style.left = "5%";
                    tlsDataPacketEl.style.top = "110px";
                    setTimeout(() => {
                        tlsDataPacketEl.style.left = "95%";
                    }, 500); // Diperlambat
                } else if (direction === "server-to-client") {
                    tlsDataPacketEl.style.left = "95%";
                    tlsDataPacketEl.style.top = "110px";
                    setTimeout(() => {
                        tlsDataPacketEl.style.left = "5%";
                    }, 500); // Diperlambat
                } else if (direction === "client-to-ca") {
                    tlsDataPacketEl.style.left = "5%";
                    tlsDataPacketEl.style.top = "110px";
                    setTimeout(() => {
                        tlsDataPacketEl.style.top = "35px";
                        tlsDataPacketEl.style.left = "50%";
                    }, 500); // Diperlambat
                } else if (direction === "ca-to-client") {
                    tlsDataPacketEl.style.top = "35px";
                    tlsDataPacketEl.style.left = "50%";
                    setTimeout(() => {
                        tlsDataPacketEl.style.top = "110px";
                        tlsDataPacketEl.style.left = "5%";
                    }, 500); // Diperlambat
                } else if (direction === "encrypted-communication") {
                    tlsDataPacketEl.classList.add("encrypted-packet");
                    // Animate back and forth to simulate continuous communication
                    tlsDataPacketEl.style.left = "5%";
                    tlsDataPacketEl.style.top = "110px";
                    setTimeout(() => {
                        tlsDataPacketEl.style.left = "95%";
                        setTimeout(() => {
                            tlsDataPacketEl.style.left = "5%";
                        }, 2000); // Diperlambat
                    }, 500); // Diperlambat
                }
            }

            // Fungsi enkripsi/dekripsi sederhana untuk simulasi enkripsi simetris
            function simpleSymmetricEncrypt(text, key) {
                if (!text || !key) return "";
                let result = "";
                for (let i = 0; i < text.length; i++) {
                    // XOR karakter dengan karakter kunci (berulang jika perlu)
                    const keyChar = key.charCodeAt(i % key.length);
                    const charCode = text.charCodeAt(i) ^ keyChar;
                    result += String.fromCharCode(charCode);
                }
                return btoa(result); // Base64 encode
            }

            function simpleSymmetricDecrypt(cipher, key) {
                if (!cipher || !key) return "";
                try {
                    const decodedCipher = atob(cipher); // Base64 decode
                    let result = "";
                    for (let i = 0; i < decodedCipher.length; i++) {
                        // XOR karakter dengan karakter kunci (berulang jika perlu)
                        const keyChar = key.charCodeAt(i % key.length);
                        const charCode = decodedCipher.charCodeAt(i) ^ keyChar;
                        result += String.fromCharCode(charCode);
                    }
                    return result;
                } catch (e) {
                    return "Error: Gagal mendekripsi (format tidak valid atau kunci salah).";
                }
            }

            // Elemen-elemen untuk simulasi enkripsi simetris
            const symmetricKeyEl = document.getElementById("symmetric-key");
            const symmetricKeyReceiverEl = document.getElementById(
                "symmetric-key-receiver",
            );
            const plainTextSymmetricEl = document.getElementById(
                "plain-text-symmetric",
            );
            const encryptedTextSymmetricEl = document.getElementById(
                "encrypted-text-symmetric",
            );
            const decryptedTextSymmetricEl = document.getElementById(
                "decrypted-text-symmetric",
            );
            const symmetricMessageEl =
                document.getElementById("symmetric-message");
            const dataPacketEl = document.getElementById("data-packet");

            function showMessage(element, message, type) {
                element.textContent = message;
                element.className = "message-box"; // Reset classes
                if (type === "success") {
                    element.classList.add("message-success");
                } else if (type === "error") {
                    element.classList.add("message-error");
                } else {
                    element.classList.add("message-info");
                }
                element.style.display = "block";
            }

            function encryptSymmetric() {
                const plainText = plainTextSymmetricEl.value;
                const key = symmetricKeyEl.value;

                if (!plainText) {
                    showMessage(
                        symmetricMessageEl,
                        "Pesan asli tidak boleh kosong!",
                        "error",
                    );
                    return;
                }

                if (!key) {
                    showMessage(
                        symmetricMessageEl,
                        "Kunci rahasia tidak boleh kosong!",
                        "error",
                    );
                    return;
                }

                const encrypted = simpleSymmetricEncrypt(plainText, key);
                encryptedTextSymmetricEl.value = encrypted;

                // Reset animasi pengiriman data
                dataPacketEl.style.transition = "none"; // Matikan transisi untuk reset instan
                dataPacketEl.textContent = "Data";
                dataPacketEl.classList.remove("encrypted-packet");
                dataPacketEl.style.left = "0";

                // Force reflow untuk memastikan reset benar-benar terjadi
                void dataPacketEl.offsetWidth;

                // Aktifkan kembali transisi
                dataPacketEl.style.transition = "all 1.5s ease";

                setTimeout(() => {
                    dataPacketEl.textContent = "🔒";
                    dataPacketEl.classList.add("encrypted-packet");
                    dataPacketEl.style.left = "50%";
                }, 100);

                setTimeout(() => {
                    dataPacketEl.style.left = "100%";
                }, 1600);

                showMessage(
                    symmetricMessageEl,
                    "Pesan berhasil dienkripsi dengan kunci simetris.",
                    "success",
                );
            }

            function decryptSymmetric() {
                const encryptedText = encryptedTextSymmetricEl.value;
                const key = symmetricKeyReceiverEl.value;

                if (!encryptedText) {
                    showMessage(
                        symmetricMessageEl,
                        "Tidak ada pesan terenkripsi untuk didekripsi.",
                        "error",
                    );
                    return;
                }

                if (!key) {
                    showMessage(
                        symmetricMessageEl,
                        "Kunci rahasia tidak boleh kosong!",
                        "error",
                    );
                    return;
                }

                const decrypted = simpleSymmetricDecrypt(encryptedText, key);
                decryptedTextSymmetricEl.value = decrypted;

                if (decrypted.startsWith("Error:")) {
                    showMessage(symmetricMessageEl, decrypted, "error");
                } else {
                    showMessage(
                        symmetricMessageEl,
                        "Pesan berhasil didekripsi dengan kunci simetris.",
                        "success",
                    );
                }
            }

            // Fungsi enkripsi/dekripsi sederhana untuk simulasi enkripsi asimetris
            function pseudoAsymmetricEncrypt(text, publicKey) {
                if (!text || !publicKey) return "";
                // Simulasi sederhana enkripsi asimetris (bukan implementasi nyata)
                let result = "";
                for (let i = 0; i < text.length; i++) {
                    result += String.fromCharCode(
                        text.charCodeAt(i) + (publicKey.length % 10),
                    );
                }
                return btoa(result); // Base64 encode
            }

            function pseudoAsymmetricDecrypt(cipher, privateKey) {
                if (!cipher || !privateKey) return "";
                try {
                    const decodedCipher = atob(cipher); // Base64 decode
                    // Simulasi sederhana dekripsi asimetris (bukan implementasi nyata)
                    let result = "";
                    for (let i = 0; i < decodedCipher.length; i++) {
                        result += String.fromCharCode(
                            decodedCipher.charCodeAt(i) -
                                (privateKey.length % 10),
                        );
                    }
                    return result;
                } catch (e) {
                    return "Error: Gagal mendekripsi (format tidak valid atau kunci salah).";
                }
            }

            // Elemen-elemen untuk simulasi enkripsi asimetris
            const plainTextAEl = document.getElementById("plain-text-a");
            const publicKeyBEl = document.getElementById("public-key-b");
            const encryptedTextAEl =
                document.getElementById("encrypted-text-a");
            const privateKeyBEl = document.getElementById("private-key-b");
            const decryptedTextBEl =
                document.getElementById("decrypted-text-b");
            const asymmetricMessageEl =
                document.getElementById("asymmetric-message");
            const asymmetricDataPacketEl = document.getElementById(
                "asymmetric-data-packet",
            );

            function encryptMessageA() {
                const plainText = plainTextAEl.value;
                const publicKey = publicKeyBEl.textContent;

                if (!plainText) {
                    showMessage(
                        asymmetricMessageEl,
                        "Pesan asli tidak boleh kosong!",
                        "error",
                    );
                    return;
                }

                const encrypted = pseudoAsymmetricEncrypt(plainText, publicKey);
                encryptedTextAEl.value = encrypted;

                // Reset dan animasi pengiriman data
                asymmetricDataPacketEl.style.transition = "none";
                asymmetricDataPacketEl.textContent = "Data";
                asymmetricDataPacketEl.classList.remove("encrypted-packet");
                asymmetricDataPacketEl.style.left = "0";

                // Force reflow
                void asymmetricDataPacketEl.offsetWidth;

                // Aktifkan kembali transisi
                asymmetricDataPacketEl.style.transition = "all 1.5s ease";

                setTimeout(() => {
                    asymmetricDataPacketEl.textContent = "🔒";
                    asymmetricDataPacketEl.classList.add("encrypted-packet");
                    asymmetricDataPacketEl.style.left = "50%";
                }, 100);

                setTimeout(() => {
                    asymmetricDataPacketEl.style.left = "100%";
                }, 1600);

                showMessage(
                    asymmetricMessageEl,
                    "Pesan berhasil dienkripsi dengan kunci publik Pihak B.",
                    "success",
                );
            }

            function decryptMessageB() {
                const encryptedText = encryptedTextAEl.value;
                const privateKey = privateKeyBEl.textContent;

                if (!encryptedText) {
                    showMessage(
                        asymmetricMessageEl,
                        "Tidak ada pesan terenkripsi untuk didekripsi.",
                        "error",
                    );
                    return;
                }

                const decrypted = pseudoAsymmetricDecrypt(
                    encryptedText,
                    privateKey,
                );
                decryptedTextBEl.value = decrypted;

                if (decrypted.startsWith("Error:")) {
                    showMessage(asymmetricMessageEl, decrypted, "error");
                } else {
                    showMessage(
                        asymmetricMessageEl,
                        "Pesan berhasil didekripsi dengan kunci privat Pihak B.",
                        "success",
                    );
                }
            }
        </script>
    </body>
</html>
