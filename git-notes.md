# Git Best Practices untuk Pengembangan per Sesi Mini CS

Pertanyaan Anda tentang Git best practices untuk pengembangan aplikasi per sesi dalam kelas mini CS sangat relevan. Mari saya pelajari terlebih dahulu struktur repositori Anda untuk memberikan rekomendasi yang sesuai.

**Use Tool: list_directory (toolu_0114ppcsyod9domSWQK1EMTY)**
```json
{
  "path": "minics"
}
```

**Tool Results: toolu_0114ppcsyod9domSWQK1EMTY**

minics/.gitignore
minics/1-intro-to-programming
minics/2-algorithm-and-data-structures
minics/3-database
minics/4-operating-system-and-modern-architecture
minics/5-computer-networks-and-cybersecurity
minics/sandbox

## Agent

Terima kasih atas informasinya. Saya melihat struktur repositori Anda memiliki folder untuk setiap sesi (1 hingga 5) plus folder sandbox. Sekarang saya akan melihat bagaimana struktur Git saat ini dengan memeriksa keberadaan file konfigurasi Git.

**Use Tool: read_file (toolu_017H1Yqs1ZYHqM9dw13aZN2P)**
```json
{
  "path": "minics/.gitignore"
}
```

**Tool Results: toolu_017H1Yqs1ZYHqM9dw13aZN2P**

# Dependencies
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log
.pnp/
.pnp.js

# Build outputs
dist/
build/
out/
.next/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific files
.DS_Store
Thumbs.db

sandbox/

## Agent

Setelah memeriksa struktur proyek dan .gitignore, berikut adalah rekomendasi best practices Git untuk pengembangan per sesi dalam kelas mini CS:

## Git Best Practices untuk Project Mini CS

### 1. Gunakan Branch untuk Setiap Sesi

```
# Membuat dan pindah ke branch baru untuk sesi tertentu
git checkout -b sesi-3-feature
```

Untuk setiap sesi, buatlah branch khusus. Ini memungkinkan Anda untuk:
- Mengerjakan fitur sesi tertentu tanpa mempengaruhi sesi lainnya
- Melakukan eksperimen tanpa merusak kode utama
- Membuat Pull Request terstruktur saat menggabungkan perubahan

### 2. Struktur Commit yang Jelas

```
# Contoh commit yang baik
git commit -m "Sesi 3: Implementasi query database untuk fitur pencarian"
```

- Awali pesan commit dengan nomor sesi untuk memudahkan penelusuran
- Tulis pesan commit yang deskriptif tentang apa yang diubah
- Lakukan commit sering tapi logis (setiap selesai satu unit fungsionalitas)

### 3. Gunakan Git Tags untuk Menandai Milestone

```
# Menandai versi tertentu dari sesi
git tag -a "sesi-3-v1.0" -m "Versi final sesi 3 Database"
```

Tags memudahkan untuk kembali ke versi tertentu dari setiap sesi, misalnya saat demo atau saat perlu merujuk ke kode lama.

### 4. Pendekatan Branch Management

Beberapa opsi pendekatan:

#### A. Branch per Sesi (Sederhana)

```
main
├── sesi-1
├── sesi-2
└── sesi-3
```

Ini adalah pendekatan yang paling sederhana, di mana setiap sesi memiliki branch sendiri yang di-merge ke main saat selesai.

#### B. Git Flow (Lebih Terstruktur)

```
main (produksi stabil)
├── develop (integrasi)
│   ├── feature/sesi-1/auth
│   ├── feature/sesi-2/algoritma-sort
│   └── feature/sesi-3/database-query
└── hotfix/* (perbaikan darurat)
```

Pendekatan ini lebih terstruktur dan cocok untuk tim yang lebih besar.

### 5. Memanfaatkan .gitignore dengan Efektif

File .gitignore Anda sudah cukup lengkap. Tapi jika ada file konfigurasi atau output spesifik untuk sesi tertentu, Anda bisa menambahkan pola khusus:

```
# Contoh pengecualian spesifik sesi
sesi-3/node_modules/
sesi-4/logs/
*.temp
```

### 6. Dokumentasikan Perubahan di README.md

```
# Pada folder sesi
git add README.md
git commit -m "Sesi 3: Update dokumentasi setup database"
```

Untuk setiap sesi, pastikan README.md memiliki:
- Deskripsi topik
- Langkah setup
- Tantangan/tugas
- Referensi tambahan

### 7. Pertimbangkan Git Submodules (Opsional)

Jika ada perpustakaan atau kode yang digunakan di beberapa sesi, pertimbangkan untuk menggunakan submodules:

```
git submodule add https://github.com/user/shared-library.git shared
```

### 8. Gunakan Git LFS untuk File Besar (Jika Diperlukan)

Jika ada aset besar seperti dataset atau gambar, gunakan Git LFS:

```
git lfs track "*.psd"
git lfs track "data/*.csv"
```

## Contoh Alur Kerja Saat Mengembangkan Sesi Baru

1. Mulai dari branch terbaru:
   ```
   git checkout main
   git pull
   ```

2. Buat branch untuk sesi baru:
   ```
   git checkout -b sesi-3-database
   ```

3. Kembangkan fitur, lakukan commit secara reguler:
   ```
   git add .
   git commit -m "Sesi 3: Tambah konfigurasi koneksi database"
   ```

4. Saat selesai, merge kembali ke main:
   ```
   git checkout main
   git merge sesi-3-database
   git push origin main
   ```

5. Beri tag untuk milestone penting:
   ```
   git tag -a "sesi-3-final" -m "Versi final materi Database"
   git push origin --tags
   ```
